#!/usr/bin/env python3
"""
Test Cleric Cleanse ability.
"""

import pygame
pygame.init()

from game_state import Game
from units.cleric import Cleric
from units.warrior import Warrior

def test_cleanse():
    print("🧪 TESTING CLERIC CLEANSE")
    print("=" * 30)
    
    game = Game()
    cleric = Cleric(1)
    target = Warrior(1)  # Same team for cleanse
    
    game.board.add_unit(cleric, 4, 4)
    game.board.add_unit(target, 4, 5)
    
    # Apply a negative status effect
    target.apply_status('Stunned', 2)
    
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Target stunned before: {target.has_status('Stunned')}")
    
    success = cleric.use_ability(4, target.position, game)
    
    print(f"Cleanse success: {success}")
    print(f"Target stunned after: {target.has_status('Stunned')}")

if __name__ == "__main__":
    test_cleanse()

import pygame
from units_core import Unit, SimpleAbility, MoveAbility, AttackAbility
from game_config import GAME_CONFIG
import game_constants as const
import math

class Warlock(Unit):
    """
    Warlock unit class - Dark magic specialist with life-drain and curse abilities.
    
    Warlocks are dark magic users who excel at damage over time effects, life stealing,
    and battlefield control through curses and fear effects. They sacrifice health for power.
    
    Movement:
        - Pattern: Orthogonal only (N, S, E, W) like Cleric
        - Range: 2 tiles in chosen direction
        - Cannot jump over entities
        
    Abilities (8 total):
        0. Move - Orthogonal movement (2 tiles)
        1. Basic Attack - Weak ranged dark bolt (3 tile range)
        2. Life Drain - Steal health from target
        3. Curse - Target takes extra damage for 3 turns
        4. Fear - Force target to move away from Warlock
        5. Dark Pact - Sacrifice health for extra AP
        6. Soul Burn - Damage over time effect
        7. Shadow Bolt - High damage single target
        
    Default Passive:
        - Dark Regeneration: Heal 1 HP when enemy dies nearby
        
    Tactical Role:
        - Damage over time specialist
        - Life manipulation
        - Battlefield control through debuffs
    """
    def __init__(self, player_id):
        super().__init__(player_id, health=GAME_CONFIG.get("warlock_config", {}).get("health", 6), max_health=GAME_CONFIG.get("warlock_config", {}).get("health", 6))
        self.name = "Warlock"
        self.max_ap = GAME_CONFIG.get("warlock_config", {}).get("max_ap", 7)
        self.current_ap = GAME_CONFIG.get("warlock_config", {}).get("max_ap", 7)
        self.board = None
        
        # Warlock-specific state
        self.dark_pact_used_this_turn = False
        self.soul_burn_targets = {}  # Track targets with soul burn effect
        
        # Load image (dark purple/black theme)
        self.image = self._create_placeholder_image((80, 0, 80) if player_id == 1 else (120, 0, 120))
        
        # Abilities
        self.abilities = [
            MoveAbility(self),
            AttackAbility(self),
            SimpleAbility("Life Drain", GAME_CONFIG.get("warlock_config", {}).get("life_drain_ap_cost", 3), "Steal 2 HP from target", cooldown=2, owner=self),
            SimpleAbility("Curse", GAME_CONFIG.get("warlock_config", {}).get("curse_ap_cost", 2), "Target takes +1 damage for 3 turns", cooldown=3, owner=self),
            SimpleAbility("Fear", GAME_CONFIG.get("warlock_config", {}).get("fear_ap_cost", 3), "Force target to move away", cooldown=2, owner=self),
            SimpleAbility("Dark Pact", GAME_CONFIG.get("warlock_config", {}).get("dark_pact_ap_cost", 0), "Sacrifice 2 HP for 3 AP", cooldown=4, owner=self),
            SimpleAbility("Soul Burn", GAME_CONFIG.get("warlock_config", {}).get("soul_burn_ap_cost", 3), "Target takes 1 damage for 3 turns", cooldown=3, owner=self),
            SimpleAbility("Shadow Bolt", GAME_CONFIG.get("warlock_config", {}).get("shadow_bolt_ap_cost", 4), "High damage dark magic", cooldown=2, owner=self)
        ]
        
        # Apply configuration from balance system
        try:
            from config_loader import apply_config_to_unit
            apply_config_to_unit(self)
        except ImportError:
            pass  # Config loader not available
    
    def _create_placeholder_image(self, color):
        """Create a placeholder image with dark magic symbols"""
        surf = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA)
        
        # Main circle (dark theme)
        pygame.draw.circle(surf, color, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2)
        pygame.draw.circle(surf, (200, 200, 200), (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2, 2)
        
        # Dark magic symbol (pentagram-like)
        center_x, center_y = const.CELL_SIZE//2, const.CELL_SIZE//2
        symbol_color = (150, 0, 150)
        
        # Draw mystical symbol
        pygame.draw.circle(surf, symbol_color, (center_x, center_y), 8, 2)
        pygame.draw.line(surf, symbol_color, (center_x-6, center_y-6), (center_x+6, center_y+6), 2)
        pygame.draw.line(surf, symbol_color, (center_x-6, center_y+6), (center_x+6, center_y-6), 2)
        
        return surf
    
    def get_valid_moves(self, board):
        """Warlock moves 2 tiles in orthogonal directions only (N, S, E, W)"""
        self.board = board
        # Check if unit can move (status effects)
        if hasattr(self, 'status_manager') and not self.status_manager.can_move():
            return []
        if self.immobilized or self.stunned:
            return []
        
        valid_moves = []
        row, col = self.position
        
        # Orthogonal directions: North, South, East, West
        orthogonal_directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]
        
        for dr, dc in orthogonal_directions:
            # Move up to 2 tiles in each direction
            for distance in range(1, 3):  # 1 and 2 tiles
                new_r, new_c = row + dr * distance, col + dc * distance
                
                # Check if position is on board
                if 0 <= new_r < const.BOARD_SIZE and 0 <= new_c < const.BOARD_SIZE:
                    # Check if position is empty
                    if (new_r, new_c) not in board.units:
                        valid_moves.append((new_r, new_c))
                    else:
                        # Can't move through units, stop checking further in this direction
                        break
                else:
                    # Off board, stop checking further in this direction
                    break
        
        return valid_moves
    
    def get_valid_attacks(self, board):
        """Warlock basic attack: ranged dark bolt (3 tile range, orthogonal)"""
        self.board = board
        if self.stunned:
            return []
        
        valid_attacks = []
        row, col = self.position
        
        # Orthogonal directions with 3 tile range
        orthogonal_directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]
        
        for dr, dc in orthogonal_directions:
            for distance in range(1, 4):  # 1, 2, 3 tiles
                r, c = row + dr * distance, col + dc * distance
                
                # Check if position is on board
                if 0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE:
                    # Check if there's a unit (friendly fire enabled)
                    if (r, c) in board.units and not board.units[(r, c)].sanctuary:
                        valid_attacks.append((r, c))
                        break  # Stop at first unit hit
                else:
                    break  # Off board
        
        return valid_attacks
    
    def get_ability_targets(self, ability_idx, board):
        """Get valid targets for Warlock abilities"""
        self.board = board
        if ability_idx == 0:
            return self.get_valid_moves(board)
        if ability_idx == 1:
            return self.get_valid_attacks(board)
        
        ability_name = self.abilities[ability_idx].name
        
        if ability_name in ["Life Drain", "Curse", "Fear", "Soul Burn", "Shadow Bolt"]:
            # Ranged abilities targeting enemies (3 tile range)
            return self._get_ranged_targets(board, range_limit=3)
        elif ability_name == "Dark Pact":
            # Self-target ability
            return [self.position]
        
        return []
    
    def _get_ranged_targets(self, board, range_limit=3):
        """Get targets within range for ranged abilities"""
        targets = []
        row, col = self.position
        
        # Check all positions within range
        for r in range(max(0, row - range_limit), min(const.BOARD_SIZE, row + range_limit + 1)):
            for c in range(max(0, col - range_limit), min(const.BOARD_SIZE, col + range_limit + 1)):
                if (r, c) == self.position:
                    continue
                
                # Calculate distance
                distance = max(abs(r - row), abs(c - col))
                if distance <= range_limit and (r, c) in board.units:
                    target_unit = board.units[(r, c)]
                    if not target_unit.sanctuary:  # Can target any unit (friendly fire)
                        targets.append((r, c))
        
        return targets
    
    def use_ability(self, ability_idx, target_pos, game=None):
        """Use Warlock ability with global AP system"""
        if game:
            self.board = game.board
        elif not self.board:
            print(f"ERROR in {self.name}.use_ability: game object not passed and self.board not set.")
            if ability_idx > 1:
                print(f"Cannot use special ability {self.abilities[ability_idx].name} without board context.")
                return False
        
        # Standard move or attack - call super() to use the updated base class logic
        if ability_idx == 0:  # Move
            return super().use_ability(ability_idx, target_pos, game)
        
        if ability_idx == 1:  # Basic Attack
            return super().use_ability(ability_idx, target_pos, game)
        
        # For other Warlock-specific abilities - use global AP system
        return super().use_ability(ability_idx, target_pos, game)
    
    def _execute_warlock_ability(self, ability, target_pos, game):
        """Execute Warlock-specific abilities"""
        ability_name = ability.name
        
        if ability_name == "Life Drain":
            return self._use_life_drain(ability, target_pos)
        elif ability_name == "Curse":
            return self._use_curse(ability, target_pos)
        elif ability_name == "Fear":
            return self._use_fear(ability, target_pos, game)
        elif ability_name == "Dark Pact":
            return self._use_dark_pact(ability, game)
        elif ability_name == "Soul Burn":
            return self._use_soul_burn(ability, target_pos)
        elif ability_name == "Shadow Bolt":
            return self._use_shadow_bolt(ability, target_pos)
        else:
            print(f"Unknown Warlock ability: {ability_name}")
            return False
    
    def _use_life_drain(self, ability, target_pos):
        """Drain life from target and heal self"""
        target_unit = self.board.units.get(target_pos)
        if target_unit:
            damage = ability.damage if ability.damage > 0 else 2
            target_unit.take_damage(damage, self)
            
            # Heal self for half the damage dealt
            heal_amount = damage // 2 + 1
            old_health = self.health
            self.health = min(self.max_health, self.health + heal_amount)
            actual_heal = self.health - old_health
            
            print(f"{self.name} drains {damage} HP from {target_unit.name} and heals for {actual_heal} HP")
            return True
        return False
    
    def _use_curse(self, ability, target_pos):
        """Apply curse effect to target"""
        target_unit = self.board.units.get(target_pos)
        if target_unit:
            # Apply curse effect (target takes +1 damage for 3 turns)
            if hasattr(target_unit, 'status_manager'):
                # Use new status system if available
                target_unit.status_manager.apply_effect("cursed", 3, self.player_id)
            else:
                # Legacy system
                target_unit.cursed = True
                target_unit.cursed_turns = 3
            
            print(f"{self.name} curses {target_unit.name} - they will take +1 damage for 3 turns")
            return True
        return False
    
    def _use_fear(self, ability, target_pos, game):
        """Force target to move away from Warlock"""
        target_unit = self.board.units.get(target_pos)
        if target_unit:
            damage = ability.damage if ability.damage > 0 else 1
            target_unit.take_damage(damage, self)
            
            # Calculate direction away from Warlock
            target_row, target_col = target_pos
            warlock_row, warlock_col = self.position
            
            # Find direction away from Warlock
            dr = target_row - warlock_row
            dc = target_col - warlock_col
            
            # Normalize direction
            if dr != 0:
                dr = 1 if dr > 0 else -1
            if dc != 0:
                dc = 1 if dc > 0 else -1
            
            # Try to move target away
            new_r, new_c = target_row + dr, target_col + dc
            
            if (0 <= new_r < const.BOARD_SIZE and 0 <= new_c < const.BOARD_SIZE and 
                (new_r, new_c) not in self.board.units):
                # Move target away
                del self.board.units[target_pos]
                self.board.units[(new_r, new_c)] = target_unit
                target_unit.position = (new_r, new_c)
                print(f"{target_unit.name} is feared and moves away to {(new_r, new_c)}")
            else:
                print(f"{target_unit.name} is feared but cannot move away")
            
            return True
        return False
    
    def _use_dark_pact(self, ability, game):
        """Sacrifice health for AP"""
        if self.health <= 2:
            print(f"{self.name} cannot use Dark Pact - not enough health!")
            return False
        
        if self.dark_pact_used_this_turn:
            print(f"{self.name} has already used Dark Pact this turn!")
            return False
        
        # Sacrifice 2 health for 3 AP
        self.health -= 2
        if game:
            game.current_player_ap += 3
        
        self.dark_pact_used_this_turn = True
        print(f"{self.name} sacrifices 2 HP for 3 AP. Health: {self.health}, AP gained: 3")
        return True
    
    def _use_soul_burn(self, ability, target_pos):
        """Apply damage over time effect"""
        target_unit = self.board.units.get(target_pos)
        if target_unit:
            damage = ability.damage if ability.damage > 0 else 1
            target_unit.take_damage(damage, self)
            
            # Apply soul burn effect (1 damage per turn for 3 turns)
            if hasattr(target_unit, 'status_manager'):
                target_unit.status_manager.apply_effect("soul_burn", 3, self.player_id)
            else:
                # Legacy system
                target_unit.soul_burn_turns = 3
                target_unit.soul_burn_damage = 1
            
            print(f"{target_unit.name} is soul burned - will take 1 damage per turn for 3 turns")
            return True
        return False
    
    def _use_shadow_bolt(self, ability, target_pos):
        """High damage dark magic attack"""
        target_unit = self.board.units.get(target_pos)
        if target_unit:
            damage = ability.damage if ability.damage > 0 else 4
            target_unit.take_damage(damage, self)
            print(f"{self.name} hits {target_unit.name} with Shadow Bolt for {damage} damage")
            return True
        return False
    
    def reset_turn(self, game=None):
        """Reset turn-specific flags"""
        super().reset_turn(game=game)
        self.dark_pact_used_this_turn = False

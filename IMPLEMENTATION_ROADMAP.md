# 🎮 IMPLEMENTATION ROADMAP - BALANCED AP SYSTEM

## 🎯 **YOUR SYSTEM SPECIFICATIONS**

### **📈 AP Scaling: 1 → 10 (+1 per turn)**
```
Turn 1: 1 AP   (positioning only)
Turn 2: 2 AP   (move + basic action)  
Turn 3: 3 AP   (simple combos)
Turn 4: 4 AP   (tactical options)
Turn 5: 5 AP   (mid-game depth)
...
Turn 10: 10 AP (full complexity)
```

### **⚖️ Target Balance:**
- **Average Unit HP:** ~10 HP
- **First Player Advantage:** Mitigated with Player 2 early bonus
- **Balance Testing:** In-game sliders for real-time adjustments

---

## 🔧 **PHASE 1: CORE AP SYSTEM (Day 1-2)**

### **Step 1: Update Game Class**
```python
# In game_state.py
class Game:
    def __init__(self):
        # ... existing code ...
        
        # New AP system
        self.base_ap = 1
        self.ap_increment = 1  # +1 per turn
        self.max_ap = 10
        self.current_player_ap = 1
        self.units_acted_this_turn = set()
        
        # First player advantage mitigation
        self.player2_early_bonus = 1  # Extra AP for P2 in early turns
    
    def calculate_turn_ap(self, turn_number, player_id):
        """Calculate AP for current turn"""
        base_ap = min(self.base_ap + (turn_number - 1) * self.ap_increment, self.max_ap)
        
        # Player 2 early game bonus
        if player_id == 2 and turn_number <= 3:
            base_ap += self.player2_early_bonus
        
        return min(base_ap, self.max_ap)
    
    def start_player_turn(self, player_id):
        """Initialize turn with calculated AP"""
        self.current_player_ap = self.calculate_turn_ap(self.turn_count, player_id)
        self.units_acted_this_turn.clear()
        
        # Reset unit action flags
        for unit in self.board.units.values():
            if unit.player_id == player_id:
                unit.has_acted_this_turn = False
```

### **Step 2: Update Unit Class**
```python
# In units_core.py
class Unit:
    def __init__(self):
        # ... existing code ...
        self.has_acted_this_turn = False
        self.can_act_multiple_times = False  # For special abilities
    
    def use_ability(self, ability_idx, target_pos, game):
        """Modified to use global AP"""
        # Check if unit can act
        if self.has_acted_this_turn and not self.can_act_multiple_times:
            print(f"{self.name} has already acted this turn!")
            return False
        
        # Check global AP
        ability = self.abilities[ability_idx]
        ap_cost = ability.ap_cost
        
        if game.current_player_ap < ap_cost:
            print(f"Not enough AP! Need {ap_cost}, have {game.current_player_ap}")
            return False
        
        # Execute ability (existing logic)
        success = self._execute_ability_logic(ability_idx, target_pos, game)
        
        if success:
            # Deduct from global AP
            game.current_player_ap -= ap_cost
            self.has_acted_this_turn = True
            game.units_acted_this_turn.add(self)
        
        return success
```

---

## 🎛️ **PHASE 2: BALANCE SLIDER INTEGRATION (Day 2-3)**

### **Step 1: Add Slider System to Game**
```python
# In game_state.py
from balance_slider_system import BalanceSliderSystem

class Game:
    def __init__(self):
        # ... existing code ...
        self.balance_sliders = BalanceSliderSystem()
        self.show_balance_ui = False  # Toggle with key press
    
    def apply_balance_changes(self):
        """Apply slider values to game"""
        changes = self.balance_sliders.apply_slider_changes(self)
        return changes
```

### **Step 2: Update Unit Stats from Sliders**
```python
# In units_core.py
class Unit:
    def get_effective_health(self, game):
        """Get health modified by sliders"""
        base_hp = self.max_health
        
        # Get multiplier from sliders
        unit_type = self.__class__.__name__.lower()
        slider_id = f"{unit_type}_hp"
        
        if slider_id in game.balance_sliders.sliders:
            return int(game.balance_sliders.sliders[slider_id].current_value)
        
        return base_hp
    
    def get_effective_ap_cost(self, ability_idx, game):
        """Get ability AP cost modified by sliders"""
        ability = self.abilities[ability_idx]
        base_cost = ability.ap_cost
        
        # Get cost from sliders
        ability_name = ability.name.lower().replace(" ", "_")
        slider_id = f"{ability_name}_ap"
        
        if slider_id in game.balance_sliders.sliders:
            return int(game.balance_sliders.sliders[slider_id].current_value)
        
        return base_cost
```

---

## 🧪 **PHASE 3: COMPREHENSIVE TESTING (Day 3-4)**

### **Test Categories:**

#### **1. AP System Tests**
```python
def test_ap_scaling():
    """Test 1→10 AP progression"""
    
def test_first_player_advantage():
    """Test Player 2 early bonus"""
    
def test_one_action_per_unit():
    """Test action limitations"""
```

#### **2. Balance Integration Tests**
```python
def test_slider_integration():
    """Test sliders affect gameplay"""
    
def test_preset_loading():
    """Test balance presets work"""
    
def test_real_time_changes():
    """Test mid-game adjustments"""
```

#### **3. Game Flow Tests**
```python
def test_complete_game():
    """Test full game with new system"""
    
def test_victory_conditions():
    """Test games end properly"""
    
def test_turn_transitions():
    """Test turn management"""
```

---

## 🎨 **PHASE 4: UI INTEGRATION (Day 4-5)**

### **Step 1: Update Game UI**
```python
# In game_ui.py
def draw_ap_display(game, screen):
    """Draw current AP and turn info"""
    font = pygame.font.Font(None, 36)
    
    # Current AP
    ap_text = f"AP: {game.current_player_ap}"
    ap_surface = font.render(ap_text, True, (255, 255, 255))
    screen.blit(ap_surface, (10, 10))
    
    # Turn info
    turn_text = f"Turn {game.turn_count} - Player {game.current_player}"
    turn_surface = font.render(turn_text, True, (255, 255, 255))
    screen.blit(turn_surface, (10, 50))

def draw_balance_sliders(game, screen):
    """Draw balance slider UI"""
    if game.show_balance_ui:
        game.balance_sliders.render_sliders(screen, 50, 100)
```

### **Step 2: Add Hotkeys**
```python
# In game_logic.py
def handle_keypress(game, key):
    """Handle keyboard input"""
    if key == pygame.K_F1:
        game.show_balance_ui = not game.show_balance_ui
    elif key == pygame.K_F2:
        game.balance_sliders.load_balance_preset("conservative")
    elif key == pygame.K_F3:
        game.balance_sliders.load_balance_preset("balanced")
    elif key == pygame.K_F4:
        game.balance_sliders.load_balance_preset("aggressive")
```

---

## ⚖️ **RECOMMENDED ABILITY COSTS**

### **Early Game (1-3 AP):**
```python
Move: 1 AP
Basic Attack: 1 AP
Simple Heal: 2 AP
Simple Damage: 2 AP
```

### **Mid Game (4-6 AP):**
```python
Advanced Abilities: 3-4 AP
Area Effects: 4-5 AP
Strong Attacks: 3-4 AP
```

### **Late Game (7-10 AP):**
```python
Ultimate Abilities: 6-8 AP
Mass Effects: 7-9 AP
Game Changers: 8-10 AP
```

---

## 🎯 **FIRST PLAYER ADVANTAGE SOLUTIONS**

### **Option 1: AP Bonus (Recommended)**
```
Turn 1: P1=1 AP, P2=2 AP (+1 bonus)
Turn 2: P1=2 AP, P2=3 AP (+1 bonus)  
Turn 3: P1=3 AP, P2=4 AP (+1 bonus)
Turn 4+: Equal AP
```

### **Option 2: Extra Action**
```
Player 2 gets one "free" 1 AP action in first 3 turns
```

### **Option 3: Setup Advantage**
```
Player 2 places units after seeing Player 1's setup
```

---

## 📊 **BALANCE TESTING WORKFLOW**

### **1. Baseline Testing**
- Play 10 games with default values
- Record win rates and game length
- Identify problem areas

### **2. Slider Adjustments**
- Use sliders to test different values
- Save promising configurations as presets
- Test edge cases and extreme values

### **3. Statistical Analysis**
- Track win rates over time
- Monitor average game length
- Identify overpowered/underpowered abilities

### **4. Iterative Refinement**
- Make small adjustments based on data
- Test new configurations
- Converge on balanced values

---

## 🚀 **IMPLEMENTATION TIMELINE**

### **Week 1: Core System**
- **Day 1:** Implement AP scaling system
- **Day 2:** Update all abilities to use global AP
- **Day 3:** Add one-action-per-unit enforcement
- **Day 4:** Basic testing and debugging
- **Day 5:** First player advantage mitigation

### **Week 2: Balance & Polish**
- **Day 1:** Integrate balance slider system
- **Day 2:** Update UI for new AP system
- **Day 3:** Comprehensive testing
- **Day 4:** Balance testing and adjustments
- **Day 5:** Documentation and polish

---

## 🎮 **SUCCESS CRITERIA**

### **Functional Requirements:**
- ✅ AP scales from 1 to 10 over turns
- ✅ Units can only act once per turn
- ✅ First player advantage is mitigated
- ✅ Balance sliders work in real-time
- ✅ Games complete without bugs

### **Balance Requirements:**
- ✅ Player 1 vs Player 2 win rate: 45-55%
- ✅ Average game length: 6-12 turns
- ✅ No single dominant strategy
- ✅ All units viable throughout game

### **User Experience:**
- ✅ Clear AP feedback in UI
- ✅ Intuitive balance adjustment
- ✅ Smooth gameplay flow
- ✅ Easy preset switching

This roadmap provides a complete path to implementing your balanced AP system with comprehensive testing and balance tools!

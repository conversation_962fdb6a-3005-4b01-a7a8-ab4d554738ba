import pygame
from units_core import Unit, SimpleAbility, MoveAbility, AttackAbility, SummonAbility
from game_config import GAME_CONFIG
import game_constants as const
import math

# Import unified systems
from core.configuration_manager import get_config_manager
from core.ability_system import DamageCalculator
from core.status_effects import StatusEffectType

class Warrior(Unit):
    """Warrior unit that moves and attacks orthogonally like a chess rook"""
    def __init__(self, player_id):
        # Initialize with unified configuration system
        config_manager = get_config_manager()
        warrior_config = config_manager.get_unit_config("Warrior")
        
        super().__init__(
            player_id, 
            health=warrior_config.get("health", 7), 
            max_health=warrior_config.get("health", 7)
        )
        self.name = "Warrior"
        self.max_ap = warrior_config.get("max_ap", 6)
        self.current_ap = warrior_config.get("max_ap", 6)
        self.board = None  # Will be set later
        
        # Warrior-specific state variables
        self.defensive_stance_active = False
        self.defensive_stance_until = 0  # Track when to deactivate
        self.riposte_active = False
        
        # Load image (placeholder for now)
        self.image = self._create_placeholder_image((180, 40, 40) if player_id == 2 else (0, 80, 180))
        
        # Abilities with unified configuration
        self.abilities = [
            MoveAbility(self),
            AttackAbility(self),
            SimpleAbility("Cleave Attack", config_manager.get_ability_ap_cost("Warrior", "Cleave Attack"), "Hit target and adjacent enemies", cooldown=1, owner=self),
            SimpleAbility("Shield Bash", config_manager.get_ability_ap_cost("Warrior", "Shield Bash"), "Deal damage and stun the target", cooldown=2, owner=self),
            SimpleAbility("Charge", config_manager.get_ability_ap_cost("Warrior", "Charge"), "Move up to 3 tiles and attack", cooldown=2, owner=self),
            SimpleAbility("Defensive Stance", config_manager.get_ability_ap_cost("Warrior", "Defensive Stance"), "Take 50% less damage until your next turn", cooldown=3, owner=self),
            SimpleAbility("Riposte", config_manager.get_ability_ap_cost("Warrior", "Riposte"), "Counter the next attack with 1 damage", cooldown=2, owner=self),
            SummonAbility(self, config_manager.get_ability_ap_cost("Warrior", "Summon"))
        ]

        # Configuration is automatically applied by the base Unit class
    
    def _create_placeholder_image(self, color):
        """Create a placeholder image with a sword symbol"""
        surf = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA)
        
        # Main circle
        pygame.draw.circle(surf, color, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2)
        pygame.draw.circle(surf, const.WHITE, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2, 2)
        
        # Sword (vertical line with crossguard)
        sword_start = (const.CELL_SIZE//2, const.CELL_SIZE//4)
        sword_end = (const.CELL_SIZE//2, 3*const.CELL_SIZE//4)
        pygame.draw.line(surf, const.WHITE, sword_start, sword_end, 3)
        
        # Crossguard (horizontal line)
        guard_start = (const.CELL_SIZE//2 - 8, const.CELL_SIZE//3)
        guard_end = (const.CELL_SIZE//2 + 8, const.CELL_SIZE//3)
        pygame.draw.line(surf, const.WHITE, guard_start, guard_end, 2)
        
        return surf

    def get_valid_moves(self, board):
        """Warrior moves orthogonally like a chess rook"""
        # Check if unit can move using unified status system
        if not self.status_effect_manager.can_unit_move(self):
            return []
            
        valid_moves = []
        row, col = self.position
        
        # Check all four orthogonal directions
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]  # right, left, down, up
        
        for dr, dc in directions:
            for distance in range(1, const.BOARD_SIZE):
                new_row, new_col = row + dr * distance, col + dc * distance
                
                # Check bounds
                if not (0 <= new_row < const.BOARD_SIZE and 0 <= new_col < const.BOARD_SIZE):
                    break
                
                # Check if tile is occupied
                if (new_row, new_col) in board.units:
                    break  # Can't move through units
                
                valid_moves.append((new_row, new_col))
        
        return valid_moves

    def get_valid_attacks(self, board):
        """Warrior attacks orthogonally like movement"""
        # Check if unit can use abilities using unified status system
        if not self.status_effect_manager.can_unit_use_abilities(self):
            return []
            
        valid_attacks = []
        row, col = self.position
        
        # Check all four orthogonal directions
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]  # right, left, down, up
        
        for dr, dc in directions:
            for distance in range(1, 5):  # Attack range of 4
                attack_row, attack_col = row + dr * distance, col + dc * distance
                
                # Check bounds
                if not (0 <= attack_row < const.BOARD_SIZE and 0 <= attack_col < const.BOARD_SIZE):
                    break
                
                # Check if there's a unit here
                if (attack_row, attack_col) in board.units:
                    target_unit = board.units[(attack_row, attack_col)]
                    # Can attack enemies
                    if target_unit.player_id != self.player_id and not target_unit.sanctuary:
                        valid_attacks.append((attack_row, attack_col))
                    break  # Line of sight blocked
        
        return valid_attacks

    def get_ability_targets(self, ability_idx, board):
        """Get valid targets for Warrior abilities"""
        self.board = board
        # Basic move
        if ability_idx == 0: return self.get_valid_moves(board)
        # Basic attack
        if ability_idx == 1: return self.get_valid_attacks(board)

        ability_name = self.abilities[ability_idx].name

        if ability_name == "Cleave Attack":
            return self.get_valid_attacks(board)  # Same as basic attack
        elif ability_name == "Shield Bash":
            return self.get_valid_attacks(board)  # Same as basic attack
        elif ability_name == "Charge":
            # Charge targets are enemies within charge range
            valid_charge_targets = []
            for pos, unit in board.units.items():
                if unit.player_id != self.player_id and not unit.sanctuary:
                    # Check if we can charge to a position adjacent to this enemy
                    enemy_row, enemy_col = pos
                    for dr, dc in [(0,1), (0,-1), (1,0), (-1,0)]:  # Adjacent positions
                        charge_pos = (enemy_row + dr, enemy_col + dc)
                        if (0 <= charge_pos[0] < const.BOARD_SIZE and
                            0 <= charge_pos[1] < const.BOARD_SIZE and
                            charge_pos not in board.units):
                            # Check if charge distance is within range
                            charge_distance = abs(charge_pos[0] - self.position[0]) + abs(charge_pos[1] - self.position[1])
                            if charge_distance <= 3:
                                valid_charge_targets.append(pos)
                                break
            return valid_charge_targets
        elif ability_name == "Defensive Stance":
            return [self.position]  # Self-target
        elif ability_name == "Riposte":
            return [self.position]  # Self-target
        elif ability_name == "Summon":
            return self.get_valid_moves(board)

        return []

    def use_ability(self, ability_idx, target_pos, game=None):
        """
        Use an ability with unified ability execution system.
        Warrior-specific abilities are handled by registered methods.
        """
        # Ensure self.board is set if game object is provided
        if game:
            self.board = game.board

        # Register Warrior-specific ability handlers with the unified system
        self._register_ability_handlers()

        # Use unified ability executor (this handles all validation, AP spending, etc.)
        return super().use_ability(ability_idx, target_pos, game)

    def _register_ability_handlers(self):
        """Register Warrior-specific ability handlers with the unified ability system"""
        ability_executor = self.ability_executor

        # Register handlers for each Warrior ability with proper lambda wrappers
        ability_executor.register_ability_handler("Cleave Attack", lambda unit, target_pos, game: self._use_cleave_attack(target_pos, game))
        ability_executor.register_ability_handler("Shield Bash", lambda unit, target_pos, game: self._use_shield_bash(target_pos, game))
        ability_executor.register_ability_handler("Charge", lambda unit, target_pos, game: self._use_charge(target_pos, game))
        ability_executor.register_ability_handler("Defensive Stance", lambda unit, target_pos, game: self._use_defensive_stance(target_pos, game))
        ability_executor.register_ability_handler("Riposte", lambda unit, target_pos, game: self._use_riposte(target_pos, game))

    def get_attack_damage(self, target_pos):
        """Calculate Warrior basic attack damage using unified damage calculation"""
        return DamageCalculator.calculate_basic_attack_damage(self, target_pos)

    def _use_cleave_attack(self, target_pos, game=None):
        """Execute Cleave Attack ability - damages target and adjacent units using unified damage calculation"""
        target_row, target_col = target_pos
        hits = 0

        # Get configured damage using unified damage calculation
        ability_damage = DamageCalculator.calculate_ability_damage(self, "Cleave Attack", target_pos)

        target_unit = self.board.units.get(target_pos)
        if target_unit and target_unit.player_id != self.player_id:
            print(f"Cleave hits main target {target_unit.name} at {target_pos}")
            target_unit.take_damage(ability_damage, self)
            hits += 1
        elif not target_unit:
            print(f"Cleave targets empty tile {target_pos}, checking adjacent for cleave.")

        # Determine "facing" for cleave (direction from warrior to target_pos)
        d_row = target_row - self.position[0]
        d_col = target_col - self.position[1]

        # Normalize (crude, assumes adjacent target)
        if d_row != 0: d_row //= abs(d_row)
        if d_col != 0: d_col //= abs(d_col)

        # Cleave targets are perpendicular to the attack direction
        cleave_positions = []
        if d_col == 0: # Vertical attack, cleave horizontally
            cleave_positions.append((target_row, target_col - 1))
            cleave_positions.append((target_row, target_col + 1))
        elif d_row == 0: # Horizontal attack, cleave vertically
            cleave_positions.append((target_row - 1, target_col))
            cleave_positions.append((target_row + 1, target_col))
        else: # Diagonal attack - cleave to the two tiles that form a 'T' with the warrior and target
            cleave_positions.append((target_row - d_row, target_col)) # one side
            cleave_positions.append((target_row, target_col - d_col)) # other side

        for r, c in cleave_positions:
            if 0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE:
                cleave_unit = self.board.units.get((r, c))
                if cleave_unit and cleave_unit.player_id != self.player_id and not cleave_unit.sanctuary:
                    print(f"Cleave hits secondary target {cleave_unit.name} at {(r,c)}")
                    cleave_unit.take_damage(ability_damage, self)
                    hits += 1

        print(f"Cleave Attack hit {hits} total targets")
        return True

    def _use_shield_bash(self, target_pos, game=None):
        """Shield Bash: Damage and stun target using unified systems"""
        target_unit = self.board.units.get(target_pos)
        if not (target_unit and target_unit.player_id != self.player_id):
            return False

        # Get configured damage using unified damage calculation
        ability_damage = DamageCalculator.calculate_ability_damage(self, "Shield Bash", target_pos)

        print(f"Shield Bash hits {target_unit.name} at {target_pos}")
        target_unit.take_damage(ability_damage, self, game=game)

        # Apply stun effect using unified status system
        target_unit.apply_status('Stunned', 2)  # Stun for 2 turns
        print(f"{target_unit.name} stunned for 2 turns")
        return True

    def _use_charge(self, target_pos, game=None):
        """Charge: Move up to 3 tiles toward target and attack using unified damage calculation"""
        target_unit = self.board.units.get(target_pos)
        if not (target_unit and target_unit.player_id != self.player_id):
            return False

        # Find the best position to charge to (adjacent to target)
        d_row = target_pos[0] - self.position[0]
        d_col = target_pos[1] - self.position[1]

        # Normalize direction vector
        norm_dr = d_row // abs(d_row) if d_row != 0 else 0
        norm_dc = d_col // abs(d_col) if d_col != 0 else 0

        # Find suitable move position (adjacent to enemy, in line of charge)
        move_pos_candidate = (target_pos[0] - norm_dr, target_pos[1] - norm_dc)

        # Check if the charge path is clear and within range
        charge_distance = abs(move_pos_candidate[0] - self.position[0]) + abs(move_pos_candidate[1] - self.position[1])
        if (charge_distance <= 3 and
            0 <= move_pos_candidate[0] < const.BOARD_SIZE and
            0 <= move_pos_candidate[1] < const.BOARD_SIZE and
            move_pos_candidate not in self.board.units):

            # Move to the charge position
            old_pos = self.position
            self.board.units[move_pos_candidate] = self
            del self.board.units[old_pos]
            self.position = move_pos_candidate
            print(f"{self.name} charges from {old_pos} to {move_pos_candidate}")

            # Attack the target with configured damage
            charge_damage = DamageCalculator.calculate_ability_damage(self, "Charge", target_pos)
            target_unit.take_damage(charge_damage, self, game=game)
            print(f"Charge attack hits {target_unit.name} for {charge_damage} damage")
            return True

        print(f"Charge failed - path blocked or out of range")
        return False

    def _use_defensive_stance(self, target_pos, game=None):
        """Defensive Stance: Reduce damage taken until next turn"""
        self.defensive_stance_active = True
        if game and hasattr(game, 'turn_count'):
            self.defensive_stance_until = game.turn_count + 1
        print(f"{self.name} enters Defensive Stance - damage reduced by 50%")
        return True

    def _use_riposte(self, target_pos, game=None):
        """Riposte: Prepare to counter the next attack"""
        self.riposte_active = True
        print(f"{self.name} prepares to Riposte")
        return True

    def take_damage(self, amount, attacker=None, game=None):
        """Override take_damage to handle Defensive Stance and Riposte using unified damage calculation"""
        original_amount = amount
        if self.defensive_stance_active:
            amount //= 2
            print(f"{self.name}'s Defensive Stance reduces damage from {original_amount} to {amount}.")

        # Handle Riposte: If active and attacker exists and is in melee range
        if self.riposte_active and attacker and attacker.is_alive():
            dist_sq = (self.position[0] - attacker.position[0])**2 + (self.position[1] - attacker.position[1])**2
            if dist_sq <= 2: # Assuming melee range is 1 (dist_sq 1 or 2 for diagonals)
                # Get configured damage for Riposte using unified damage calculation
                riposte_damage = DamageCalculator.calculate_ability_damage(self, "Riposte")

                print(f"{self.name} Ripostes against {attacker.name} for {riposte_damage} damage!")
                attacker.take_damage(riposte_damage, self, game=game)
                self.riposte_active = False # Riposte triggers once

        super().take_damage(amount, attacker, game=game)
        if not self.is_alive():
            print(f"{self.name} has been defeated!")
        return self.health

    def reset_ap(self, game=None):
        """Override reset_ap to handle Warrior-specific state cleanup"""
        super().reset_ap(game=game)
        # Defensive stance wears off at the START of the next turn for this unit
        if game and self.defensive_stance_active and game.turn_count >= self.defensive_stance_until:
             self.defensive_stance_active = False
             print(f"{self.name}'s Defensive Stance wears off.")
        # Riposte also wears off if not used
        if self.riposte_active: # If it didn't trigger, it wears off now
            self.riposte_active = False
            print(f"{self.name}'s Riposte preparation wears off.")

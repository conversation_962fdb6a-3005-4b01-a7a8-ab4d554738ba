import pygame
import math
from abc import ABC, abstractmethod
from game_config import GAME_CONFIG
import game_constants as const

# Import unified systems
from core.configuration_manager import get_config_manager, apply_config_to_unit
from core.status_effects import get_status_effect_manager, StatusEffectType
from core.ability_system import get_ability_executor, DamageCalculator

class StatusEffect:
    """Represents an active status effect on a unit."""
    def __init__(self, name, duration, owner):
        self.name = name
        self.duration = duration  # Duration in number of the owner's turns
        self.owner = owner

    def tick(self):
        """Decrements the duration of the status effect."""
        self.duration -= 1
        print(f"'{self.name}' on {self.owner.name} has {self.duration} turns remaining.")

    def is_expired(self):
        """Check if the status effect has expired."""
        return self.duration <= 0

class Unit(ABC):
    """Base class for all units in the game"""
    def __init__(self, player_id, health=3, max_health=3):
        self.player_id = player_id
        self.health = health
        self.max_health = max_health
        self.position = (0, 0)
        self.level = 1
        self.xp = 0
        self.name = "Generic Unit"

        self.max_ap = 6
        self.current_ap = 6
        self.abilities = []

        # Unified status effect system (replaces old status tracking)
        self.status_effects = {}  # Dictionary for new unified system
        self.status_effect_manager = get_status_effect_manager()

        # Configuration manager for unified config access
        self.config_manager = get_config_manager()

        # Ability executor for unified ability handling (each unit gets its own instance)
        from core.ability_system import AbilityExecutor
        self.ability_executor = AbilityExecutor()

        # Protection tracking (can also be converted to status effects if they have durations)
        self.sanctuary = False

        # Legacy status effect compatibility properties (for backward compatibility)
        self._stunned = False
        self._immobilized = False
        self._chilled = False
        self._poisoned = False
        self._divine_protection = False
        self.stunned_until = 0
        self.immobilized_until = 0
        self.poison_remaining = 0

        color = "blue" if player_id == 1 else "red"
        self.image = None

        # Apply configuration using unified system
        self._apply_unit_configuration()

    def _apply_unit_configuration(self):
        """Apply configuration using the unified configuration system"""
        try:
            apply_config_to_unit(self)
        except Exception as e:
            print(f"Warning: Could not apply configuration to {self.__class__.__name__}: {e}")

    def apply_status(self, effect_name, duration):
        """
        Applies a status effect to the unit using the unified status system.
        Maintains backward compatibility with old status effect names.
        """
        # Map old status names to new enum types
        status_mapping = {
            'Stunned': StatusEffectType.STUNNED,
            'Crippled': StatusEffectType.CRIPPLED,
            'Immobilized': StatusEffectType.CRIPPLED,  # Map to Crippled
            'Chilled': StatusEffectType.CHILLED,
            'Poisoned': StatusEffectType.POISONED,
        }

        if effect_name in status_mapping:
            # Use unified status system
            self.status_effect_manager.apply_status_effect(
                self, status_mapping[effect_name], duration
            )
        else:
            # Fallback to old system for unknown effects - but keep status_effects as dict
            # Initialize legacy_status_effects list if needed
            if not hasattr(self, 'legacy_status_effects'):
                self.legacy_status_effects = []

            # Remove existing effect with same name
            self.legacy_status_effects = [ef for ef in self.legacy_status_effects if ef.name != effect_name]
            new_effect = StatusEffect(effect_name, duration, self)
            self.legacy_status_effects.append(new_effect)
            print(f"{self.name} is now affected by '{effect_name}' for {duration} turns.")

    def has_status(self, effect_name):
        """
        Checks if the unit has a specific status effect.
        Works with both unified and legacy status systems.
        """
        # Check unified status system first
        status_mapping = {
            'Stunned': StatusEffectType.STUNNED,
            'Crippled': StatusEffectType.CRIPPLED,
            'Immobilized': StatusEffectType.CRIPPLED,
            'Chilled': StatusEffectType.CHILLED,
            'Poisoned': StatusEffectType.POISONED,
        }

        if effect_name in status_mapping:
            return status_mapping[effect_name] in self.status_effects

        # Fallback to legacy system
        if hasattr(self, 'legacy_status_effects'):
            return any(effect.name == effect_name for effect in self.legacy_status_effects)

        return False

    # Legacy status effect compatibility properties
    @property
    def stunned(self):
        """Compatibility property for legacy stunned checks"""
        return self._stunned or self.has_status('Stunned')

    @stunned.setter
    def stunned(self, value):
        self._stunned = value

    @property
    def immobilized(self):
        """Compatibility property for legacy immobilized checks"""
        return self._immobilized or self.has_status('Immobilized') or self.has_status('Crippled')

    @immobilized.setter
    def immobilized(self, value):
        self._immobilized = value

    @property
    def chilled(self):
        """Compatibility property for legacy chilled checks"""
        return self._chilled or self.has_status('Chilled')

    @chilled.setter
    def chilled(self, value):
        self._chilled = value

    @property
    def poisoned(self):
        """Compatibility property for legacy poisoned checks"""
        return self._poisoned or self.has_status('Poisoned')

    @poisoned.setter
    def poisoned(self, value):
        self._poisoned = value

    @property
    def divine_protection(self):
        """Compatibility property for legacy divine protection checks"""
        return self._divine_protection or self.has_status('Divine Protection')

    @divine_protection.setter
    def divine_protection(self, value):
        self._divine_protection = value

    def tick_statuses(self, game=None):
        """
        Ticks down the duration of all status effects at the start of the unit's turn.
        Uses unified status effect system.
        """
        # Use unified status effect manager for turn processing
        can_act = self.status_effect_manager.process_turn_start(self)

        # Handle legacy status effects if they exist
        if hasattr(self, 'legacy_status_effects'):
            # Handle start-of-turn damage from effects like Poison
            if self.has_status("Poisoned"):
                print(f"{self.name} takes 1 poison damage.")
                self.take_damage(1, attacker=None, game=game)
                if not self.is_alive():
                    # No need to tick other statuses if the unit dies
                    return can_act

            # Tick down durations and remove expired effects (legacy system)
            for effect in self.legacy_status_effects:
                if hasattr(effect, 'tick'):
                    effect.tick()

            # Filter out expired effects (legacy system)
            self.legacy_status_effects = [ef for ef in self.legacy_status_effects if not ef.is_expired()]

        return can_act

    def process_turn_end(self):
        """
        Process status effects at the end of the unit's turn.
        Uses unified status effect system.
        """
        self.status_effect_manager.process_turn_end(self)

    def reset_ap(self, game=None):
        """Reset AP at the start of the player's turn. Does NOT clear statuses anymore."""
        self.current_ap = self.max_ap

    def get_valid_moves(self, board):
        """Get valid movement positions for this unit using unified status system"""
        # Check if unit can move using unified status system
        if not self.status_effect_manager.can_unit_move(self):
            return []
        # Base implementation - overridden by specific unit types
        return []
    
    def can_use_ability(self, ability_idx, game=None):
        """Check if the unit can use a specific ability using unified status system"""
        # Check if unit can use abilities using unified status system
        if not self.status_effect_manager.can_unit_use_abilities(self):
            return False

        if ability_idx < 0 or ability_idx >= len(self.abilities):
            return False

        ability = self.abilities[ability_idx]
        ap_cost = self.get_ability_ap_cost(ability_idx)

        # Check global AP if game is provided, otherwise check unit AP (backward compatibility)
        if game and hasattr(game, 'current_player_ap'):
            if game.current_player_ap < ap_cost:
                return False
        else:
            # Fallback to individual AP system
            if self.current_ap < ap_cost:
                return False

        if ability.cooldown_remaining > 0:
            return False

        return True

    def get_ability_ap_cost(self, ability_idx):
        """Get the AP cost for an ability, including status effect modifiers using unified system"""
        if ability_idx < 0 or ability_idx >= len(self.abilities):
            return 0

        ability = self.abilities[ability_idx]
        base_cost = ability.ap_cost

        # Use unified status effect system for AP cost modification
        return self.status_effect_manager.get_modified_ap_cost(self, base_cost)

    def move(self, target_pos, game_board):
        """Move the unit to a new position"""
        if not game_board:
            print("Error: game_board not provided to unit.move()")
            return False
        old_pos = self.position
        success = game_board.move_unit(old_pos, target_pos)
        if success:
            self.position = target_pos
            return True
        return False

    def attack(self, target_pos, game=None):
        """Attack a target position"""
        damage = self.get_attack_damage(target_pos)
        if hasattr(self, 'board') and self.board and target_pos in self.board.units:
            target_unit = self.board.units[target_pos]
            target_unit.take_damage(damage, self, game=game)
        return damage

    def get_attack_damage(self, target_pos):
        """Calculate attack damage using unified damage calculation system"""
        return DamageCalculator.calculate_basic_attack_damage(self, target_pos)

    def get_valid_attacks(self, board):
        """Get valid attack targets for this unit using unified status system"""
        # Check if unit can use abilities (attacks are abilities) using unified status system
        if not self.status_effect_manager.can_unit_use_abilities(self):
            return []
            
        valid_attacks = []
        row, col = self.position
        adjacent_tiles = [(row-1, col), (row+1, col), (row, col-1), (row, col+1)]
        
        for r, c in adjacent_tiles:
            if (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and
                (r, c) in board.units and
                board.units[(r, c)].player_id != self.player_id and
                not board.units[(r, c)].sanctuary):
                valid_attacks.append((r, c))
        
        return valid_attacks

    def take_damage(self, amount, attacker=None, game=None):
        """Take damage and check if defeated"""
        if self.has_status('Divine Protection'):
            amount = amount // 2
            
        self.health -= amount
        if self.health <= 0:
            self.health = 0
            if attacker:
                attacker.gain_xp(1)
        return self.health

    def is_alive(self):
        return self.health > 0

    def gain_xp(self, amount):
        self.xp += amount
        xp_to_level = GAME_CONFIG["global_settings"].get("xp_to_level", 3)
        max_level = GAME_CONFIG["global_settings"].get("max_level", 5)
        if self.xp >= xp_to_level and self.level < max_level:
            self.level_up()
            self.xp = 0

    def level_up(self):
        self.level += 1
        self.max_health += GAME_CONFIG["global_settings"].get("level_up_health_increase", 1)
        self.health += GAME_CONFIG["global_settings"].get("level_up_heal_amount", 1)

    @abstractmethod
    def _create_placeholder_image(self, color):
        pass

    def get_ability_targets(self, ability_idx, board):
        if ability_idx == 0 and self.abilities[0].name == "Move":
             return self.get_valid_moves(board)
        elif ability_idx == 1 and self.abilities[1].name == "Basic Attack":
             return self.get_valid_attacks(board)
        return []

    def use_ability(self, ability_idx, target_pos, game=None):
        """
        Use an ability with unified ability execution system.
        This method serves as the main entry point for ability usage.
        """
        # Use unified ability executor for comprehensive handling
        success, result = self.ability_executor.execute_ability(self, ability_idx, target_pos, game)

        # Log result for debugging
        if not success:
            print(f"{self.name} failed to use ability {ability_idx}: {result.value}")

        return success

    def use_ability_base(self, ability_idx, target_pos, game=None):
        """
        Base ability execution for standard abilities (Move, Attack).
        Used by the unified ability executor for standard ability handling.
        """
        if not self.can_use_ability(ability_idx, game):
            return False

        ability = self.abilities[ability_idx]
        ap_cost = self.get_ability_ap_cost(ability_idx)

        # Use global AP system if game is provided, otherwise fall back to unit AP
        if game and hasattr(game, 'spend_ap'):
            # Check if unit has already acted (for global AP system)
            if hasattr(self, 'has_acted_this_turn') and self.has_acted_this_turn:
                print(f"{self.name} has already acted this turn!")
                return False

            # Try to spend AP from global pool
            if not game.spend_ap(ap_cost, self):
                print(f"Not enough global AP! Need {ap_cost}, have {game.current_player_ap}")
                return False
        else:
            # Fallback to old per-unit AP system
            self.current_ap -= ap_cost

        ability.cooldown_remaining = ability.cooldown

        # Let the ability object handle its execution
        success = ability.execute(target_pos, game)

        # If execution failed and we used global AP, we should refund it
        if not success and game and hasattr(game, 'current_player_ap'):
            game.current_player_ap += ap_cost
            if hasattr(self, 'has_acted_this_turn'):
                self.has_acted_this_turn = False
            if hasattr(game, 'units_acted_this_turn'):
                game.units_acted_this_turn.discard(self)
            ability.cooldown_remaining = 0  # Reset cooldown on failure
        elif not success:
            # Refund unit AP on failure
            self.current_ap += ap_cost
            ability.cooldown_remaining = 0

        return success

    def _spend_ap_for_ability(self, ability_idx, game=None):
        """Helper method for unit subclasses to spend AP using global system"""
        # Check if unit can act (global AP system)
        if game and hasattr(game, 'can_unit_act') and not game.can_unit_act(self):
            return False, 0

        if not self.can_use_ability(ability_idx, game):
            return False, 0

        ability = self.abilities[ability_idx]
        ap_cost = ability.ap_cost + (1 if self.has_status('Chilled') else 0)

        # Use global AP system if available, otherwise fall back to individual AP
        if game and hasattr(game, 'spend_ap'):
            if not game.spend_ap(ap_cost, self):
                return False, ap_cost
        else:
            # Fallback to individual AP system
            if self.current_ap < ap_cost:
                return False, ap_cost
            self.current_ap -= ap_cost

        ability.cooldown_remaining = ability.cooldown
        return True, ap_cost

    def reset_turn(self, game=None):
        """Reset turn-based effects and handle status expiration"""
        # This method is called at the end of the unit's player's turn
        # Handle any turn-based cleanup here

        # Legacy status effect cleanup (if using old system)
        if hasattr(self, '_stunned_until') and hasattr(game, 'turn_count'):
            if self._stunned_until <= game.turn_count:
                self._stunned = False
                self._stunned_until = 0

        if hasattr(self, '_immobilized_until') and hasattr(game, 'turn_count'):
            if self._immobilized_until <= game.turn_count:
                self._immobilized = False
                self._immobilized_until = 0

        # New status effect system handles its own expiration via tick_statuses
        # No additional cleanup needed here for new system
        pass

    def __str__(self):
        # Collect status effects from both unified and legacy systems
        status_list = []

        # Unified status effects (dict)
        if hasattr(self, 'status_effects') and isinstance(self.status_effects, dict):
            for effect_type, effect in self.status_effects.items():
                status_list.append(f"{effect_type.value}({effect.duration})")

        # Legacy status effects (list)
        if hasattr(self, 'legacy_status_effects'):
            for effect in self.legacy_status_effects:
                status_list.append(f"{effect.name}({effect.duration})")

        statuses = ', '.join(status_list) or "None"
        return (f"{self.name} (Player {self.player_id}) "
                f"HP: {self.health}/{self.max_health} AP: {self.current_ap}/{self.max_ap} "
                f"Pos: {self.position} Lvl: {self.level} XP: {self.xp} "
                f"Statuses: [{statuses}]")

class Ability(ABC):
    """Base class for abilities"""
    def __init__(self, name, ap_cost, description, cooldown=0, owner=None):
        self.name = name
        self.ap_cost = ap_cost
        self.description = description
        self.cooldown = cooldown
        self.cooldown_remaining = 0
        self.owner = owner

    @abstractmethod
    def execute(self, target_pos=None, game=None):
        raise NotImplementedError

    def reset_cooldown(self):
        self.cooldown_remaining = 0

    def tick_cooldown(self):
        if self.cooldown_remaining > 0:
            self.cooldown_remaining -= 1

class MoveAbility(Ability):
    def __init__(self, owner):
        super().__init__(
            name="Move", 
            ap_cost=GAME_CONFIG["global_settings"].get("move_ap_cost", 1), 
            description="Move to a valid tile", 
            cooldown=0,
            owner=owner
        )

    def execute(self, target_pos, game=None):
        if game:
            return self.owner.move(target_pos, game.board)
        print("Error: game object not provided to MoveAbility.execute")
        return False

class AttackAbility(Ability):
    def __init__(self, owner):
        super().__init__(
            name="Basic Attack", 
            ap_cost=GAME_CONFIG["global_settings"].get("attack_ap_cost", 2), 
            description="Perform a basic attack on an adjacent enemy", 
            cooldown=0,
            owner=owner
        )

    def execute(self, target_pos, game=None):
        if game:
            self.owner.board = game.board
            return self.owner.attack(target_pos, game=game) > 0
        print("Error: game object not provided to AttackAbility.execute")
        return False

class StunAbility(Ability):
    """A sample ability that applies a stun effect."""
    def __init__(self, owner):
        super().__init__(
            name="Stun",
            ap_cost=3,
            description="Stuns an enemy for 2 turns, preventing them from acting.",
            cooldown=4,
            owner=owner
        )

    def execute(self, target_pos, game=None):
        if not game:
            print("Error: StunAbility requires a game object.")
            return False

        target_unit = game.board.units.get(target_pos)
        if target_unit:
            target_unit.apply_status('Stunned', 2) # Apply stun for 2 turns
            return True
        return False

class SimpleAbility(Ability):
    """A simple ability that delegates execution to the owner unit's use_ability method."""
    def __init__(self, name, ap_cost, description, cooldown=0, owner=None):
        super().__init__(name, ap_cost, description, cooldown, owner)

    def execute(self, target_pos, game=None):
        # This is handled by the unit's use_ability method
        # This class exists just to provide a concrete implementation
        return False

class SummonAbility(Ability):
    """Ability to summon a pawn unit at a valid movement position."""
    def __init__(self, owner, ap_cost=3):
        super().__init__(
            name="Summon",
            ap_cost=ap_cost,
            description="Summon a pawn unit at a position you could move to",
            cooldown=3,
            owner=owner
        )

    def execute(self, target_pos, game=None):
        if not game:
            print("Error: SummonAbility requires a game object.")
            return False

        # Import Pawn here to avoid circular imports
        from units.pawn import Pawn

        # Check if target position is empty
        if target_pos in game.board.units:
            print(f"Cannot summon at {target_pos}: position occupied.")
            return False

        # Create new pawn unit
        pawn = Pawn(self.owner.player_id)

        # Add pawn to the board
        success = game.board.add_unit(pawn, target_pos[0], target_pos[1])
        if success:
            print(f"{self.owner.name} summons a Pawn at {target_pos}")
            return True
        else:
            print(f"Failed to summon Pawn at {target_pos}")
            return False
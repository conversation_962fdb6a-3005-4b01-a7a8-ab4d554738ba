#!/usr/bin/env python3
"""
Test the four new classes: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Druid, Bard
"""

import pygame
import sys
from game_state import Game
from units.warlock import Warlock
from units.paladin import Paladin
from units.druid import Druid
from units.bard import Bard

def test_new_classes():
    """Test that all new classes work correctly"""
    print("🎮 TESTING NEW CLASSES")
    print("=" * 25)
    
    pygame.init()
    
    # Test 1: Class Creation and Basic Properties
    print("📋 TEST 1: Class Creation")
    print("-" * 25)
    
    game = Game()
    
    # Create all new classes
    warlock = Warlock(1)
    paladin = Paladin(1)
    druid = Druid(1)
    bard = Bard(1)
    
    new_classes = [warlock, paladin, druid, bard]
    
    creation_working = True
    for unit in new_classes:
        print(f"🔸 {unit.name}:")
        print(f"  Health: {unit.health}/{unit.max_health}")
        print(f"  AP: {unit.current_ap}/{unit.max_ap}")
        print(f"  Abilities: {len(unit.abilities)}")
        
        # Check if all abilities have required attributes
        for ability in unit.abilities:
            has_damage = hasattr(ability, 'damage')
            if not has_damage:
                print(f"  ❌ {ability.name} missing damage attribute")
                creation_working = False
        
        if creation_working:
            print(f"  ✅ All abilities have damage attribute")
    
    print(f"Class Creation: {'✅ WORKING' if creation_working else '❌ BROKEN'}")
    
    # Test 2: Movement Patterns
    print(f"\n📋 TEST 2: Movement Patterns")
    print("-" * 30)
    
    # Position units on board (use safe positions)
    positions = [(2, 2), (4, 4), (6, 6), (7, 7)]
    for i, unit in enumerate(new_classes):
        unit.position = positions[i]
        game.board.units[positions[i]] = unit
        unit.board = game.board
    
    movement_working = True
    for unit in new_classes:
        moves = unit.get_valid_moves(game.board)
        print(f"🔸 {unit.name} at {unit.position}:")
        print(f"  Valid moves: {len(moves)}")
        
        if len(moves) == 0:
            print(f"  ❌ No valid moves found")
            movement_working = False
        else:
            print(f"  ✅ Movement pattern working")
            # Show first few moves as examples
            example_moves = moves[:3]
            print(f"  Examples: {example_moves}")
    
    print(f"Movement Patterns: {'✅ WORKING' if movement_working else '❌ BROKEN'}")
    
    # Test 3: AP System Integration
    print(f"\n📋 TEST 3: AP System Integration")
    print("-" * 33)
    
    game.start_player_turn(1)
    game.current_player_ap = 10
    
    ap_working = True
    for unit in new_classes:
        # Reset acted flag
        unit.has_acted_this_turn = False
        
        # Get valid moves and attacks for safe testing
        valid_moves = unit.get_valid_moves(game.board)
        valid_attacks = unit.get_valid_attacks(game.board)

        # Try first action (should work)
        if valid_moves:
            first_action = unit.use_ability(0, valid_moves[0], game)
        else:
            first_action = False

        # Try second action (should fail)
        if valid_attacks:
            second_action = unit.use_ability(1, valid_attacks[0], game)
        else:
            second_action = unit.use_ability(0, (unit.position[0], unit.position[1]), game)  # Try invalid move
        
        one_action_enforced = first_action and not second_action
        
        print(f"🔸 {unit.name}:")
        print(f"  First action: {first_action}")
        print(f"  Second action: {second_action}")
        print(f"  One action enforced: {'✅' if one_action_enforced else '❌'}")
        
        if not one_action_enforced:
            ap_working = False
    
    print(f"AP System Integration: {'✅ WORKING' if ap_working else '❌ BROKEN'}")
    
    # Test 4: Special Abilities
    print(f"\n📋 TEST 4: Special Abilities")
    print("-" * 29)
    
    abilities_working = True
    
    # Test one special ability per class
    test_abilities = [
        (warlock, 2, "Life Drain"),
        (paladin, 2, "Lay on Hands"),
        (druid, 2, "Wild Shape"),
        (bard, 2, "Inspire")
    ]
    
    for unit, ability_idx, ability_name in test_abilities:
        print(f"🔸 Testing {unit.name} - {ability_name}:")
        
        # Reset for clean test
        unit.has_acted_this_turn = False
        game.current_player_ap = 10
        
        # Get targets
        targets = unit.get_ability_targets(ability_idx, game.board)
        print(f"  Available targets: {len(targets)}")
        
        if len(targets) > 0:
            # Try to use ability
            target = targets[0]
            success = unit.use_ability(ability_idx, target, game)
            print(f"  Ability execution: {'✅' if success else '❌'}")
            
            if not success:
                abilities_working = False
        else:
            print(f"  ⚠️ No targets available (expected for some abilities)")
    
    print(f"Special Abilities: {'✅ WORKING' if abilities_working else '❌ BROKEN'}")
    
    # Test 5: Configuration Integration
    print(f"\n📋 TEST 5: Configuration Integration")
    print("-" * 37)
    
    # Test configuration changes
    from menu_screens.new_config_menu import NewConfigMenu
    screen = pygame.display.set_mode((800, 600))
    clock = pygame.time.Clock()
    
    config_menu = NewConfigMenu(screen, clock)
    
    config_working = True
    
    # Check if new classes are in configuration
    for class_name in ["Warlock", "Paladin", "Druid", "Bard"]:
        if class_name not in config_menu.class_data:
            print(f"❌ {class_name} missing from class_data")
            config_working = False
        elif class_name not in config_menu.ability_data:
            print(f"❌ {class_name} missing from ability_data")
            config_working = False
        else:
            print(f"✅ {class_name} in configuration")
    
    # Test configuration changes
    if config_working:
        # Modify some values
        config_menu.class_data["Warlock"]["hp"] = 10
        config_menu.ability_data["Paladin"]["Divine Smite"]["damage"] = 5
        config_menu.ability_data["Druid"]["Call Lightning"]["damage"] = 4
        config_menu.ability_data["Bard"]["Shatter"]["damage"] = 6
        
        # Save configuration
        config_menu._save_configuration()
        
        # Reload and create new units
        from config_loader import reload_configuration
        reload_configuration()
        
        new_warlock = Warlock(1)
        new_paladin = Paladin(1)
        new_druid = Druid(1)
        new_bard = Bard(1)
        
        print(f"\nConfiguration changes applied:")
        print(f"  Warlock HP: {new_warlock.max_health} (expected: 10)")
        print(f"  Divine Smite Damage: {new_paladin.abilities[3].damage} (expected: 5)")
        print(f"  Call Lightning Damage: {new_druid.abilities[6].damage} (expected: 4)")
        print(f"  Shatter Damage: {new_bard.abilities[7].damage} (expected: 6)")
        
        config_changes_working = (new_warlock.max_health == 10 and 
                                 new_paladin.abilities[3].damage == 5 and
                                 new_druid.abilities[6].damage == 4 and
                                 new_bard.abilities[7].damage == 6)
        
        if not config_changes_working:
            config_working = False
    
    print(f"Configuration Integration: {'✅ WORKING' if config_working else '❌ BROKEN'}")
    
    pygame.quit()
    
    # Summary
    print(f"\n" + "=" * 25)
    print("🎯 NEW CLASSES TEST SUMMARY")
    print("-" * 27)
    print(f"Class Creation: {'✅ WORKING' if creation_working else '❌ BROKEN'}")
    print(f"Movement Patterns: {'✅ WORKING' if movement_working else '❌ BROKEN'}")
    print(f"AP System Integration: {'✅ WORKING' if ap_working else '❌ BROKEN'}")
    print(f"Special Abilities: {'✅ WORKING' if abilities_working else '❌ BROKEN'}")
    print(f"Configuration Integration: {'✅ WORKING' if config_working else '❌ BROKEN'}")
    
    all_working = (creation_working and movement_working and ap_working and 
                   abilities_working and config_working)
    
    if all_working:
        print(f"\n🎉 ALL NEW CLASSES SUCCESSFUL!")
        print("✅ Warlock - Dark magic specialist")
        print("✅ Paladin - Holy warrior")
        print("✅ Druid - Nature magic specialist")
        print("✅ Bard - Support and inspiration specialist")
        print("✅ All classes integrate with existing systems")
        print("✅ Configuration system supports new classes")
        return True
    else:
        print(f"\n❌ SOME ISSUES REMAIN!")
        if not creation_working:
            print("❌ Class creation issues")
        if not movement_working:
            print("❌ Movement pattern issues")
        if not ap_working:
            print("❌ AP system integration issues")
        if not abilities_working:
            print("❌ Special ability issues")
        if not config_working:
            print("❌ Configuration integration issues")
        return False

if __name__ == "__main__":
    success = test_new_classes()
    if success:
        print(f"\n🚀 NEW CLASSES READY!")
        print("Four new classes successfully added to the game!")
    else:
        print(f"\n🔧 NEEDS MORE WORK!")
        print("Check the errors above.")
    
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Test configuration priority system (static vs dynamic).
"""

import pygame
pygame.init()

from core.configuration_manager import get_config_manager
from units.warrior import Warrior

def test_config_priority():
    print("⚙️ TESTING CONFIGURATION PRIORITY SYSTEM")
    print("=" * 50)
    
    config_manager = get_config_manager()
    
    print("📋 Static Config (game_config.py):")
    static_warrior_health = config_manager.static_config.get("warrior_config", {}).get("health", "NOT_FOUND")
    print(f"  Warrior health: {static_warrior_health}")
    
    print("\n📋 Dynamic Config (game_balance_config.json):")
    dynamic_warrior_data = config_manager.dynamic_config.get("class_data", {}).get("Warrior", {})
    dynamic_warrior_health = dynamic_warrior_data.get("hp", "NOT_FOUND")
    print(f"  Warrior hp: {dynamic_warrior_health}")
    
    print("\n🎯 Final Configuration Used:")
    final_config = config_manager.get_unit_config("Warrior")
    final_health = final_config.get("health", "NOT_FOUND")
    print(f"  Warrior health: {final_health}")
    
    print("\n🧪 Actual Unit Creation:")
    warrior = Warrior(1)
    actual_health = warrior.health
    print(f"  Warrior actual health: {actual_health}")
    
    print("\n🔍 Analysis:")
    if static_warrior_health == actual_health:
        print("  ✅ Using STATIC config (game_config.py)")
        print("  📝 This means the options menu sliders haven't been used yet,")
        print("     or the JSON structure doesn't match the expected format.")
    elif dynamic_warrior_health == actual_health:
        print("  ✅ Using DYNAMIC config (game_balance_config.json)")
        print("  📝 This means the options menu sliders are working correctly!")
    else:
        print("  ❓ Configuration mismatch detected")
        print(f"     Static: {static_warrior_health}")
        print(f"     Dynamic: {dynamic_warrior_health}")
        print(f"     Actual: {actual_health}")

def check_json_structure():
    print(f"\n🔧 CHECKING JSON STRUCTURE COMPATIBILITY:")
    print("=" * 50)
    
    config_manager = get_config_manager()
    
    print("Expected structure for dynamic config:")
    print("  {")
    print("    'Warrior': { 'health': 10, 'max_ap': 6, ... },")
    print("    'Mage': { 'health': 8, 'max_ap': 8, ... }")
    print("  }")
    
    print(f"\nActual JSON structure:")
    print("  {")
    print("    'class_data': {")
    print("      'Warrior': { 'hp': 17, 'movement': 2 },")
    print("      'Mage': { 'hp': 20, 'movement': 2 }")
    print("    },")
    print("    'ability_data': { ... }")
    print("  }")
    
    print(f"\n📝 ISSUE IDENTIFIED:")
    print("  The JSON file uses 'class_data' wrapper and 'hp' instead of 'health'.")
    print("  The configuration manager expects direct class names as keys.")
    print("  This explains why static config is being used instead of dynamic config.")

if __name__ == "__main__":
    test_config_priority()
    check_json_structure()

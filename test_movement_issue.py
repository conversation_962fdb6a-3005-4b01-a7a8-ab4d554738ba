#!/usr/bin/env python3
"""
Test script to diagnose movement issues with <PERSON> and <PERSON>
"""

import pygame
from game_state import Game
from units.hunter import Hunter
from units.king import King
from units.warrior import Warrior

def test_movement_issue():
    """Test movement for units that might be having issues"""
    pygame.init()
    
    print("🚨 MOVEMENT ISSUE DIAGNOSIS 🚨")
    print("=" * 40)
    
    game = Game()
    
    # Test Hunter
    print("\n📋 TEST 1: Hunter Movement")
    print("-" * 25)
    
    hunter = Hunter(1)
    hunter.position = (4, 4)
    hunter.board = game.board
    game.board.units = {(4, 4): hunter}
    
    print(f"Hunter position: {hunter.position}")
    print(f"Hunter has passive_manager: {hasattr(hunter, 'passive_manager')}")
    print(f"Hunter current AP: {hunter.current_ap}")
    print(f"Hunter max AP: {hunter.max_ap}")
    
    # Check status effects
    print(f"Hunter immobilized: {hunter.immobilized}")
    print(f"Hunter stunned: {hunter.stunned}")
    
    if hasattr(hunter, 'status_manager'):
        print(f"Hunter can move (status): {hunter.status_manager.can_move()}")
    
    # Test movement
    try:
        moves = hunter.get_valid_moves(game.board)
        print(f"Valid moves: {len(moves)}")
        if moves:
            print(f"Example moves: {moves[:5]}")
        else:
            print("❌ NO VALID MOVES!")
    except Exception as e:
        print(f"❌ Error getting moves: {e}")
        import traceback
        traceback.print_exc()
    
    # Test King
    print("\n\n📋 TEST 2: King Movement")
    print("-" * 20)
    
    king = King(1)
    king.position = (5, 5)
    king.board = game.board
    game.board.units = {(4, 4): hunter, (5, 5): king}
    
    print(f"King position: {king.position}")
    print(f"King has passive_manager: {hasattr(king, 'passive_manager')}")
    print(f"King current AP: {king.current_ap}")
    print(f"King max AP: {king.max_ap}")
    
    # Check status effects
    print(f"King immobilized: {king.immobilized}")
    print(f"King stunned: {king.stunned}")
    
    if hasattr(king, 'status_manager'):
        print(f"King can move (status): {king.status_manager.can_move()}")
    
    # Test movement
    try:
        king_moves = king.get_valid_moves(game.board)
        print(f"Valid moves: {len(king_moves)}")
        if king_moves:
            print(f"Example moves: {king_moves[:5]}")
        else:
            print("❌ NO VALID MOVES!")
    except Exception as e:
        print(f"❌ Error getting moves: {e}")
        import traceback
        traceback.print_exc()
    
    # Test Warrior for comparison
    print("\n\n📋 TEST 3: Warrior Movement (Control)")
    print("-" * 35)
    
    warrior = Warrior(1)
    warrior.position = (6, 6)
    warrior.board = game.board
    game.board.units[(6, 6)] = warrior
    
    print(f"Warrior position: {warrior.position}")
    print(f"Warrior has passive_manager: {hasattr(warrior, 'passive_manager')}")
    print(f"Warrior current AP: {warrior.current_ap}")
    print(f"Warrior max AP: {warrior.max_ap}")
    
    try:
        warrior_moves = warrior.get_valid_moves(game.board)
        print(f"Valid moves: {len(warrior_moves)}")
        if warrior_moves:
            print(f"Example moves: {warrior_moves[:5]}")
        else:
            print("❌ NO VALID MOVES!")
    except Exception as e:
        print(f"❌ Error getting moves: {e}")
        import traceback
        traceback.print_exc()
    
    # Test abilities
    print("\n\n📋 TEST 4: Hunter Abilities")
    print("-" * 25)
    
    print(f"Hunter abilities count: {len(hunter.abilities)}")
    for i, ability in enumerate(hunter.abilities):
        print(f"  {i}: {ability.name}")
    
    # Test ability usage
    try:
        # Test basic attack
        targets = hunter.get_ability_targets(1, game.board)  # Basic attack
        print(f"Basic attack targets: {len(targets)}")
        if targets:
            print(f"Example targets: {targets[:3]}")
    except Exception as e:
        print(f"❌ Error getting ability targets: {e}")
        import traceback
        traceback.print_exc()
    
    # Test AP usage
    print("\n\n📋 TEST 5: AP and Action Tests")
    print("-" * 30)
    
    print(f"Hunter can use ability (index 0): {hunter.can_use_ability(0)}")
    print(f"Hunter can use ability (index 1): {hunter.can_use_ability(1)}")
    
    # Test movement ability specifically
    try:
        move_targets = hunter.get_ability_targets(0, game.board)  # Move ability
        print(f"Move ability targets: {len(move_targets)}")
        if move_targets:
            print(f"Example move targets: {move_targets[:5]}")
    except Exception as e:
        print(f"❌ Error getting move targets: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 40)
    print("🔍 DIAGNOSIS COMPLETE")
    print("\nIf units show 0 valid moves, check:")
    print("  • Status effects (immobilized/stunned)")
    print("  • AP availability")
    print("  • Board state conflicts")
    print("  • Passive system integration issues")

if __name__ == "__main__":
    test_movement_issue()

import pygame
from units_core import Unit, SimpleAbility, MoveAbility, AttackAbility
from game_config import GAME_CONFIG
import game_constants as const
import math

class Druid(Unit):
    """
    Druid unit class - Nature magic specialist with shapeshifting and environmental control.
    
    Druids are versatile nature magic users who can shapeshift, control the battlefield
    through environmental effects, and provide healing and support to allies.
    
    Movement:
        - Pattern: All directions (8-directional like King)
        - Range: 2 tiles in any direction
        - Can move through forest/nature terrain without penalty
        
    Abilities (8 total):
        0. Move - 8-directional movement (2 tiles)
        1. Basic Attack - Nature bolt (ranged)
        2. Wild Shape - Transform for enhanced abilities
        3. Entangle - Root enemies in place
        4. Healing Spring - Create healing area
        5. <PERSON> Barrier - Create damaging wall
        6. Call Lightning - Area damage spell
        7. Nature's Wrath - Powerful nature attack
        
    Default Passive:
        - Nature's Bond: Heal 1 HP at start of turn if on nature terrain
        
    Tactical Role:
        - Battlefield control specialist
        - Versatile support/damage hybrid
        - Environmental manipulation
    """
    def __init__(self, player_id):
        super().__init__(player_id, health=GAME_CONFIG.get("druid_config", {}).get("health", 7), max_health=GAME_CONFIG.get("druid_config", {}).get("health", 7))
        self.name = "Druid"
        self.max_ap = GAME_CONFIG.get("druid_config", {}).get("max_ap", 7)
        self.current_ap = GAME_CONFIG.get("druid_config", {}).get("max_ap", 7)
        self.board = None
        
        # Druid-specific state
        self.wild_shape_active = False
        self.wild_shape_turns = 0
        self.wild_shape_form = None  # "bear", "wolf", "eagle"
        self.healing_springs = {}  # Track healing spring positions
        self.thorn_barriers = set()  # Track thorn barrier positions
        
        # Load image (green/brown nature theme)
        self.image = self._create_placeholder_image((34, 139, 34) if player_id == 1 else (107, 142, 35))
        
        # Abilities
        self.abilities = [
            MoveAbility(self),
            AttackAbility(self),
            SimpleAbility("Wild Shape", GAME_CONFIG.get("druid_config", {}).get("wild_shape_ap_cost", 3), "Transform for 3 turns", cooldown=4, owner=self),
            SimpleAbility("Entangle", GAME_CONFIG.get("druid_config", {}).get("entangle_ap_cost", 2), "Root target for 2 turns", cooldown=2, owner=self),
            SimpleAbility("Healing Spring", GAME_CONFIG.get("druid_config", {}).get("healing_spring_ap_cost", 3), "Create healing area", cooldown=3, owner=self),
            SimpleAbility("Thorn Barrier", GAME_CONFIG.get("druid_config", {}).get("thorn_barrier_ap_cost", 3), "Create damaging wall", cooldown=3, owner=self),
            SimpleAbility("Call Lightning", GAME_CONFIG.get("druid_config", {}).get("call_lightning_ap_cost", 4), "Area lightning damage", cooldown=3, owner=self),
            SimpleAbility("Nature's Wrath", GAME_CONFIG.get("druid_config", {}).get("natures_wrath_ap_cost", 4), "Powerful nature attack", cooldown=2, owner=self)
        ]
        
        # Add healing amounts to healing abilities
        self.abilities[4].heal_amount = 2  # Healing Spring
        
        # Apply configuration from balance system
        try:
            from config_loader import apply_config_to_unit
            apply_config_to_unit(self)
        except ImportError:
            pass  # Config loader not available
    
    def _create_placeholder_image(self, color):
        """Create a placeholder image with nature symbols"""
        surf = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA)
        
        # Main circle (nature theme)
        pygame.draw.circle(surf, color, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2)
        pygame.draw.circle(surf, (139, 69, 19), (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2, 2)
        
        # Nature symbol (leaf/tree)
        center_x, center_y = const.CELL_SIZE//2, const.CELL_SIZE//2
        symbol_color = (0, 100, 0)
        
        # Draw leaf/tree symbol
        pygame.draw.circle(surf, symbol_color, (center_x, center_y - 4), 6)
        pygame.draw.rect(surf, (139, 69, 19), (center_x - 1, center_y + 2, 2, 8))
        
        return surf
    
    def get_valid_moves(self, board):
        """Druid moves 2 tiles in any direction (8-directional)"""
        self.board = board
        # Check if unit can move (status effects)
        if hasattr(self, 'status_manager') and not self.status_manager.can_move():
            return []
        if self.immobilized or self.stunned:
            return []
        
        valid_moves = []
        row, col = self.position
        
        # All 8 directions
        all_directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1), (0, 1), (1, -1), (1, 0), (1, 1)]
        
        for dr, dc in all_directions:
            # Move up to 2 tiles in each direction
            for distance in range(1, 3):  # 1 and 2 tiles
                new_r, new_c = row + dr * distance, col + dc * distance
                
                # Check if position is on board
                if 0 <= new_r < const.BOARD_SIZE and 0 <= new_c < const.BOARD_SIZE:
                    # Check if position is empty
                    if (new_r, new_c) not in board.units:
                        valid_moves.append((new_r, new_c))
                    else:
                        # Can't move through units, stop checking further in this direction
                        break
                else:
                    # Off board, stop checking further in this direction
                    break
        
        return valid_moves
    
    def get_valid_attacks(self, board):
        """Druid basic attack: ranged nature bolt (3 tile range)"""
        self.board = board
        if self.stunned:
            return []
        
        valid_attacks = []
        row, col = self.position
        
        # Check all positions within 3 tile range
        for r in range(max(0, row - 3), min(const.BOARD_SIZE, row + 4)):
            for c in range(max(0, col - 3), min(const.BOARD_SIZE, col + 4)):
                if (r, c) == self.position:
                    continue
                
                # Calculate distance
                distance = max(abs(r - row), abs(c - col))
                if distance <= 3 and (r, c) in board.units:
                    target_unit = board.units[(r, c)]
                    if not target_unit.sanctuary:  # Can target any unit (friendly fire)
                        valid_attacks.append((r, c))
        
        return valid_attacks
    
    def get_ability_targets(self, ability_idx, board):
        """Get valid targets for Druid abilities"""
        self.board = board
        if ability_idx == 0:
            return self.get_valid_moves(board)
        if ability_idx == 1:
            return self.get_valid_attacks(board)
        
        ability_name = self.abilities[ability_idx].name
        
        if ability_name == "Wild Shape":
            # Self-target ability
            return [self.position]
        elif ability_name in ["Entangle", "Nature's Wrath"]:
            # Enemy-targeting abilities (3 tile range)
            return self._get_ranged_targets(board, range_limit=3)
        elif ability_name in ["Healing Spring", "Thorn Barrier"]:
            # Ground-targeting abilities (adjacent)
            return self._get_adjacent_empty_spaces(board)
        elif ability_name == "Call Lightning":
            # Area targeting (3 tile range)
            return self._get_area_targets(board, range_limit=3)
        
        return []
    
    def _get_ranged_targets(self, board, range_limit=3):
        """Get targets within range for ranged abilities"""
        targets = []
        row, col = self.position
        
        # Check all positions within range
        for r in range(max(0, row - range_limit), min(const.BOARD_SIZE, row + range_limit + 1)):
            for c in range(max(0, col - range_limit), min(const.BOARD_SIZE, col + range_limit + 1)):
                if (r, c) == self.position:
                    continue
                
                # Calculate distance
                distance = max(abs(r - row), abs(c - col))
                if distance <= range_limit and (r, c) in board.units:
                    target_unit = board.units[(r, c)]
                    if not target_unit.sanctuary:  # Can target any unit (friendly fire)
                        targets.append((r, c))
        
        return targets
    
    def _get_adjacent_empty_spaces(self, board):
        """Get adjacent empty spaces for ground-targeting abilities"""
        empty_spaces = []
        row, col = self.position
        
        # Check all adjacent positions
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:
                    continue
                
                r, c = row + dr, col + dc
                
                if (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and
                    (r, c) not in board.units):
                    empty_spaces.append((r, c))
        
        return empty_spaces
    
    def _get_area_targets(self, board, range_limit=3):
        """Get all positions within range for area abilities"""
        targets = []
        row, col = self.position
        
        # Check all positions within range (including empty ones)
        for r in range(max(0, row - range_limit), min(const.BOARD_SIZE, row + range_limit + 1)):
            for c in range(max(0, col - range_limit), min(const.BOARD_SIZE, col + range_limit + 1)):
                if (r, c) == self.position:
                    continue
                
                # Calculate distance
                distance = max(abs(r - row), abs(c - col))
                if distance <= range_limit:
                    targets.append((r, c))
        
        return targets
    
    def use_ability(self, ability_idx, target_pos, game=None):
        """Use Druid ability with global AP system"""
        if game:
            self.board = game.board
        elif not self.board:
            print(f"ERROR in {self.name}.use_ability: game object not passed and self.board not set.")
            if ability_idx > 1:
                print(f"Cannot use special ability {self.abilities[ability_idx].name} without board context.")
                return False
        
        # Standard move or attack - call super() to use the updated base class logic
        if ability_idx == 0:  # Move
            return super().use_ability(ability_idx, target_pos, game)
        
        if ability_idx == 1:  # Basic Attack
            return super().use_ability(ability_idx, target_pos, game)
        
        # For other Druid-specific abilities - use global AP system
        return super().use_ability(ability_idx, target_pos, game)
    
    def _execute_druid_ability(self, ability, target_pos, game):
        """Execute Druid-specific abilities"""
        ability_name = ability.name
        
        if ability_name == "Wild Shape":
            return self._use_wild_shape(ability, game)
        elif ability_name == "Entangle":
            return self._use_entangle(ability, target_pos)
        elif ability_name == "Healing Spring":
            return self._use_healing_spring(ability, target_pos, game)
        elif ability_name == "Thorn Barrier":
            return self._use_thorn_barrier(ability, target_pos)
        elif ability_name == "Call Lightning":
            return self._use_call_lightning(ability, target_pos)
        elif ability_name == "Nature's Wrath":
            return self._use_natures_wrath(ability, target_pos)
        else:
            print(f"Unknown Druid ability: {ability_name}")
            return False
    
    def _use_wild_shape(self, ability, game):
        """Transform into animal form"""
        if self.wild_shape_active:
            print(f"{self.name} is already in wild shape!")
            return False
        
        # Choose form based on situation (could be player choice in full implementation)
        import random
        forms = ["bear", "wolf", "eagle"]
        self.wild_shape_form = random.choice(forms)
        self.wild_shape_active = True
        self.wild_shape_turns = 3
        
        # Apply form bonuses
        if self.wild_shape_form == "bear":
            self.max_health += 3
            self.health += 3
            print(f"{self.name} transforms into a bear (+3 HP, enhanced melee)")
        elif self.wild_shape_form == "wolf":
            print(f"{self.name} transforms into a wolf (+1 movement, pack tactics)")
        elif self.wild_shape_form == "eagle":
            print(f"{self.name} transforms into an eagle (flight, enhanced range)")
        
        return True
    
    def _use_entangle(self, ability, target_pos):
        """Root target in place"""
        target_unit = self.board.units.get(target_pos)
        if target_unit:
            damage = ability.damage if ability.damage > 0 else 1
            target_unit.take_damage(damage, self)
            
            # Apply entangle effect (immobilized for 2 turns)
            if hasattr(target_unit, 'status_manager'):
                target_unit.status_manager.apply_effect("entangled", 2, self.player_id)
            else:
                target_unit.immobilized = True
                target_unit.immobilized_turns = 2
            
            print(f"{target_unit.name} is entangled by roots and cannot move for 2 turns")
            return True
        return False
    
    def _use_healing_spring(self, ability, target_pos, game):
        """Create healing area"""
        # Create healing spring at target position
        self.healing_springs[target_pos] = {
            'turns_remaining': 4,
            'heal_amount': getattr(ability, 'heal_amount', 2)
        }
        
        print(f"{self.name} creates a healing spring at {target_pos} - heals allies for 4 turns")
        return True
    
    def _use_thorn_barrier(self, ability, target_pos):
        """Create damaging wall"""
        # Create thorn barrier at target position
        self.thorn_barriers.add(target_pos)
        
        print(f"{self.name} creates a thorn barrier at {target_pos} - damages enemies who enter")
        return True
    
    def _use_call_lightning(self, ability, target_pos):
        """Area lightning damage"""
        damage = ability.damage if ability.damage > 0 else 3
        targets_hit = 0
        
        # Damage all units in 3x3 area around target
        target_row, target_col = target_pos
        
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                r, c = target_row + dr, target_col + dc
                
                if (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and
                    (r, c) in self.board.units):
                    unit = self.board.units[(r, c)]
                    if not unit.sanctuary:
                        unit.take_damage(damage, self)
                        targets_hit += 1
                        print(f"  Lightning strikes {unit.name} for {damage} damage")
        
        print(f"{self.name} calls lightning at {target_pos}, hitting {targets_hit} targets")
        return targets_hit > 0
    
    def _use_natures_wrath(self, ability, target_pos):
        """Powerful nature attack"""
        target_unit = self.board.units.get(target_pos)
        if target_unit:
            damage = ability.damage if ability.damage > 0 else 4
            
            # Extra damage if Druid is in wild shape
            if self.wild_shape_active:
                damage += 2
                print(f"Nature's Wrath enhanced by wild shape!")
            
            target_unit.take_damage(damage, self)
            print(f"{self.name} unleashes nature's wrath on {target_unit.name} for {damage} damage")
            return True
        return False
    
    def reset_turn(self, game=None):
        """Reset turn-specific effects"""
        super().reset_turn(game=game)
        
        # Update wild shape
        if self.wild_shape_active:
            self.wild_shape_turns -= 1
            if self.wild_shape_turns <= 0:
                self._end_wild_shape()
        
        # Update healing springs
        expired_springs = []
        for pos, data in self.healing_springs.items():
            data['turns_remaining'] -= 1
            if data['turns_remaining'] <= 0:
                expired_springs.append(pos)
            else:
                # Heal any ally on the spring
                if pos in self.board.units:
                    unit = self.board.units[pos]
                    if unit.player_id == self.player_id:
                        old_health = unit.health
                        unit.health = min(unit.max_health, unit.health + data['heal_amount'])
                        actual_heal = unit.health - old_health
                        if actual_heal > 0:
                            print(f"{unit.name} heals {actual_heal} HP from healing spring")
        
        for pos in expired_springs:
            del self.healing_springs[pos]
            print(f"Healing spring at {pos} expires")
    
    def _end_wild_shape(self):
        """End wild shape transformation"""
        if self.wild_shape_form == "bear":
            # Remove bear bonuses
            self.max_health -= 3
            self.health = min(self.health, self.max_health)
        
        self.wild_shape_active = False
        self.wild_shape_form = None
        print(f"{self.name} returns to normal form")

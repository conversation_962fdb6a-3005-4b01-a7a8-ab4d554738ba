#!/usr/bin/env python3
"""
Test script for Rogue's new Fan of Knives ability and fixed knight movement
"""

import pygame
from game_state import Game
from units.rogue import Rogue
from units.hunter import Hunter
from units.warrior import Warrior

def test_rogue_fan_of_knives():
    """Test the new Fan of Knives ability"""
    pygame.init()
    
    print("🗡️ TESTING ROGUE FAN OF KNIVES 🗡️")
    print("=" * 40)
    
    game = Game()
    
    # Create rogue and some targets
    rogue = Rogue(1)
    hunter1 = Hunter(2)  # Enemy
    hunter2 = Hunter(2)  # Enemy
    warrior = Warrior(2)  # Enemy
    
    # Position rogue in center
    rogue.position = (4, 4)
    rogue.board = game.board
    
    # Position enemies at knight-move positions
    hunter1.position = (2, 3)  # Knight move: up 2, left 1
    hunter2.position = (3, 2)  # Knight move: up 1, left 2
    warrior.position = (6, 5)  # Knight move: down 2, right 1
    
    # Set up board
    game.board.units = {
        (4, 4): rogue,
        (2, 3): hunter1,
        (3, 2): hunter2,
        (6, 5): warrior
    }
    
    print(f"Setup:")
    print(f"  Rogue at {rogue.position}")
    print(f"  Hunter1 at {hunter1.position} (HP: {hunter1.health})")
    print(f"  Hunter2 at {hunter2.position} (HP: {hunter2.health})")
    print(f"  Warrior at {warrior.position} (HP: {warrior.health})")
    
    # Test Fan of Knives ability
    print(f"\n📋 Testing Fan of Knives Ability")
    print("-" * 30)
    
    # Check if ability exists
    fan_ability_idx = None
    for i, ability in enumerate(rogue.abilities):
        if ability.name == "Fan of Knives":
            fan_ability_idx = i
            break
    
    if fan_ability_idx is None:
        print("❌ Fan of Knives ability not found!")
        return
    
    print(f"✅ Fan of Knives found at index {fan_ability_idx}")
    print(f"   AP cost: {rogue.abilities[fan_ability_idx].ap_cost}")
    print(f"   Cooldown: {rogue.abilities[fan_ability_idx].cooldown}")
    
    # Check targeting
    targets = rogue.get_ability_targets(fan_ability_idx, game.board)
    print(f"   Targets: {targets}")
    
    # Check if rogue can use the ability
    print(f"   Rogue AP: {rogue.current_ap}/{rogue.max_ap}")
    print(f"   Can use: {rogue.can_use_ability(fan_ability_idx)}")
    
    # Use the ability
    print(f"\n🗡️ Using Fan of Knives...")
    result = rogue.use_ability(fan_ability_idx, rogue.position, game)
    print(f"   Result: {result}")
    
    # Check damage dealt
    print(f"\n📊 After Fan of Knives:")
    print(f"  Hunter1 HP: {hunter1.health} (was {Hunter(2).health})")
    print(f"  Hunter2 HP: {hunter2.health} (was {Hunter(2).health})")
    print(f"  Warrior HP: {warrior.health} (was {Warrior(2).health})")
    print(f"  Rogue AP: {rogue.current_ap}/{rogue.max_ap}")

def test_rogue_knight_movement():
    """Test the fixed knight movement system"""
    pygame.init()
    
    print("\n\n♞ TESTING ROGUE KNIGHT MOVEMENT ♞")
    print("=" * 40)
    
    game = Game()
    rogue = Rogue(1)
    rogue.position = (4, 4)
    rogue.board = game.board
    game.board.units = {(4, 4): rogue}
    
    print(f"Rogue at {rogue.position}")
    print(f"Movement range config: {rogue.abilities[0].ap_cost}")
    
    # Test movement
    moves = rogue.get_valid_moves(game.board)
    print(f"\nValid moves: {len(moves)}")
    
    # Categorize moves by distance
    move_distances = {}
    for move in moves:
        dr = abs(move[0] - 4)
        dc = abs(move[1] - 4)
        distance = max(dr, dc)  # Chebyshev distance
        if distance not in move_distances:
            move_distances[distance] = []
        move_distances[distance].append(move)
    
    print(f"\nMoves by distance:")
    for distance in sorted(move_distances.keys()):
        print(f"  Distance {distance}: {len(move_distances[distance])} moves")
        print(f"    Examples: {move_distances[distance][:3]}")
    
    # Test specific knight moves
    expected_knight_moves = [
        (2, 3), (2, 5),  # Up 2, left/right 1
        (3, 2), (3, 6),  # Up 1, left/right 2
        (5, 2), (5, 6),  # Down 1, left/right 2
        (6, 3), (6, 5)   # Down 2, left/right 1
    ]
    
    print(f"\n🔍 Checking basic knight moves:")
    for expected_move in expected_knight_moves:
        if expected_move in moves:
            print(f"  ✅ {expected_move}")
        else:
            print(f"  ❌ {expected_move} (missing)")

def test_rogue_abilities_list():
    """Test that all Rogue abilities are present"""
    pygame.init()
    
    print("\n\n📋 TESTING ROGUE ABILITIES LIST 📋")
    print("=" * 40)
    
    rogue = Rogue(1)
    
    expected_abilities = [
        "Move", "Basic Attack", "Backstab", "Poison Strike", 
        "Smoke Bomb", "Shadow Step", "Fan of Knives", "Assassination"
    ]
    
    print(f"Rogue has {len(rogue.abilities)} abilities:")
    for i, ability in enumerate(rogue.abilities):
        print(f"  {i}: {ability.name} (Cost: {ability.ap_cost}, Cooldown: {ability.cooldown})")
    
    print(f"\n🔍 Checking expected abilities:")
    for expected in expected_abilities:
        found = any(ability.name == expected for ability in rogue.abilities)
        if found:
            print(f"  ✅ {expected}")
        else:
            print(f"  ❌ {expected} (missing)")

if __name__ == "__main__":
    test_rogue_abilities_list()
    test_rogue_knight_movement()
    test_rogue_fan_of_knives()
    
    print("\n" + "=" * 40)
    print("🎯 ROGUE TESTING COMPLETE!")
    print("\nKey Features:")
    print("• Fan of Knives: Area attack at knight-move positions")
    print("• Knight Movement: L-shaped moves with configurable range")
    print("• All abilities: 8 total including new Fan of Knives")
    print("• AP Cost: 2 for Fan of Knives")
    print("• Cooldown: 2 turns for Fan of Knives")

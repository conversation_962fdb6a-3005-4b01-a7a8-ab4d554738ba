import os
import sys
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox
import webbrowser
import traceback

class GameLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("Tactical PvP Strategy Launcher")
        self.root.geometry("600x400")
        self.root.resizable(False, False)
        
        # Set icon if available
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
        
        # Configure style
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.style.configure('TButton', font=('Arial', 12), padding=10)
        self.style.configure('TLabel', font=('Arial', 14))
        self.style.configure('Title.TLabel', font=('Arial', 24, 'bold'))
        
        # Create main frame
        self.main_frame = ttk.Frame(self.root, padding=20)
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        self.title_label = ttk.Label(
            self.main_frame, 
            text="Tactical PvP Strategy", 
            style='Title.TLabel'
        )
        self.title_label.pack(pady=(0, 20))
        
        # Game description
        self.desc_text = """A turn-based tactical strategy game where two players compete 
on a 9x9 chess-like board with unique units and abilities."""
        self.desc_label = ttk.Label(
            self.main_frame, 
            text=self.desc_text,
            wraplength=500,
            justify=tk.CENTER
        )
        self.desc_label.pack(pady=(0, 30))
        
        # Buttons frame
        self.button_frame = ttk.Frame(self.main_frame)
        self.button_frame.pack(fill=tk.X, pady=10)
        
        # Play button
        self.play_button = ttk.Button(
            self.button_frame,
            text="Play Game",
            command=self.play_game,
            width=20
        )
        self.play_button.pack(pady=10)
        
        # Options button
        self.options_button = ttk.Button(
            self.button_frame,
            text="Options",
            command=self.show_options,
            width=20
        )
        self.options_button.pack(pady=10)
        
        # Help button
        self.help_button = ttk.Button(
            self.button_frame,
            text="Help",
            command=self.show_help,
            width=20
        )
        self.help_button.pack(pady=10)
        
        # Exit button
        self.exit_button = ttk.Button(
            self.button_frame,
            text="Exit",
            command=self.root.destroy,
            width=20
        )
        self.exit_button.pack(pady=10)
        
        # Version info
        self.version_label = ttk.Label(
            self.main_frame,
            text="Version 1.0.0",
            font=('Arial', 10)
        )
        self.version_label.pack(side=tk.BOTTOM, pady=(20, 0))
        
        # Check for Python and dependencies
        self.check_dependencies()
    
    def check_dependencies(self):
        """Check if Python and required packages are installed"""
        try:
            # Check if pygame is installed
            import pygame
            print("Pygame is installed.")
        except ImportError:
            if messagebox.askyesno(
                "Missing Dependencies", 
                "Pygame is not installed. Would you like to install it now?"
            ):
                try:
                    subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
                    messagebox.showinfo("Success", "Dependencies installed successfully!")
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to install dependencies: {str(e)}")
    
    def play_game(self):
        """Launch the game"""
        try:
            # Get the current directory to ensure the game runs from the correct location
            current_dir = os.path.dirname(os.path.abspath(__file__))
            
            # Hide the launcher window
            self.root.withdraw()
            
            # Check if the game files exist
            main_menu_path = os.path.join(current_dir, "main_menu.py")
            if not os.path.exists(main_menu_path):
                raise FileNotFoundError(f"Game file not found: {main_menu_path}")
            
            # Print debug info
            print(f"Launching game from directory: {current_dir}")
            print(f"Using Python: {sys.executable}")
            print(f"Game file: {main_menu_path}")
            
            # Start the game process
            process = subprocess.Popen(
                [sys.executable, main_menu_path],
                cwd=current_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # Wait for the game to finish
            stdout, stderr = process.communicate()
            
            # If there was an error, log it
            if process.returncode != 0:
                print("Game process failed with return code:", process.returncode)
                print("STDOUT:", stdout)
                print("STDERR:", stderr)
                messagebox.showerror("Error", f"Game crashed. Error: {stderr}")
            
            # Show the launcher window again
            self.root.deiconify()
            
        except Exception as e:
            error_details = traceback.format_exc()
            messagebox.showerror("Error", f"Failed to start the game: {str(e)}\n\nDetails:\n{error_details}")
            self.root.deiconify()
    
    def show_options(self):
        """Show options dialog"""
        options_window = tk.Toplevel(self.root)
        options_window.title("Game Options")
        options_window.geometry("400x300")
        options_window.transient(self.root)
        options_window.grab_set()
        
        # Create options frame
        options_frame = ttk.Frame(options_window, padding=20)
        options_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        options_title = ttk.Label(
            options_frame,
            text="Game Options",
            style='Title.TLabel'
        )
        options_title.pack(pady=(0, 20))
        
        # Resolution option
        resolution_frame = ttk.Frame(options_frame)
        resolution_frame.pack(fill=tk.X, pady=10)
        
        resolution_label = ttk.Label(
            resolution_frame,
            text="Resolution:"
        )
        resolution_label.pack(side=tk.LEFT, padx=(0, 10))
        
        resolution_var = tk.StringVar(value="1280x720")
        resolution_combo = ttk.Combobox(
            resolution_frame,
            textvariable=resolution_var,
            values=["1280x720", "1366x768", "1600x900", "1920x1080"],
            state="readonly",
            width=15
        )
        resolution_combo.pack(side=tk.LEFT)
        
        # Fullscreen option
        fullscreen_var = tk.BooleanVar(value=False)
        fullscreen_check = ttk.Checkbutton(
            options_frame,
            text="Fullscreen",
            variable=fullscreen_var
        )
        fullscreen_check.pack(anchor=tk.W, pady=10)
        
        # Sound option
        sound_var = tk.BooleanVar(value=True)
        sound_check = ttk.Checkbutton(
            options_frame,
            text="Sound Effects",
            variable=sound_var
        )
        sound_check.pack(anchor=tk.W, pady=10)
        
        # Music option
        music_var = tk.BooleanVar(value=True)
        music_check = ttk.Checkbutton(
            options_frame,
            text="Music",
            variable=music_var
        )
        music_check.pack(anchor=tk.W, pady=10)
        
        # Buttons
        button_frame = ttk.Frame(options_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        save_button = ttk.Button(
            button_frame,
            text="Save",
            command=lambda: self.save_options(
                resolution_var.get(),
                fullscreen_var.get(),
                sound_var.get(),
                music_var.get(),
                options_window
            )
        )
        save_button.pack(side=tk.LEFT, padx=5)
        
        cancel_button = ttk.Button(
            button_frame,
            text="Cancel",
            command=options_window.destroy
        )
        cancel_button.pack(side=tk.LEFT, padx=5)
    
    def save_options(self, resolution, fullscreen, sound, music, window):
        """Save options to a config file"""
        try:
            with open("game_config.py", "a") as f:
                f.write(f"resolution={resolution}\n")
                f.write(f"fullscreen={fullscreen}\n")
                f.write(f"sound={sound}\n")
                f.write(f"music={music}\n")
            
            messagebox.showinfo("Success", "Options saved successfully!")
            window.destroy()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to save options: {str(e)}")
    
    def show_help(self):
        """Show help dialog with game instructions"""
        help_window = tk.Toplevel(self.root)
        help_window.title("Game Help")
        help_window.geometry("500x400")
        help_window.transient(self.root)
        help_window.grab_set()
        
        # Create help frame with scrollbar
        help_frame = ttk.Frame(help_window)
        help_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        help_title = ttk.Label(
            help_frame,
            text="Game Help",
            style='Title.TLabel'
        )
        help_title.pack(pady=(0, 20))
        
        # Create canvas and scrollbar
        canvas = tk.Canvas(help_frame)
        scrollbar = ttk.Scrollbar(help_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Help content
        help_text = """
How to Play

1. Setup Phase:
   - Each player takes turns placing their units on their side of the board.
   - Player 1 places units on the bottom 3 rows, Player 2 on the top 3 rows.
   - Select a unit type from the right panel, then click on the board to place it.

2. Gameplay Phase:
   - Players take turns moving and attacking with their units.
   - Click on your unit to select it, then click on a highlighted square to move or attack.
   - When finished with your turn, click the "End Turn" button.

3. Victory Condition:
   - Eliminate all of your opponent's units to win.

Unit Types

1. Warrior:
   - Melee unit with high health
   - Moves and attacks in adjacent squares (including diagonals)

2. Archer:
   - Ranged unit with medium health
   - Moves 1-2 squares horizontally or vertically
   - Attacks targets 2-4 squares away with line-of-sight

3. Mage:
   - Spell caster with low health
   - Moves 1 square in any direction
   - Can attack enemies up to 3 squares away

4. Knight:
   - Mobile unit with L-shaped movement pattern (like a chess knight)
   - Can jump over other units
   - Attacks in the same L-shaped pattern

Controls

- Left-click: Select units, move, attack, and interact with buttons
- Any key: Return to main menu after a game ends
        """
        
        help_content = ttk.Label(
            scrollable_frame,
            text=help_text,
            wraplength=450,
            justify=tk.LEFT
        )
        help_content.pack(pady=10)
        
        # Close button
        close_button = ttk.Button(
            help_frame,
            text="Close",
            command=help_window.destroy
        )
        close_button.pack(pady=10)

def main():
    root = tk.Tk()
    app = GameLauncher(root)
    root.mainloop()

if __name__ == "__main__":
    main()

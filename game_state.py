import pygame
from game_board import GameBoard
# Assuming game_units.py will be refactored to not be a direct dependency for specific units here,
# or units are passed in/configured. For now, keep original imports if they were used by __init__ directly.
# from game_units import <PERSON>, <PERSON>, <PERSON>, Pawn, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ge # Potentially remove if not directly instantiated
from game_config import GAME_CONFIG
from game_settings import SELECTED_ABILITIES, SELECTED_PASSIVES # This is a global, might be better passed to Game or handled by AbilitySelectionMenu
from passive_system import PASSIVE_ABILITIES, PassiveType
import game_ui # For update_ability_buttons call in init
import game_logic # Potentially for some state-related helpers if any were in Game class
import game_visuals # For board_to_screen if it stays as a method
import game_constants as const # Import constants
from game_constants import STATE_SETUP, STATE_PLAYING, STATE_GAME_OVER

# Constants that were in game.py, might be better in game_constants.py or settings
# WINDOW_WIDTH = 1280
# WINDOW_HEIGHT = 720
# FPS = 60 # FPS is more for the loop, not state

# Colors - these could also be in a constants/settings file
# DARK_GRAY = (30, 30, 30)
# LIGHT_GRAY = (200, 200, 200)
# BLUE = (0, 100, 200)
# RED = (200, 60, 60)
# GREEN = (60, 180, 75)
# YELLOW = (255, 200, 0)

class Game:
    def __init__(self, fullscreen=False):
        # Pygame.init() should ideally be called ONCE at the application entry point (e.g. launcher.py)
        # If main_menu.py calls it, and launcher calls main_menu, that's the one.
        # For now, assuming it might be okay here if game can be run standalone via game.py
        if not pygame.get_init(): # Initialize only if not already initialized
            pygame.init()
            
        flags = pygame.FULLSCREEN if fullscreen else 0
        self.screen = pygame.display.set_mode((const.WINDOW_WIDTH, const.WINDOW_HEIGHT), flags) # Use const
        pygame.display.set_caption("Tactical PvP Strategy")
        self.clock = pygame.time.Clock()
        
        self.title_font = pygame.font.Font(const.FONT_DEFAULT_NAME, const.FONT_SIZE_TITLE) # Use const
        self.ui_font = pygame.font.Font(const.FONT_DEFAULT_NAME, const.FONT_SIZE_UI)       # Use const
        self.small_font = pygame.font.Font(const.FONT_DEFAULT_NAME, const.FONT_SIZE_SMALL) # Use const
        self.tiny_font = pygame.font.Font(const.FONT_DEFAULT_NAME, const.FONT_SIZE_TINY)   # Use const
        
        self.board = GameBoard(self.screen) 
        
        self.state = const.STATE_SETUP # Use const
        self.current_player = 1
        self.winner = None
        self.turn_count = 1

        # NEW BALANCED AP SYSTEM (1 → 10, +1 per turn)
        self.base_ap = 1
        self.ap_increment = 1
        self.max_ap = 10
        self.current_player_ap = 1
        self.units_acted_this_turn = set()

        # First player advantage mitigation
        self.player2_early_bonus = 1  # Extra AP for P2 in early turns

        # BALANCE SLIDER SYSTEM
        from balance_slider_system import BalanceSliderSystem
        self.balance_sliders = BalanceSliderSystem()
        self.show_balance_ui = False  # Toggle with F1 key

        self.setup_phase = 1 # Specific to STATE_SETUP
        self.selected_unit_type = None # Specific to STATE_SETUP
        self.available_units = { # This could be loaded from config
            1: {"Warrior": 999, "Hunter": 999, "Rogue": 999, "Pawn": 999, "King": 1, "Cleric": 999, "Mage": 999, "Warlock": 999, "Paladin": 999, "Druid": 999, "Bard": 999},
            2: {"Warrior": 999, "Hunter": 999, "Rogue": 999, "Pawn": 999, "King": 1, "Cleric": 999, "Mage": 999, "Warlock": 999, "Paladin": 999, "Druid": 999, "Bard": 999}
        }
        
        self.selected_unit = None
        self.valid_moves = []
        self.valid_attacks = []
        self.selected_ability = None
        self.valid_ability_targets = []
        self.ability_buttons = [] # Managed by game_ui.update_ability_buttons
        
        # Multi-stage and directional ability attributes
        self.multi_stage_ability = False
        self.ability_stage = 0
        self.ability_targets = []
        self.directional_ability = False
        self.hover_pos = None # Grid position tuple (row, col)
        self.highlighted_positions = [] # For directional previews
        self.cone_color = None # For directional previews
        
        # UI Element Rects - These are stateful and used by logic/UI
        self.unit_buttons = self._create_unit_buttons() # For setup phase
        self.done_button = pygame.Rect(const.WINDOW_WIDTH - 150, const.WINDOW_HEIGHT - 60, 120, 40) # Use const
        
        self.end_turn_button = pygame.Rect(const.WINDOW_WIDTH - 150, 20, 120, 40) # Use const
        self.escape_button = pygame.Rect(const.WINDOW_WIDTH - 150, 70, 120, 40) # Use const
        
        # Initialize UI components that depend on game state
        game_ui.update_ability_buttons(self) # Populates self.ability_buttons

    def calculate_turn_ap(self, turn_number, player_id):
        """Calculate AP for a given turn with first player advantage mitigation"""
        base_ap = min(self.base_ap + (turn_number - 1) * self.ap_increment, self.max_ap)

        # First player advantage mitigation - Player 2 gets early bonus
        if player_id == 2 and turn_number <= 3:
            bonus_ap = self.player2_early_bonus
            return min(base_ap + bonus_ap, self.max_ap)

        return base_ap

    def start_player_turn(self, player_id):
        """Initialize a player's turn with calculated AP"""
        self.current_player = player_id

        # Calculate AP for this turn
        self.current_player_ap = self.calculate_turn_ap(self.turn_count, player_id)
        self.units_acted_this_turn.clear()

        # Reset unit action flags
        for unit in self.board.units.values():
            if unit.player_id == player_id:
                unit.has_acted_this_turn = False

        print(f"Player {player_id} Turn {self.turn_count}: {self.current_player_ap} AP available")
        return self.current_player_ap

    def spend_ap(self, amount, unit=None):
        """Spend AP from the global pool"""
        if self.current_player_ap >= amount:
            self.current_player_ap -= amount
            if unit:
                self.units_acted_this_turn.add(unit)
                unit.has_acted_this_turn = True
            return True
        return False

    def can_unit_act(self, unit):
        """Check if a unit can perform any action"""
        # Check if unit has already acted
        if hasattr(unit, 'has_acted_this_turn') and unit.has_acted_this_turn:
            return False

        # Check if unit has any affordable actions
        if hasattr(unit, 'abilities') and unit.abilities:
            # Use the unit's method to get actual AP costs including status effects
            min_cost = min(unit.get_ability_ap_cost(i) for i in range(len(unit.abilities)))
            return self.current_player_ap >= min_cost

        return self.current_player_ap > 0

    def get_ap_progression_preview(self, max_turns=10):
        """Get AP progression for both players (for UI display)"""
        progression = []
        for turn in range(1, max_turns + 1):
            p1_ap = self.calculate_turn_ap(turn, 1)
            p2_ap = self.calculate_turn_ap(turn, 2)
            progression.append({
                'turn': turn,
                'player1_ap': p1_ap,
                'player2_ap': p2_ap,
                'difference': p2_ap - p1_ap
            })
        return progression

    def apply_balance_changes(self):
        """Apply balance slider values to the game"""
        changes_applied = []

        # Apply global AP settings
        if "ap_scaling" in self.balance_sliders.sliders:
            self.ap_increment = int(self.balance_sliders.sliders["ap_scaling"].current_value)
            changes_applied.append(f"AP scaling: +{self.ap_increment} per turn")

        if "max_ap" in self.balance_sliders.sliders:
            self.max_ap = int(self.balance_sliders.sliders["max_ap"].current_value)
            changes_applied.append(f"Max AP: {self.max_ap}")

        if "p2_bonus" in self.balance_sliders.sliders:
            self.player2_early_bonus = int(self.balance_sliders.sliders["p2_bonus"].current_value)
            changes_applied.append(f"Player 2 bonus: +{self.player2_early_bonus} AP")

        # Apply unit-specific changes (HP, etc.)
        for unit in self.board.units.values():
            unit_type = unit.__class__.__name__.lower()
            hp_slider_id = f"{unit_type}_hp"

            if hp_slider_id in self.balance_sliders.sliders:
                new_max_hp = int(self.balance_sliders.sliders[hp_slider_id].current_value)
                if unit.max_health != new_max_hp:
                    # Adjust current health proportionally
                    health_ratio = unit.health / unit.max_health if unit.max_health > 0 else 1
                    unit.max_health = new_max_hp
                    unit.health = max(1, int(new_max_hp * health_ratio))
                    changes_applied.append(f"{unit.name} max HP: {new_max_hp}")

        return changes_applied

    def _create_unit_buttons(self):
        # This is helper for __init__, specific to setup UI state
        buttons = {}
        button_width = 120
        button_height = 40
        start_x = const.WINDOW_WIDTH - button_width - 20 # Use const.WINDOW_WIDTH
        start_y = 100
        spacing = 50  # Magic number
        # Unit types could be from GAME_CONFIG or a unit registry
        unit_types_for_buttons = ["Warrior", "Hunter", "Rogue", "Pawn", "King", "Cleric", "Mage", "Warlock", "Paladin", "Druid", "Bard"]
        for i, unit_type in enumerate(unit_types_for_buttons):
            buttons[unit_type] = pygame.Rect(start_x, start_y + i * spacing, button_width, button_height)
        return buttons
    
    # _filter_unit_abilities was in Game class. It modifies unit.abilities based on SELECTED_ABILITIES.
    # This feels like it should be part of game setup logic or when a unit is finalized for the board.
    # Keeping it here for now as it modifies state related to units that Game will manage.
    def _filter_unit_abilities(self, unit):
        """
        Apply selected abilities and passives to a unit.

        This method filters the unit's abilities based on player selections
        and applies the selected passive abilities.

        Args:
            unit: The unit to configure
        """
        self._apply_selected_abilities(unit)
        self._apply_selected_passives(unit)

    def _apply_selected_abilities(self, unit):
        """
        Filter unit abilities based on player selections.

        Args:
            unit: The unit to filter abilities for
        """
        unit_type_name = unit.name
        default_abilities = unit.abilities[:2]  # Move and Basic Attack

        if unit_type_name in SELECTED_ABILITIES and SELECTED_ABILITIES[unit_type_name]:
            selected_indices = SELECTED_ABILITIES[unit_type_name]
            special_abilities = unit.abilities[2:]  # Special abilities start from index 2

            new_special_abilities = []
            for index in selected_indices:
                if 0 <= index < len(special_abilities):
                    new_special_abilities.append(special_abilities[index])

            unit.abilities = default_abilities + new_special_abilities

    def _apply_selected_passives(self, unit):
        """
        Apply selected passive abilities to a unit.

        Args:
            unit: The unit to apply passives to
        """
        unit_type_name = unit.name

        if unit_type_name in SELECTED_PASSIVES and SELECTED_PASSIVES[unit_type_name]:
            selected_passive_types = SELECTED_PASSIVES[unit_type_name]

            for passive_type_name in selected_passive_types:
                try:
                    passive_type = PassiveType(passive_type_name)
                    if passive_type in PASSIVE_ABILITIES:
                        passive_ability = PASSIVE_ABILITIES[passive_type]
                        if hasattr(unit, 'passive_manager'):
                            unit.passive_manager.add_passive(passive_ability)
                        else:
                            print(f"Warning: {unit.name} does not have passive_manager - skipping passive {passive_type_name}")
                        # Print statement removed - add_passive already prints when successful
                except ValueError:
                    print(f"Warning: Unknown passive type '{passive_type_name}' for {unit_type_name}")

    # board_to_screen was a method in Game class, seems like a utility.
    # It delegates to game_visuals. It can stay if Game object is conveniently passed around,
    # or be called directly: game_visuals.board_to_screen(game_object_or_screen, pos_tuple)
    def board_to_screen(self, pos_tuple):
        return game_visuals.board_to_screen(self, pos_tuple)

# Other methods from Game class like run() will be moved to game_loop.py
# Drawing methods like _draw_board, _draw_units will be moved to game_draw.py 
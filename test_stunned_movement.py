#!/usr/bin/env python3
"""
Test that stunned units cannot move.
"""

import pygame
pygame.init()

from game_state import Game
from units.warrior import Warrior
from units.rogue import <PERSON>

def test_stunned_movement():
    print("🚫 TESTING STUNNED UNIT MOVEMENT RESTRICTION")
    print("=" * 50)
    
    # Create game and units
    game = Game()
    warrior = Warrior(1)
    target = Rogue(2)
    
    # Set up board
    game.board.add_unit(warrior, 4, 4)
    game.board.add_unit(target, 4, 5)
    
    # Set up game state
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Initial positions:")
    print(f"  Warrior at: {warrior.position}")
    print(f"  Target at: {target.position}")
    
    # Use Shield Bash to stun the target
    print(f"\n1. Using Shield Bash to stun target...")
    success = warrior.use_ability(3, target.position, game)
    print(f"  Shield Bash success: {success}")
    print(f"  Target stunned: {target.has_status('Stunned')}")
    
    # Switch to player 2 and try to move the stunned unit
    print(f"\n2. Switching to player 2 and trying to move stunned unit...")
    game.current_player = 2
    game.current_player_ap = 10
    target.has_acted_this_turn = False  # Reset for testing
    
    # Check if unit can move
    can_move = target.status_effect_manager.can_unit_move(target)
    print(f"  Can stunned unit move: {can_move}")
    
    # Get valid moves
    valid_moves = target.get_valid_moves(game.board)
    print(f"  Valid moves for stunned unit: {valid_moves}")
    
    # Try to move the stunned unit
    if valid_moves:
        print(f"  Attempting to move to {valid_moves[0]}...")
        move_success = target.use_ability(0, valid_moves[0], game)  # Move ability
        print(f"  Move success: {move_success}")
        print(f"  Target position after move attempt: {target.position}")
    else:
        print(f"  No valid moves available (as expected for stunned unit)")
    
    # Test that the unit can still be targeted by abilities
    print(f"\n3. Testing that stunned unit can still be targeted...")
    game.current_player = 1
    game.current_player_ap = 10
    warrior.has_acted_this_turn = False  # Reset for testing
    
    # Try another attack on the stunned unit
    attack_success = warrior.use_ability(1, target.position, game)  # Basic attack
    print(f"  Basic attack on stunned unit success: {attack_success}")
    
    print(f"\n✅ Stunned movement restriction test completed!")

if __name__ == "__main__":
    test_stunned_movement()

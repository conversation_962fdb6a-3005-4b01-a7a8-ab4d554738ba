import pygame
import sys
from menu_screens.button import Button
from menu_screens.settings_menu import Settings
from menu_screens.new_config_menu import NewConfigMenu
from menu_screens.ability_selection_menu import AbilitySelectionMenu

# Constants (ideally from a shared source)
WINDOW_WIDTH = 1280 
WINDOW_HEIGHT = 720
FPS = 60
DARK_GRAY = (30, 30, 30)
LIGHT_GRAY = (200, 200, 200)

class OptionsMenu:
    def __init__(self, screen, clock, main_menu_ref):
        self.screen = screen
        self.clock = clock
        self.main_menu_ref = main_menu_ref # To pass to SettingsMenu
        self.running = True
        
        self.title_font = pygame.font.Font(None, 74)
        self.button_font = pygame.font.Font(None, 48)
        
        self._create_buttons()

    def _create_buttons(self):
        current_screen_width, current_screen_height = self.screen.get_size()
        button_width = 350
        button_height = 60
        spacing = 20
        
        # Centering buttons
        start_y = current_screen_height // 2 - (4 * button_height + 3 * spacing) // 2
        center_x = current_screen_width // 2

        self.screen_settings_button = Button(center_x - button_width//2, start_y, button_width, button_height, "Screen Settings")
        self.game_config_button = Button(center_x - button_width//2, start_y + (button_height + spacing), button_width, button_height, "Game Configuration")
        self.ability_select_button = Button(center_x - button_width//2, start_y + 2*(button_height + spacing), button_width, button_height, "Select Abilities")
        self.back_button = Button(center_x - button_width//2, start_y + 3*(button_height + spacing) + 30, button_width, button_height, "Back to Main Menu")

    def draw_background(self):
        current_screen_width, current_screen_height = self.screen.get_size()
        for y in range(current_screen_height):
            color_value = int(30 + (y / current_screen_height) * 20)
            pygame.draw.line(self.screen, (color_value, color_value, color_value),
                           (0, y), (current_screen_width, y))

    def run(self):
        self.running = True
        self._create_buttons() # Ensure buttons are updated for current screen dimensions

        while self.running:
            current_screen_width, current_screen_height = self.screen.get_size()
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit() # OptionsMenu might be deep, allow full exit
                
                if self.screen_settings_button.handle_event(event):
                    # SettingsMenu needs main_menu_ref to update global screen settings
                    settings_menu = Settings(self.screen, self.clock, self.main_menu_ref)
                    settings_menu.run()
                    self._create_buttons() # Refresh buttons in case resolution changed
                
                if self.game_config_button.handle_event(event):
                    config_menu = NewConfigMenu(self.screen, self.clock)
                    config_menu.run()
                    self._create_buttons()
                
                if self.ability_select_button.handle_event(event):
                    ability_menu = AbilitySelectionMenu(self.screen, self.clock)
                    ability_menu.run()
                    self._create_buttons()

                if self.back_button.handle_event(event):
                    self.running = False
            
            self.draw_background()
            
            title_surface = self.title_font.render("OPTIONS", True, LIGHT_GRAY)
            title_rect = title_surface.get_rect(center=(current_screen_width//2, 100))
            self.screen.blit(title_surface, title_rect)
            
            self.screen_settings_button.draw(self.screen, self.button_font)
            self.game_config_button.draw(self.screen, self.button_font)
            self.ability_select_button.draw(self.screen, self.button_font)
            self.back_button.draw(self.screen, self.button_font)
            
            pygame.display.flip()
            self.clock.tick(FPS) 
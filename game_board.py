import pygame
import numpy as np
import game_constants as const # Import constants

# Constants previously defined here are now in game_constants.py
# BOARD_SIZE = 9
# CELL_SIZE = 70
# BOARD_PADDING = 50
# HIGHLIGHT_ALPHA = 128

# Colors previously defined here are now in game_constants.py
# DARK_CELL = (60, 60, 70)
# LIGHT_CELL = (150, 150, 160)
# PLAYER1_COLOR = (0, 100, 200)    # Blue
# PLAYER2_COLOR = (200, 60, 60)    # Red
# MOVE_HIGHLIGHT = (0, 255, 0, HIGHLIGHT_ALPHA)
# ATTACK_HIGHLIGHT = (255, 0, 0, HIGHLIGHT_ALPHA)
# SELECTED_HIGHLIGHT = (255, 255, 0, HIG<PERSON>IGHT_ALPHA)

class GameBoard:
    def __init__(self, screen):
        self.screen = screen
        self.board = np.zeros((const.BOARD_SIZE, const.BOARD_SIZE), dtype=int)  # 0 = empty
        self.units = {}  # Dictionary mapping (x, y) positions to Unit objects
        
        # Create surfaces for highlighting
        self.move_surface = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA)
        self.move_surface.fill(const.MOVE_HIGHLIGHT_COLOR)
        self.attack_surface = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA)
        self.attack_surface.fill(const.ATTACK_HIGHLIGHT_COLOR)
        self.selected_surface = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA)
        self.selected_surface.fill(const.SELECTED_HIGHLIGHT_COLOR)
    
    def get_rect(self):
        """Get the pygame Rect for the entire board"""
        width = const.CELL_SIZE * const.BOARD_SIZE + 2 * const.BOARD_PADDING
        height = const.CELL_SIZE * const.BOARD_SIZE + 2 * const.BOARD_PADDING
        return pygame.Rect(0, 0, width, height)
    
    def get_cell_rect(self, row, col):
        """Get the pygame Rect for a specific cell"""
        x = col * const.CELL_SIZE + const.BOARD_PADDING
        y = row * const.CELL_SIZE + const.BOARD_PADDING
        return pygame.Rect(x, y, const.CELL_SIZE, const.CELL_SIZE)
    
    def get_cell_from_pos(self, pos):
        """Convert screen position to board cell coordinates"""
        x, y = pos
        x = (x - const.BOARD_PADDING) // const.CELL_SIZE
        y = (y - const.BOARD_PADDING) // const.CELL_SIZE
        
        if 0 <= x < const.BOARD_SIZE and 0 <= y < const.BOARD_SIZE:
            return y, x  # Return as row, col
        return None
    
    def screen_to_board(self, screen_pos):
        """Convert screen coordinates to board position"""
        x, y = screen_pos
        
        # Adjust for board offset and tile size
        board_x = (x - const.BOARD_PADDING) // const.CELL_SIZE
        board_y = (y - const.BOARD_PADDING) // const.CELL_SIZE
        
        # Ensure position is within board bounds
        if 0 <= board_x < const.BOARD_SIZE and 0 <= board_y < const.BOARD_SIZE:
            return (board_y, board_x)  # Return as (row, col)
        return None
    
    def add_unit(self, unit, row, col):
        """Add a unit to the board"""
        if 0 <= row < const.BOARD_SIZE and 0 <= col < const.BOARD_SIZE:
            self.units[(row, col)] = unit
            self.board[row, col] = unit.player_id
            unit.position = (row, col)
            return True
        return False
    
    def remove_unit(self, pos):
        """Remove a unit from the board"""
        if pos in self.units:
            row, col = pos
            unit = self.units.pop(pos)
            self.board[row, col] = 0
            return unit
        return None
    
    def move_unit(self, from_pos, to_pos):
        """Move a unit from one position to another"""
        if from_pos in self.units:
            unit = self.units[from_pos]
            to_row, to_col = to_pos
            
            # Update the board state
            self.units.pop(from_pos)
            from_row, from_col = from_pos
            self.board[from_row, from_col] = 0
            
            # Add the unit to its new position
            self.units[to_pos] = unit
            self.board[to_row, to_col] = unit.player_id
            unit.position = to_pos
            return True
        return False
    
    def is_valid_move(self, from_pos, to_pos):
        """Check if a move is valid"""
        # Basic check - destination must be empty
        to_row, to_col = to_pos
        if self.board[to_row, to_col] != 0:
            return False
            
        # Get the unit's movement pattern
        unit = self.units.get(from_pos)
        if unit:
            return to_pos in unit.get_valid_moves(self)
        return False
    
    def draw(self, valid_moves=None, valid_attacks=None, selected_unit=None):
        """Draw the game board and all units"""
        if valid_moves is None:
            valid_moves = []
        if valid_attacks is None:
            valid_attacks = []
            
        # Draw the board background
        board_rect = self.get_rect()
        pygame.draw.rect(self.screen, const.DARK_GRAY, board_rect) # Use const.DARK_GRAY
        
        # Draw the board cells
        for row in range(const.BOARD_SIZE):
            for col in range(const.BOARD_SIZE):
                rect = self.get_cell_rect(row, col)
                color = const.LIGHT_CELL if (row + col) % 2 == 0 else const.DARK_CELL # Use const colors
                
                # If this is player territory, tint the cells slightly
                if row < 3:  # Player 2 territory
                    color = tuple(max(0, min(255, c + 15 if c > 100 else c - 15)) for c in color)
                elif row >= const.BOARD_SIZE - 3:  # Player 1 territory
                    color = tuple(max(0, min(255, c + 15 if c > 100 else c - 15)) for c in color)
                
                pygame.draw.rect(self.screen, color, rect)
                
                # Draw border
                pygame.draw.rect(self.screen, const.BLACK, rect, 1) # Use const.BLACK for border
        
        # Draw highlights for valid moves and attacks
        if selected_unit:
            # Highlight selected unit position
            selected_rect = self.get_cell_rect(*selected_unit.position)
            self.screen.blit(self.selected_surface, selected_rect)
            
            # Highlight valid moves
            for move in valid_moves:
                rect = self.get_cell_rect(*move)
                self.screen.blit(self.move_surface, rect)
            
            # Highlight valid attacks
            for attack in valid_attacks:
                rect = self.get_cell_rect(*attack)
                self.screen.blit(self.attack_surface, rect)
        
        # Draw units
        for pos, unit in self.units.items():
            row, col = pos
            rect = self.get_cell_rect(row, col)
            
            # Draw unit circle with color based on player
            color = const.PLAYER1_COLOR if unit.player_id == 1 else const.PLAYER2_COLOR # Use const colors
            pygame.draw.circle(self.screen, color, rect.center, const.CELL_SIZE//2 - 5)
            
            # Draw unit type (first letter)
            font = pygame.font.Font(const.FONT_DEFAULT_NAME, 36) # Use const.FONT_DEFAULT_NAME
            text = font.render(unit.name[0], True, const.WHITE) # Use const.WHITE for text
            text_rect = text.get_rect(center=rect.center)
            self.screen.blit(text, text_rect)
            
            # Draw health bar
            health_percent = unit.health / unit.max_health
            bar_width = int((const.CELL_SIZE - 10) * health_percent)
            health_bar = pygame.Rect(rect.x + 5, rect.y + rect.height - 12, bar_width, 7)
            pygame.draw.rect(self.screen, const.GREEN, health_bar) # Use const.GREEN
            pygame.draw.rect(self.screen, const.LIGHT_GRAY, 
                           pygame.Rect(rect.x + 5, rect.y + rect.height - 12, const.CELL_SIZE - 10, 7), 1) # Use const.LIGHT_GRAY

#!/usr/bin/env python3
"""
Balanced AP System Implementation
- AP starts at 1, increases by 1 each turn, max 10
- Average unit HP around 10
- First player advantage mitigation
- In-game balance sliders support
"""

import pygame
from game_state import Game
from units.warrior import Warrior
from units.mage import Mage
from units.cleric import Cleric

class BalancedAPGame(Game):
    """Game with balanced AP scaling and first player advantage mitigation"""
    
    def __init__(self, fullscreen=False):
        super().__init__(fullscreen)
        
        # Balanced AP system
        self.base_ap = 1
        self.ap_increment = 1
        self.max_ap = 10
        self.current_turn_number = 1
        
        # Current player's AP for this turn
        self.current_player_ap = self.base_ap
        self.units_acted_this_turn = set()
        
        # First player advantage mitigation
        self.first_player_advantage_mitigation = True
        self.player2_bonus_ap = 0  # Extra AP for player 2
        
        # Balance tracking for sliders
        self.balance_stats = {
            "average_game_length": 0,
            "player1_wins": 0,
            "player2_wins": 0,
            "total_games": 0
        }
    
    def calculate_turn_ap(self, turn_number, player_id=None):
        """Calculate AP for a given turn, with first player advantage mitigation"""
        base_ap = min(self.base_ap + (turn_number - 1) * self.ap_increment, self.max_ap)
        
        # First player advantage mitigation
        if self.first_player_advantage_mitigation and player_id == 2:
            # Player 2 gets slight bonus in early game
            if turn_number <= 3:
                bonus = 1
            elif turn_number <= 5:
                bonus = 0.5  # Rounded down, so effectively 0 extra but considered in balance
            else:
                bonus = 0
            
            return min(base_ap + int(bonus), self.max_ap)
        
        return base_ap
    
    def start_player_turn(self, player_id):
        """Start a player's turn with calculated AP"""
        self.current_player = player_id
        
        # Calculate AP for this turn
        if player_id == 1:
            # Player 1 turn increments the turn counter
            self.current_turn_number = self.turn_count
        
        self.current_player_ap = self.calculate_turn_ap(self.current_turn_number, player_id)
        self.units_acted_this_turn.clear()
        
        # Reset unit action flags
        for unit in self.board.units.values():
            if unit.player_id == player_id:
                unit.has_acted_this_turn = False
        
        print(f"Player {player_id} Turn {self.current_turn_number}: {self.current_player_ap} AP")
        
        return self.current_player_ap
    
    def get_ap_progression_preview(self, max_turns=10):
        """Show AP progression for both players"""
        progression = []
        for turn in range(1, max_turns + 1):
            p1_ap = self.calculate_turn_ap(turn, 1)
            p2_ap = self.calculate_turn_ap(turn, 2)
            progression.append({
                'turn': turn,
                'player1_ap': p1_ap,
                'player2_ap': p2_ap,
                'difference': p2_ap - p1_ap
            })
        return progression
    
    def update_balance_stats(self, winner):
        """Update balance statistics for slider adjustments"""
        self.balance_stats["total_games"] += 1
        self.balance_stats["average_game_length"] = (
            (self.balance_stats["average_game_length"] * (self.balance_stats["total_games"] - 1) + 
             self.current_turn_number) / self.balance_stats["total_games"]
        )
        
        if winner == 1:
            self.balance_stats["player1_wins"] += 1
        else:
            self.balance_stats["player2_wins"] += 1
    
    def get_balance_report(self):
        """Get current balance statistics"""
        if self.balance_stats["total_games"] == 0:
            return "No games completed yet"
        
        p1_winrate = (self.balance_stats["player1_wins"] / self.balance_stats["total_games"]) * 100
        p2_winrate = (self.balance_stats["player2_wins"] / self.balance_stats["total_games"]) * 100
        
        return {
            "player1_winrate": p1_winrate,
            "player2_winrate": p2_winrate,
            "average_game_length": self.balance_stats["average_game_length"],
            "total_games": self.balance_stats["total_games"],
            "balance_assessment": self._assess_balance(p1_winrate, p2_winrate)
        }
    
    def _assess_balance(self, p1_winrate, p2_winrate):
        """Assess game balance and suggest adjustments"""
        difference = abs(p1_winrate - p2_winrate)
        
        if difference <= 5:
            return "Well balanced"
        elif difference <= 10:
            return "Slightly imbalanced"
        elif difference <= 20:
            return "Moderately imbalanced"
        else:
            return "Severely imbalanced"

def create_balanced_units():
    """Create units with balanced stats for ~10 HP average"""
    
    # Balanced unit stats
    unit_configs = {
        "warrior": {"hp": 12, "max_ap": 6, "role": "tank"},
        "mage": {"hp": 8, "max_ap": 9, "role": "damage"},
        "cleric": {"hp": 10, "max_ap": 7, "role": "support"},
        "rogue": {"hp": 9, "max_ap": 8, "role": "mobility"},
        "hunter": {"hp": 10, "max_ap": 8, "role": "ranged"}
    }
    
    return unit_configs

def test_balanced_ap_system():
    """Test the balanced AP system"""
    pygame.init()
    
    print("⚖️ TESTING BALANCED AP SYSTEM ⚖️")
    print("=" * 42)
    
    game = BalancedAPGame()
    
    # Test 1: AP Progression
    print("📋 TEST 1: AP Progression")
    print("-" * 25)
    
    progression = game.get_ap_progression_preview(10)
    print("Turn | P1 AP | P2 AP | Diff")
    print("-" * 30)
    for turn_data in progression:
        print(f"{turn_data['turn']:4d} | {turn_data['player1_ap']:5d} | {turn_data['player2_ap']:5d} | {turn_data['difference']:+4d}")
    
    # Test 2: First Player Advantage Mitigation
    print(f"\n📋 TEST 2: First Player Advantage Mitigation")
    print("-" * 43)
    
    early_game_advantage = sum(turn_data['difference'] for turn_data in progression[:5])
    total_advantage = sum(turn_data['difference'] for turn_data in progression)
    
    print(f"Early game (turns 1-5) P2 advantage: +{early_game_advantage} total AP")
    print(f"Full game (turns 1-10) P2 advantage: +{total_advantage} total AP")
    
    if early_game_advantage > 0:
        print("✅ Player 2 gets early game compensation")
    else:
        print("❌ No early game compensation")
    
    # Test 3: Simulated Turn Progression
    print(f"\n📋 TEST 3: Simulated Turn Progression")
    print("-" * 36)
    
    # Simulate first few turns
    for turn in range(1, 6):
        print(f"\n--- Turn {turn} ---")
        
        # Player 1 turn
        p1_ap = game.start_player_turn(1)
        print(f"Player 1: {p1_ap} AP")
        
        # Player 2 turn  
        p2_ap = game.start_player_turn(2)
        print(f"Player 2: {p2_ap} AP")
        
        # Increment turn counter for next iteration
        game.turn_count += 1
    
    # Test 4: Unit Balance Preview
    print(f"\n📋 TEST 4: Unit Balance Preview")
    print("-" * 31)
    
    unit_configs = create_balanced_units()
    total_hp = sum(config["hp"] for config in unit_configs.values())
    average_hp = total_hp / len(unit_configs)
    
    print("Unit Type | HP | Max AP | Role")
    print("-" * 35)
    for unit_type, config in unit_configs.items():
        print(f"{unit_type:9s} | {config['hp']:2d} | {config['max_ap']:6d} | {config['role']}")
    
    print(f"\nAverage HP: {average_hp:.1f}")
    
    if 9 <= average_hp <= 11:
        print("✅ HP balance target achieved (~10 HP)")
    else:
        print(f"⚠️ HP balance needs adjustment (target: ~10, actual: {average_hp:.1f})")
    
    # Test 5: Balance Assessment Simulation
    print(f"\n📋 TEST 5: Balance Assessment Simulation")
    print("-" * 38)
    
    # Simulate some game results
    import random
    for i in range(20):
        winner = random.choice([1, 2])
        game.current_turn_number = random.randint(5, 12)
        game.update_balance_stats(winner)
    
    balance_report = game.get_balance_report()
    print(f"Player 1 winrate: {balance_report['player1_winrate']:.1f}%")
    print(f"Player 2 winrate: {balance_report['player2_winrate']:.1f}%")
    print(f"Average game length: {balance_report['average_game_length']:.1f} turns")
    print(f"Balance assessment: {balance_report['balance_assessment']}")
    
    print(f"\n" + "=" * 42)
    print("🎯 BALANCED AP SYSTEM SUMMARY")
    print("-" * 30)
    print("✅ AP scaling: 1 → 10 (+1 per turn)")
    print("✅ First player advantage mitigation")
    print("✅ Target HP: ~10 average")
    print("✅ Balance tracking for sliders")
    print("✅ Statistical analysis")
    
    print("\n🎮 RECOMMENDED ABILITY COSTS:")
    print("Move: 1 AP")
    print("Basic Attack: 1-2 AP") 
    print("Simple Abilities: 2-3 AP")
    print("Advanced Abilities: 4-6 AP")
    print("Ultimate Abilities: 7-9 AP")
    
    print("\n⚖️ This system provides:")
    print("- Gradual complexity increase")
    print("- Balanced first player advantage")
    print("- Good granularity for balancing")
    print("- Statistical feedback for sliders")

if __name__ == "__main__":
    test_balanced_ap_system()

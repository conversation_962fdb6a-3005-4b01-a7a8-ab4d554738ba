import pygame
import sys
from pygame import gfxdraw # Used for sliders
from menu_screens.button import Button
from game_config import GAME_CONFIG # For reading and writing config values

# Constants that might be needed (similar to settings_menu.py)
# Ideally from a shared source or passed in.
WINDOW_WIDTH = 1280
WINDOW_HEIGHT = 720
FPS = 60
DARK_GRAY = (30, 30, 30)
LIGHT_GRAY = (200, 200, 200)
SELECTED_COLOR = (0, 200, 100) # For selected items, slider handles
DISABLED_COLOR = (100, 100, 100)

class ConfigMenu:
    def __init__(self, screen, clock):
        self.screen = screen
        self.clock = clock
        self.running = True
        
        self.title_font = pygame.font.Font(None, 74)
        self.button_font = pygame.font.Font(None, 48)
        self.option_font = pygame.font.Font(None, 36)
        self.label_font = pygame.font.Font(None, 32)
        
        current_screen_width, current_screen_height = self.screen.get_size()
        self.back_button = But<PERSON>(current_screen_width//2 - 100, current_screen_height - 100, 200, 60, "Back")
        self.save_button = But<PERSON>(current_screen_width//2 - 100, current_screen_height - 180, 200, 60, "Save Changes")
        
        self.class_buttons = self._create_class_buttons()
        self.selected_class = "General"
        
        self.sliders = self._create_sliders() # This needs current_screen_width for positioning
        
        self.original_config = {k: v for k, v in GAME_CONFIG.items()} # Deep copy for restoration
    
    def _create_class_buttons(self):
        buttons = {}
        button_width = 140
        button_height = 40
        start_x = 100
        y_pos = 120 # Renamed y to y_pos to avoid conflict if screen height is also y
        
        class_names = ["General", "Hunter", "Warrior", "Rogue", "Cleric", "Mage", "Pawn", "King"]
        
        for i, class_name in enumerate(class_names):
            x_pos = start_x + (i % 4) * (button_width + 20)
            y_offset = 0 if i < 4 else button_height + 10
            buttons[class_name] = Button(x_pos, y_pos + y_offset, button_width, button_height, class_name)
        
        return buttons
    
    def _create_sliders(self):
        sliders = {}
        current_screen_width, _ = self.screen.get_size()
        slider_width = 300
        slider_height = 30
        start_x = current_screen_width//2 - slider_width//2
        start_y = 220 # Base Y for the first slider
        spacing = 40
        
        # Helper to create slider entries to reduce repetition
        def add_slider(category, key, label, min_val, max_val, y_offset_idx):
            if category not in sliders: sliders[category] = {}
            sliders[category][key] = {
                'rect': pygame.Rect(start_x, start_y + spacing * y_offset_idx, slider_width, slider_height),
                'label': label,
                'min': min_val,
                'max': max_val,
                'value': GAME_CONFIG.get(key, min_val), # Use .get for safety
                'dragging': False
            }

        # General settings
        add_slider('General', 'ability_damage', "Ability Damage", 1, 5, 0)
        add_slider('General', 'move_ap_cost', "Move AP Cost", 1, 3, 1)
        add_slider('General', 'attack_ap_cost', "Attack AP Cost", 1, 5, 2)

        # Unit-specific base stats (Health, Max AP, Movement Range)
        unit_configs = [
            ("Hunter", "hunter_health", "Health", 1, 10, 0),
            ("Hunter", "hunter_max_ap", "Max AP", 3, 15, 1),
            ("Hunter", "hunter_movement_range", "Movement Range", 1, 5, 2),
            ("Warrior", "warrior_health", "Health", 1, 15, 0),
            ("Warrior", "warrior_max_ap", "Max AP", 3, 10, 1),
            ("Warrior", "warrior_movement_range", "Movement Range", 1, 5, 2),
            ("Rogue", "rogue_health", "Health", 1, 8, 0),
            ("Rogue", "rogue_max_ap", "Max AP", 4, 12, 1),
            ("Rogue", "rogue_movement_range", "Movement Range", 1, 5, 2),
            ("Cleric", "cleric_health", "Health", 1, 12, 0),
            ("Cleric", "cleric_max_ap", "Max AP", 3, 10, 1),
            ("Cleric", "cleric_movement_range", "Movement Range", 1, 5, 2),
            ("Mage", "mage_health", "Health", 1, 8, 0),
            ("Mage", "mage_max_ap", "Max AP", 3, 12, 1),
            ("Mage", "mage_movement_range", "Movement Range", 1, 5, 2),
            ("Pawn", "pawn_health", "Health", 1, 5, 0),
            ("Pawn", "pawn_max_ap", "Max AP", 2, 6, 1),
            ("Pawn", "pawn_movement_range", "Movement Range", 1, 3, 2),
            ("King", "king_health", "Health", 5, 20, 0),
            ("King", "king_max_ap", "Max AP", 3, 8, 1),
            ("King", "king_movement_range", "Movement Range", 1, 3, 2),
        ]
        for cat, key, label, min_v, max_v, y_idx in unit_configs:
            add_slider(cat, key, label, min_v, max_v, y_idx)

        # Unit-specific ability costs (example for Hunter)
        # This needs to be more generic if all abilities are configurable
        # For now, keeping the structure simple, assuming a few key abilities per class are shown
        hunter_ability_ap_costs = [
            ("ricochet_shot_ap_cost", "Ricochet AP", 1, 5, 3),
            ("triple_shot_ap_cost", "Triple Shot AP", 1, 5, 4),
            ("multishot_ap_cost", "Multishot AP", 1, 5, 5),
            ("piercing_shot_ap_cost", "Piercing AP", 1, 5, 6),
            # Add other Hunter ability costs here
        ]
        for key, label, min_v, max_v, y_idx in hunter_ability_ap_costs:
            add_slider("Hunter", key, label, min_v, max_v, y_idx)

        # Add more sliders for other classes and their abilities as needed...
        # Example for Warrior Cleave Attack AP Cost
        add_slider("Warrior", "cleave_attack_ap_cost", "Cleave AP", 1, 5, 3)
        add_slider("Mage", "fireball_ap_cost", "Fireball AP", 1, 5, 3)
        add_slider("Cleric", "heal_ap_cost", "Heal AP", 1, 5, 3)
        add_slider("King", "royal_decree_ap_cost", "Decree AP", 1, 5, 3)
        add_slider("Rogue", "backstab_ap_cost", "Backstab AP", 1, 5, 3)

        return sliders
    
    def draw_background(self):
        current_screen_width, current_screen_height = self.screen.get_size()
        for y_pos in range(current_screen_height):
            color_value = int(30 + (y_pos / current_screen_height) * 20)
            pygame.draw.line(self.screen, (color_value, color_value, color_value),
                           (0, y_pos), (current_screen_width, y_pos))
    
    def draw_slider(self, slider_info):
        current_screen_width, _ = self.screen.get_size()
        rect = slider_info['rect']
        # Adjust rect x based on current screen width if it was based on initial WINDOW_WIDTH
        rect.centerx = current_screen_width // 2 

        label_surface = self.label_font.render(f"{slider_info['label']}: {slider_info['value']}", True, LIGHT_GRAY)
        self.screen.blit(label_surface, (rect.x, rect.y - 25))
        
        pygame.draw.rect(self.screen, DARK_GRAY, rect, border_radius=4)
        pygame.draw.rect(self.screen, LIGHT_GRAY, rect, 2, border_radius=4)
        
        handle_width = 20
        handle_x = rect.x + int((slider_info['value'] - slider_info['min']) / 
                                (slider_info['max'] - slider_info['min']) * (rect.width - handle_width))
        handle_rect = pygame.Rect(handle_x, rect.y, handle_width, rect.height)
        pygame.draw.rect(self.screen, SELECTED_COLOR, handle_rect, border_radius=8)
        
    def handle_slider_event(self, event, slider_info):
        rect = slider_info['rect']
        if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
            if rect.collidepoint(event.pos):
                slider_info['dragging'] = True
        elif event.type == pygame.MOUSEBUTTONUP and event.button == 1:
            slider_info['dragging'] = False
        elif event.type == pygame.MOUSEMOTION and slider_info['dragging']:
            self.update_slider_value(event.pos[0], slider_info)

    def update_slider_value(self, x_pos, slider_info):
        rect = slider_info['rect']
        handle_width = 20 
        relative_x = x_pos - rect.x - (handle_width / 2)
        percentage = max(0, min(1, relative_x / (rect.width - handle_width)))
        value_range = slider_info['max'] - slider_info['min']
        slider_info['value'] = slider_info['min'] + round(percentage * value_range)

    def apply_changes(self):
        for class_name, class_sliders in self.sliders.items():
            for key, slider_info in class_sliders.items():
                GAME_CONFIG[key] = slider_info['value']

        # Save configuration to file
        try:
            from game_config import save_config
            save_config()
            print("Configuration saved successfully!")
        except Exception as e:
            print(f"Error saving configuration: {e}")
        print("Game config updated with slider values.")
        self.original_config = {k: v for k, v in GAME_CONFIG.items()} # Update original after saving

    def run(self):
        self.running = True
        self._create_sliders() #Re-create sliders to position them based on current screen dimensions
        self.back_button.rect.midbottom = (self.screen.get_width() // 2, self.screen.get_height() - 40)
        self.save_button.rect.midbottom = (self.screen.get_width() // 2, self.screen.get_height() - 100)

        while self.running:
            current_screen_width, current_screen_height = self.screen.get_size()
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()
                
                if self.back_button.handle_event(event):
                    # Restore original config if changes weren't saved
                    for key, value in self.original_config.items():
                        GAME_CONFIG[key] = value
                    self.running = False
                
                if self.save_button.handle_event(event):
                    self.apply_changes()
                    # self.running = False # Optionally close after saving

                for class_name, button in self.class_buttons.items():
                    if button.handle_event(event):
                        self.selected_class = class_name
                
                # Handle events for sliders of the selected class
                if self.selected_class in self.sliders:
                    for slider_info in self.sliders[self.selected_class].values():
                        self.handle_slider_event(event, slider_info)
            
            self.draw_background()
            
            title_surface = self.title_font.render("GAME CONFIGURATION", True, LIGHT_GRAY)
            title_rect = title_surface.get_rect(center=(current_screen_width//2, 60))
            self.screen.blit(title_surface, title_rect)
            
            for class_name, button in self.class_buttons.items():
                button.is_hovered = button.rect.collidepoint(pygame.mouse.get_pos())
                button.draw(self.screen, self.option_font if self.selected_class != class_name else self.label_font) # Highlight selected tab

            if self.selected_class in self.sliders:
                for slider_info in self.sliders[self.selected_class].values():
                    self.draw_slider(slider_info)
            
            self.save_button.draw(self.screen, self.button_font)
            self.back_button.draw(self.screen, self.button_font)
            
            pygame.display.flip()
            self.clock.tick(FPS) 
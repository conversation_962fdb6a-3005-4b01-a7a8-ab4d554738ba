import pygame
import math
# from game_board import BOARD_SIZE, CELL_SIZE, BOARD_PADDING # Now from const
# from game_constants import STATE_PLAYING # Now use const.STATE_PLAYING
import game_constants as const # Import constants

# Colors (examples, ideally from game object or settings)
# YELLOW = (255, 200, 0) # Now from const

def process_mouse_motion(game, pos): # screen coordinates
    """Handle mouse motion events for directional ability aiming and trajectory preview."""
    if game.state == const.STATE_PLAYING and game.selected_unit and game.selected_ability is not None: # Use const
        board_pos = game.board.get_cell_from_pos(pos)
        if board_pos:
            game.hover_pos = board_pos
            ability_name = game.selected_unit.abilities[game.selected_ability].name

            if game.directional_ability:
                # Handle directional abilities like Cone of Cold
                if ability_name == "Cone of Cold":
                    calculate_highlighted_cone(game)
                else:
                    calculate_highlighted_line(game)
            elif ability_name == "Ricochet Shot":
                # Handle Ricochet Shot trajectory preview
                calculate_ricochet_trajectory(game)
        else:
            game.hover_pos = None
            game.highlighted_positions = []

def calculate_highlighted_cone(game):
    """Calculate highlighted tiles for Cone of Cold T-pattern based on game.hover_pos defining direction."""
    game.highlighted_positions = []
    if not game.selected_unit or not game.hover_pos or game.selected_ability is None:
        return

    unit_pos = game.selected_unit.position
    dir_r = game.hover_pos[0] - unit_pos[0]
    dir_c = game.hover_pos[1] - unit_pos[1]

    # Normalize direction to unit vector
    if abs(dir_r) > 1: dir_r = dir_r // abs(dir_r) if dir_r != 0 else 0
    if abs(dir_c) > 1: dir_c = dir_c // abs(dir_c) if dir_c != 0 else 0

    # Only allow orthogonal directions for T-pattern
    if abs(dir_r) + abs(dir_c) != 1:
        return

    # Calculate T-pattern tiles for the hovered direction
    mage_r, mage_c = unit_pos
    t_pattern_tiles = []

    # First tile: 1 tile in the chosen direction
    first_tile_r = mage_r + dir_r
    first_tile_c = mage_c + dir_c
    if 0 <= first_tile_r < const.BOARD_SIZE and 0 <= first_tile_c < const.BOARD_SIZE:
        t_pattern_tiles.append((first_tile_r, first_tile_c))

    # Second row: 3 tiles at 2 tiles distance (center + left + right)
    second_row_center_r = mage_r + dir_r * 2
    second_row_center_c = mage_c + dir_c * 2

    # Add center tile of second row
    if 0 <= second_row_center_r < const.BOARD_SIZE and 0 <= second_row_center_c < const.BOARD_SIZE:
        t_pattern_tiles.append((second_row_center_r, second_row_center_c))

    # Add left and right tiles of second row (perpendicular to direction)
    if dir_r == 0:  # Horizontal movement (East/West)
        # Perpendicular is North/South
        left_tile = (second_row_center_r - 1, second_row_center_c)
        right_tile = (second_row_center_r + 1, second_row_center_c)
    else:  # Vertical movement (North/South)
        # Perpendicular is East/West
        left_tile = (second_row_center_r, second_row_center_c - 1)
        right_tile = (second_row_center_r, second_row_center_c + 1)

    # Add perpendicular tiles if on board
    for tile in [left_tile, right_tile]:
        if 0 <= tile[0] < const.BOARD_SIZE and 0 <= tile[1] < const.BOARD_SIZE:
            t_pattern_tiles.append(tile)

    game.highlighted_positions = t_pattern_tiles
    game.cone_color = const.DIRECTIONAL_PREVIEW_COLOR # Use const

def calculate_highlighted_line(game):
    """Calculate highlighted tiles for a line-based directional ability."""
    game.highlighted_positions = []
    if not game.selected_unit or not game.hover_pos:
        return

    start_pos = game.selected_unit.position
    max_line_range = const.BOARD_SIZE # Use const
    
    current_r, current_c = start_pos
    target_r, target_c = game.hover_pos
    
    line_tiles = []
    steps = max(abs(target_r - current_r), abs(target_c - current_c))
    if steps == 0: 
        game.highlighted_positions = []
        return

    for i in range(1, steps + 1):
        t = i / steps
        r = round(current_r + t * (target_r - current_r))
        c = round(current_c + t * (target_c - current_c))
        if not (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE): # Use const
            break
        if len(line_tiles) < max_line_range:
            line_tiles.append((r,c))
        else:
            break
            
    game.highlighted_positions = list(set(line_tiles))
    game.cone_color = const.DIRECTIONAL_PREVIEW_COLOR # Use const for line highlight tint too

def calculate_ricochet_trajectory(game):
    """Calculate highlighted tiles for Ricochet Shot trajectory preview."""
    game.highlighted_positions = []
    if not game.selected_unit or not game.hover_pos:
        return

    # Check if the hovered position is a valid target for Ricochet Shot
    if game.hover_pos not in game.valid_ability_targets:
        return

    # Get trajectory preview from the Hunter
    if hasattr(game.selected_unit, 'get_ricochet_trajectory_preview'):
        trajectory = game.selected_unit.get_ricochet_trajectory_preview(game.hover_pos, game.board)
        game.highlighted_positions = trajectory
        # Use a different color for trajectory preview
        game.cone_color = const.YELLOW  # Bright yellow for trajectory

def board_to_screen(game, pos_tuple): 
    """Convert board (row, col) coordinates to screen (x, y) coordinates (top-left of cell)."""
    row, col = pos_tuple
    screen_x = const.BOARD_PADDING + col * const.CELL_SIZE # Use const
    screen_y = const.BOARD_PADDING + row * const.CELL_SIZE # Use const
    return (screen_x, screen_y)

# Removed commented out MockGameLogic and STATE_PLAYING assignment
# Global game settings that need to be shared between modules
# This file helps avoid circular imports between game.py and main_menu.py

import json
import os

# File to store ability selections
ABILITIES_SAVE_FILE = "ability_selections.json"
# File to store passive selections
PASSIVES_SAVE_FILE = "passive_selections.json"

# Default ability selections
DEFAULT_SELECTED_ABILITIES = {
    "Warrior": [0, 1, 2],  # Charge, Defensive Stance, <PERSON><PERSON><PERSON>e
    "Hunter": [0, 1, 2],   # <PERSON>chet Shot, <PERSON>nar<PERSON>, Aimed Shot
    "Rogue": [0, 1, 2],    # Backstab, Dash, Invisibility
    "Pawn": [0],           # Promotion
    "King": [0, 1],        # <PERSON>, Inspire
    "Cleric": [0, 1, 2, 3, 4],  # <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Divine Protection
    "Mage": [0, 1, 2, 3, 4]     # <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> of Cold, <PERSON> Nova, Arcane <PERSON>
}

# Default passive selections (passive type names)
# TEMPORARILY DISABLED - Only <PERSON> keeps knockback immunity for testing
DEFAULT_SELECTED_PASSIVES = {
    "Warrior": ["knockback_immunity"],  # Barbarian-style immunity (KEEP FOR TESTING)
    "Hunter": [],                       # DISABLED - was causing AP issues
    "Rogue": [],                        # DISABLED - was causing AP issues
    "Mage": [],                         # DISABLED - was causing AP issues
    "Cleric": [],                       # DISABLED - was causing AP issues
    "King": [],                         # DISABLED - was causing AP issues
    "Pawn": []                         # No default passives
}

def load_ability_selections():
    """Load ability selections from file, or return defaults if file doesn't exist."""
    try:
        if os.path.exists(ABILITIES_SAVE_FILE):
            with open(ABILITIES_SAVE_FILE, 'r') as f:
                loaded_selections = json.load(f)
                # Ensure all unit types are present, fill in defaults for missing ones
                for unit_type, default_abilities in DEFAULT_SELECTED_ABILITIES.items():
                    if unit_type not in loaded_selections:
                        loaded_selections[unit_type] = default_abilities
                return loaded_selections
        else:
            print(f"No saved ability selections found. Using defaults.")
            return DEFAULT_SELECTED_ABILITIES.copy()
    except Exception as e:
        print(f"Error loading ability selections: {e}. Using defaults.")
        return DEFAULT_SELECTED_ABILITIES.copy()

def save_ability_selections(selections):
    """Save ability selections to file."""
    try:
        with open(ABILITIES_SAVE_FILE, 'w') as f:
            json.dump(selections, f, indent=2)
        print(f"Ability selections saved to {ABILITIES_SAVE_FILE}")
        return True
    except Exception as e:
        print(f"Error saving ability selections: {e}")
        return False

def load_passive_selections():
    """Load passive selections from file, or return defaults if file doesn't exist."""
    try:
        if os.path.exists(PASSIVES_SAVE_FILE):
            with open(PASSIVES_SAVE_FILE, 'r') as f:
                loaded_selections = json.load(f)
                # Ensure all unit types are present, fill in defaults for missing ones
                for unit_type, default_passives in DEFAULT_SELECTED_PASSIVES.items():
                    if unit_type not in loaded_selections:
                        loaded_selections[unit_type] = default_passives
                return loaded_selections
        else:
            return DEFAULT_SELECTED_PASSIVES.copy()
    except Exception as e:
        print(f"Error loading passive selections: {e}")
        return DEFAULT_SELECTED_PASSIVES.copy()

def save_passive_selections(selections):
    """Save passive selections to file."""
    try:
        with open(PASSIVES_SAVE_FILE, 'w') as f:
            json.dump(selections, f, indent=2)
        print(f"Passive selections saved to {PASSIVES_SAVE_FILE}")
        return True
    except Exception as e:
        print(f"Error saving passive selections: {e}")
        return False

# Load ability and passive selections on module import
SELECTED_ABILITIES = load_ability_selections()
SELECTED_PASSIVES = load_passive_selections()

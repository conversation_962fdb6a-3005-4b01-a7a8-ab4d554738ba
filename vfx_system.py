import pygame
import math
import random
from pathlib import Path

class VFXManager:
    """
    Visual Effects Manager for the game.
    Handles particles, animations, and visual feedback.
    """
    
    def __init__(self):
        self.active_effects = []
        self.particle_systems = []
        
        # VFX settings
        self.enabled = True
        self.quality = "high"  # "low", "medium", "high"
        
        # Load VFX assets
        self.textures = {}
        self._load_vfx_assets()
    
    def _load_vfx_assets(self):
        """Load VFX textures and sprites"""
        vfx_dir = Path("assets/vfx")
        vfx_dir.mkdir(parents=True, exist_ok=True)
        
        # Define VFX categories
        vfx_categories = {
            "particles": ["spark.png", "smoke.png", "magic.png", "blood.png"],
            "effects": ["explosion.png", "heal.png", "shield.png", "curse.png"],
            "projectiles": ["fireball.png", "arrow.png", "bolt.png", "beam.png"]
        }
        
        for category, files in vfx_categories.items():
            category_dir = vfx_dir / category
            category_dir.mkdir(exist_ok=True)
            
            for file_name in files:
                file_path = category_dir / file_name
                texture_key = f"{category}_{file_name.replace('.png', '')}"
                
                if file_path.exists():
                    try:
                        self.textures[texture_key] = pygame.image.load(str(file_path)).convert_alpha()
                    except pygame.error as e:
                        print(f"Could not load VFX texture {file_path}: {e}")
                        self._create_placeholder_texture(texture_key)
                else:
                    self._create_placeholder_texture(texture_key)
    
    def _create_placeholder_texture(self, texture_key):
        """Create placeholder texture for missing VFX assets"""
        # Create simple colored squares as placeholders
        size = 16
        surf = pygame.Surface((size, size), pygame.SRCALPHA)
        
        # Different colors for different types
        if "fire" in texture_key or "explosion" in texture_key:
            color = (255, 100, 0, 200)
        elif "heal" in texture_key or "blessing" in texture_key:
            color = (0, 255, 100, 200)
        elif "magic" in texture_key or "bolt" in texture_key:
            color = (100, 100, 255, 200)
        elif "curse" in texture_key or "dark" in texture_key:
            color = (150, 0, 150, 200)
        else:
            color = (255, 255, 255, 200)
        
        pygame.draw.circle(surf, color, (size//2, size//2), size//2)
        self.textures[texture_key] = surf
    
    def create_ability_effect(self, ability_name, position, class_name=""):
        """Create visual effect for ability use"""
        if not self.enabled:
            return
        
        x, y = position
        
        # Define effects for different abilities
        if "fire" in ability_name.lower() or ability_name == "Fireball":
            self._create_fire_effect(x, y)
        elif "heal" in ability_name.lower() or "blessing" in ability_name.lower():
            self._create_heal_effect(x, y)
        elif "lightning" in ability_name.lower() or "bolt" in ability_name.lower():
            self._create_lightning_effect(x, y)
        elif "curse" in ability_name.lower() or "dark" in ability_name.lower():
            self._create_dark_effect(x, y)
        elif "shield" in ability_name.lower() or "protection" in ability_name.lower():
            self._create_shield_effect(x, y)
        elif "explosion" in ability_name.lower() or "shatter" in ability_name.lower():
            self._create_explosion_effect(x, y)
        else:
            self._create_generic_magic_effect(x, y)
    
    def _create_fire_effect(self, x, y):
        """Create fire/explosion effect"""
        for i in range(15):
            particle = FireParticle(x, y)
            self.particle_systems.append(particle)
    
    def _create_heal_effect(self, x, y):
        """Create healing effect"""
        for i in range(10):
            particle = HealParticle(x, y)
            self.particle_systems.append(particle)
        
        # Add healing glow
        glow = GlowEffect(x, y, (0, 255, 100), 60)
        self.active_effects.append(glow)
    
    def _create_lightning_effect(self, x, y):
        """Create lightning effect"""
        lightning = LightningEffect(x, y)
        self.active_effects.append(lightning)
    
    def _create_dark_effect(self, x, y):
        """Create dark magic effect"""
        for i in range(12):
            particle = DarkParticle(x, y)
            self.particle_systems.append(particle)
    
    def _create_shield_effect(self, x, y):
        """Create shield/protection effect"""
        shield = ShieldEffect(x, y)
        self.active_effects.append(shield)
    
    def _create_explosion_effect(self, x, y):
        """Create explosion effect"""
        for i in range(20):
            particle = ExplosionParticle(x, y)
            self.particle_systems.append(particle)
    
    def _create_generic_magic_effect(self, x, y):
        """Create generic magic effect"""
        for i in range(8):
            particle = MagicParticle(x, y)
            self.particle_systems.append(particle)
    
    def create_damage_number(self, position, damage, color=(255, 0, 0)):
        """Create floating damage number"""
        x, y = position
        damage_text = DamageNumber(x, y, str(damage), color)
        self.active_effects.append(damage_text)
    
    def create_heal_number(self, position, heal_amount):
        """Create floating heal number"""
        x, y = position
        heal_text = DamageNumber(x, y, f"+{heal_amount}", (0, 255, 0))
        self.active_effects.append(heal_text)
    
    def update(self, dt):
        """Update all active effects"""
        if not self.enabled:
            return
        
        # Update particles
        self.particle_systems = [p for p in self.particle_systems if p.update(dt)]
        
        # Update effects
        self.active_effects = [e for e in self.active_effects if e.update(dt)]
    
    def render(self, screen):
        """Render all effects"""
        if not self.enabled:
            return
        
        # Render particles
        for particle in self.particle_systems:
            particle.render(screen)
        
        # Render effects
        for effect in self.active_effects:
            effect.render(screen)
    
    def clear_all_effects(self):
        """Clear all active effects"""
        self.particle_systems.clear()
        self.active_effects.clear()
    
    def set_quality(self, quality):
        """Set VFX quality (low, medium, high)"""
        self.quality = quality
    
    def toggle_vfx(self):
        """Toggle VFX on/off"""
        self.enabled = not self.enabled
        if not self.enabled:
            self.clear_all_effects()
        return self.enabled

# Particle Classes
class Particle:
    """Base particle class"""
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.vx = random.uniform(-2, 2)
        self.vy = random.uniform(-2, 2)
        self.life = 1.0
        self.max_life = 1.0
        self.size = random.uniform(2, 6)
        self.color = (255, 255, 255)
    
    def update(self, dt):
        self.x += self.vx * dt * 60
        self.y += self.vy * dt * 60
        self.life -= dt * 2
        return self.life > 0
    
    def render(self, screen):
        if self.life > 0:
            alpha = int(255 * (self.life / self.max_life))
            color = (*self.color, alpha)
            size = int(self.size * (self.life / self.max_life))
            if size > 0:
                pygame.draw.circle(screen, self.color, (int(self.x), int(self.y)), size)

class FireParticle(Particle):
    def __init__(self, x, y):
        super().__init__(x, y)
        self.color = (255, random.randint(50, 150), 0)
        self.vy = random.uniform(-3, -1)
        self.life = random.uniform(0.5, 1.5)
        self.max_life = self.life

class HealParticle(Particle):
    def __init__(self, x, y):
        super().__init__(x, y)
        self.color = (0, 255, random.randint(100, 200))
        self.vy = random.uniform(-2, -0.5)
        self.life = random.uniform(1.0, 2.0)
        self.max_life = self.life

class DarkParticle(Particle):
    def __init__(self, x, y):
        super().__init__(x, y)
        self.color = (random.randint(100, 150), 0, random.randint(100, 150))
        self.life = random.uniform(0.8, 1.2)
        self.max_life = self.life

class ExplosionParticle(Particle):
    def __init__(self, x, y):
        super().__init__(x, y)
        angle = random.uniform(0, 2 * math.pi)
        speed = random.uniform(2, 8)
        self.vx = math.cos(angle) * speed
        self.vy = math.sin(angle) * speed
        self.color = (255, random.randint(100, 200), 0)
        self.life = random.uniform(0.3, 0.8)
        self.max_life = self.life

class MagicParticle(Particle):
    def __init__(self, x, y):
        super().__init__(x, y)
        self.color = (random.randint(100, 255), random.randint(100, 255), 255)
        self.life = random.uniform(0.8, 1.5)
        self.max_life = self.life

# Effect Classes
class Effect:
    """Base effect class"""
    def __init__(self, x, y, duration=1.0):
        self.x = x
        self.y = y
        self.duration = duration
        self.time = 0
    
    def update(self, dt):
        self.time += dt
        return self.time < self.duration
    
    def render(self, screen):
        pass

class GlowEffect(Effect):
    def __init__(self, x, y, color, radius, duration=1.0):
        super().__init__(x, y, duration)
        self.color = color
        self.radius = radius
    
    def render(self, screen):
        progress = self.time / self.duration
        alpha = int(255 * (1 - progress))
        radius = int(self.radius * (1 - progress * 0.5))
        
        if radius > 0:
            # Create glow surface
            glow_surf = pygame.Surface((radius * 2, radius * 2), pygame.SRCALPHA)
            pygame.draw.circle(glow_surf, (*self.color, alpha), (radius, radius), radius)
            screen.blit(glow_surf, (self.x - radius, self.y - radius), special_flags=pygame.BLEND_ADD)

class LightningEffect(Effect):
    def __init__(self, x, y):
        super().__init__(x, y, 0.2)
        self.points = self._generate_lightning_points()
    
    def _generate_lightning_points(self):
        points = [(self.x, self.y - 50)]
        current_x, current_y = self.x, self.y - 50
        
        while current_y < self.y + 50:
            current_x += random.uniform(-10, 10)
            current_y += random.uniform(5, 15)
            points.append((current_x, current_y))
        
        return points
    
    def render(self, screen):
        if len(self.points) > 1:
            pygame.draw.lines(screen, (255, 255, 255), False, self.points, 3)
            pygame.draw.lines(screen, (200, 200, 255), False, self.points, 1)

class ShieldEffect(Effect):
    def __init__(self, x, y):
        super().__init__(x, y, 2.0)
    
    def render(self, screen):
        progress = self.time / self.duration
        alpha = int(255 * (1 - progress))
        radius = 30
        
        # Draw shield circle
        shield_surf = pygame.Surface((radius * 2, radius * 2), pygame.SRCALPHA)
        pygame.draw.circle(shield_surf, (0, 150, 255, alpha), (radius, radius), radius, 3)
        screen.blit(shield_surf, (self.x - radius, self.y - radius))

class DamageNumber(Effect):
    def __init__(self, x, y, text, color):
        super().__init__(x, y, 1.5)
        self.text = text
        self.color = color
        self.font = pygame.font.Font(None, 24)
        self.vy = -2
    
    def update(self, dt):
        self.y += self.vy * dt * 60
        return super().update(dt)
    
    def render(self, screen):
        progress = self.time / self.duration
        alpha = int(255 * (1 - progress))
        
        text_surf = self.font.render(self.text, True, self.color)
        text_surf.set_alpha(alpha)
        screen.blit(text_surf, (self.x - text_surf.get_width()//2, self.y))

# Global VFX manager instance
vfx_manager = VFXManager()

# Convenience functions
def create_ability_vfx(ability_name, position, class_name=""):
    """Create VFX for ability use"""
    vfx_manager.create_ability_effect(ability_name, position, class_name)

def create_damage_vfx(position, damage):
    """Create damage number VFX"""
    vfx_manager.create_damage_number(position, damage)

def create_heal_vfx(position, heal_amount):
    """Create heal number VFX"""
    vfx_manager.create_heal_number(position, heal_amount)

def update_vfx(dt):
    """Update all VFX"""
    vfx_manager.update(dt)

def render_vfx(screen):
    """Render all VFX"""
    vfx_manager.render(screen)

def toggle_vfx():
    """Toggle VFX on/off"""
    return vfx_manager.toggle_vfx()

# VFX setup instructions
VFX_SETUP_INSTRUCTIONS = """
✨ VFX SYSTEM SETUP INSTRUCTIONS

1. CREATE VFX DIRECTORIES:
   Create these folders in your project:
   - assets/vfx/particles/
   - assets/vfx/effects/
   - assets/vfx/projectiles/

2. ADD VFX ASSETS (OPTIONAL):
   Place .png files in appropriate directories:
   - particles/spark.png, particles/smoke.png, etc.
   - effects/explosion.png, effects/heal.png, etc.
   - projectiles/fireball.png, projectiles/arrow.png, etc.

3. INTEGRATE WITH GAME LOOP:
   Add this to your main game loop:
   ```python
   from vfx_system import update_vfx, render_vfx
   
   # In update loop
   update_vfx(dt)
   
   # In render loop (after drawing game objects)
   render_vfx(screen)
   ```

4. INTEGRATE WITH ABILITIES:
   Add this to ability execution:
   ```python
   from vfx_system import create_ability_vfx, create_damage_vfx
   
   def use_ability(self, ability_name, target_pos):
       # Create VFX at target position
       create_ability_vfx(ability_name, target_pos, self.name)
       
       # Create damage numbers
       if damage > 0:
           create_damage_vfx(target_pos, damage)
   ```

5. FREE VFX RESOURCES:
   - opengameart.org
   - itch.io (free game assets)
   - kenney.nl (free game assets)
   - Adobe After Effects (create your own)
"""

if __name__ == "__main__":
    print(VFX_SETUP_INSTRUCTIONS)

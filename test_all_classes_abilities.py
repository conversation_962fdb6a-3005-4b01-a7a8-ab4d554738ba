#!/usr/bin/env python3
"""
Test all class abilities to identify issues similar to Warrior.
"""

import pygame
pygame.init()

from game_state import Game
from units.warrior import Warrior
from units.rogue import Rogue
from units.mage import Mage
from units.hunter import Hunter
from units.cleric import <PERSON><PERSON><PERSON>

def test_class_abilities(unit_class, class_name, target_class=None):
    """Test abilities for a specific class"""
    print(f"🧪 TESTING {class_name.upper()} ABILITIES")
    print("-" * 40)
    
    game = Game()
    unit = unit_class(1)
    
    if target_class:
        target = target_class(2)
        game.board.add_unit(target, 4, 5)
        target_pos = target.position
    else:
        target_pos = (4, 5)  # Empty position
    
    game.board.add_unit(unit, 4, 4)
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Unit: {unit.name}")
    print(f"Abilities: {[ability.name for ability in unit.abilities]}")
    print()
    
    # Test each ability (skip Move and Basic Attack)
    for i in range(2, len(unit.abilities)):
        ability = unit.abilities[i]
        print(f"Testing {ability.name}...")
        
        # Reset for each test
        game.current_player_ap = 10
        unit.has_acted_this_turn = False
        if hasattr(game, 'units_acted_this_turn'):
            game.units_acted_this_turn.discard(unit)
        
        try:
            # Get valid targets
            valid_targets = unit.get_ability_targets(i, game.board)
            if valid_targets:
                test_target = valid_targets[0]
            else:
                test_target = target_pos
            
            success = unit.use_ability(i, test_target, game)
            print(f"  {ability.name}: {'✅ SUCCESS' if success else '❌ FAILED'}")
            
        except Exception as e:
            print(f"  {ability.name}: ❌ ERROR - {e}")
        
        print()
    
    print(f"✅ {class_name} testing completed!\n")

def main():
    print("🎮 TESTING ALL CLASS ABILITIES")
    print("=" * 50)
    
    # Test each class
    test_class_abilities(Warrior, "Warrior", Rogue)
    test_class_abilities(Rogue, "Rogue", Warrior)
    test_class_abilities(Mage, "Mage", Warrior)
    test_class_abilities(Hunter, "Hunter", Warrior)
    test_class_abilities(Cleric, "Cleric", Warrior)
    
    print("🎉 ALL CLASS TESTING COMPLETED!")

if __name__ == "__main__":
    main()

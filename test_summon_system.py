#!/usr/bin/env python3

import pygame
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state import Game
from units.warrior import Warrior
from units.rogue import Rogue
from units.mage import Mage
from units.cleric import Cleric
from units.hunter import Hunter

def test_summon_system():
    """Test if summon abilities work for all classes"""
    print("🔍 TESTING SUMMON SYSTEM")
    print("=" * 40)
    
    # Initialize pygame
    pygame.init()
    
    # Test each class
    classes_to_test = [
        ("Warrior", Warrior),
        ("Rogue", Rogue),
        ("Mage", Mage),
        ("<PERSON><PERSON><PERSON>", <PERSON><PERSON><PERSON>),
        ("<PERSON>", <PERSON>)
    ]
    
    results = {}
    
    for class_name, class_type in classes_to_test:
        print(f"\n📋 TESTING {class_name.upper()} SUMMON")
        print("-" * 30)
        
        try:
            # Create a game instance
            game = Game()
            
            # Create unit
            unit = class_type(player_id=1)
            game.board.add_unit(unit, 4, 4)
            
            # Set up game state
            game.current_player = 1
            game.current_player_ap = 10
            game.units_acted_this_turn = set()
            
            print(f"{class_name} at {unit.position}")
            print(f"Game AP: {game.current_player_ap}")
            print(f"{class_name} abilities: {[a.name for a in unit.abilities]}")
            
            # Find Summon ability
            summon_idx = None
            for i, ability in enumerate(unit.abilities):
                if ability.name == "Summon":
                    summon_idx = i
                    break
            
            if summon_idx is None:
                print(f"❌ {class_name} has no Summon ability!")
                results[class_name] = {"has_summon": False, "summon_works": False}
                continue
            
            print(f"✓ {class_name} has Summon ability (index {summon_idx})")
            print(f"  AP cost: {unit.abilities[summon_idx].ap_cost}")
            
            # Get valid summon targets
            valid_targets = unit.get_ability_targets(summon_idx, game.board)
            print(f"✓ Valid summon targets: {len(valid_targets)}")
            print(f"  Targets: {valid_targets[:5]}...")  # Show first 5
            
            if not valid_targets:
                print(f"❌ {class_name} has no valid summon targets!")
                results[class_name] = {"has_summon": True, "summon_works": False, "no_targets": True}
                continue
            
            # Try to summon at first valid target
            target_pos = valid_targets[0]
            print(f"Attempting to summon at {target_pos}")
            
            success = unit.use_ability(summon_idx, target_pos, game)
            print(f"Summon success: {success}")
            print(f"Game AP after summon: {game.current_player_ap}")
            
            # Check if a unit was actually summoned
            summoned_unit = game.board.units.get(target_pos)
            if summoned_unit and summoned_unit != unit:
                print(f"✓ Summoned unit: {summoned_unit.name} at {target_pos}")
                print(f"  Summoned unit player: {summoned_unit.player_id}")
                print(f"  Summoned unit health: {summoned_unit.health}/{summoned_unit.max_health}")
                
                results[class_name] = {
                    "has_summon": True,
                    "summon_works": True,
                    "ap_deducted": 10 - game.current_player_ap,
                    "summoned_unit": summoned_unit.name
                }
            else:
                print(f"❌ No unit was summoned at {target_pos}")
                results[class_name] = {
                    "has_summon": True,
                    "summon_works": False,
                    "ap_deducted": 10 - game.current_player_ap
                }
                
        except Exception as e:
            print(f"❌ {class_name} SUMMON FAILED: {e}")
            results[class_name] = {
                "has_summon": False,
                "summon_works": False,
                "error": str(e)
            }
    
    # Summary
    print(f"\n" + "=" * 40)
    print("🎯 SUMMON SYSTEM SUMMARY")
    print("-" * 40)
    
    working_summons = []
    broken_summons = []
    
    for class_name, result in results.items():
        if result.get("summon_works", False):
            working_summons.append(class_name)
            summoned = result.get("summoned_unit", "Unknown")
            ap_used = result.get("ap_deducted", 0)
            print(f"✅ {class_name}: Summon works, created {summoned}, {ap_used} AP used")
        else:
            broken_summons.append(class_name)
            if not result.get("has_summon", False):
                print(f"❌ {class_name}: No summon ability")
            elif result.get("no_targets", False):
                print(f"❌ {class_name}: No valid targets")
            else:
                error_msg = result.get("error", "Summon failed")
                print(f"❌ {class_name}: {error_msg}")
    
    print(f"\n📊 SUMMON RESULTS:")
    print(f"✅ Working: {len(working_summons)}/{len(classes_to_test)} - {working_summons}")
    print(f"❌ Broken: {len(broken_summons)}/{len(classes_to_test)} - {broken_summons}")
    
    return len(working_summons) == len(classes_to_test)

if __name__ == "__main__":
    success = test_summon_system()
    if success:
        print("\n🎉 ALL SUMMON ABILITIES WORKING!")
    else:
        print("\n⚠️ SOME SUMMON ABILITIES NEED FIXING!")

#!/usr/bin/env python3

import pygame
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state import Game
from units.rogue import Rogue
from units.warrior import Warrior

def test_rogue_abilities():
    """Test if Rogue abilities work correctly"""
    print("🗡️ TESTING ROGUE ABILITIES")
    print("=" * 40)
    
    # Initialize pygame (required for unit creation)
    pygame.init()
    
    # Create a game instance
    game = Game()
    
    # Create a Rogue and a target
    rogue = Rogue(player_id=1)
    target = Warrior(player_id=2)
    
    game.board.add_unit(rogue, 4, 4)
    game.board.add_unit(target, 3, 3)  # Diagonally adjacent for backstab
    
    # Set up game state
    game.current_player = 1
    game.current_player_ap = 10  # Plenty of AP
    game.units_acted_this_turn = set()
    
    print(f"Rogue at {rogue.position}")
    print(f"Target at {target.position}")
    print(f"Initial AP: {game.current_player_ap}")
    print(f"Target HP: {target.health}/{target.max_health}")
    
    # Test 1: Basic Attack
    print("\n📋 TEST 1: Basic Attack")
    attack_idx = 1  # Basic Attack
    
    success = rogue.use_ability(attack_idx, target.position, game)
    print(f"Basic attack success: {success}")
    print(f"Target HP after attack: {target.health}/{target.max_health}")
    print(f"AP after attack: {game.current_player_ap}")
    
    # Reset for next test
    target.health = target.max_health
    game.current_player_ap = 10
    game.units_acted_this_turn = set()
    
    # Test 2: Backstab
    print("\n📋 TEST 2: Backstab Ability")
    backstab_idx = None
    for i, ability in enumerate(rogue.abilities):
        if ability.name == "Backstab":
            backstab_idx = i
            break
    
    if backstab_idx is None:
        print("❌ Rogue doesn't have Backstab ability!")
        return False
    
    print(f"✓ Rogue has Backstab ability (index {backstab_idx})")
    print(f"  AP cost: {rogue.abilities[backstab_idx].ap_cost}")
    
    # Get valid backstab targets
    valid_targets = rogue.get_ability_targets(backstab_idx, game.board)
    print(f"✓ Valid backstab targets: {len(valid_targets)}")
    print(f"  Targets: {valid_targets}")
    
    if target.position in valid_targets:
        print(f"✓ Target {target.position} is valid for backstab")
        
        # Use backstab on the target
        success = rogue.use_ability(backstab_idx, target.position, game)
        print(f"Backstab success: {success}")
        print(f"Target HP after backstab: {target.health}/{target.max_health}")
        print(f"AP after backstab: {game.current_player_ap}")
        print(f"Rogue position after backstab: {rogue.position}")
        
        if success and target.health < target.max_health:
            print("✅ Backstab dealt damage!")
            return True
        else:
            print("❌ Backstab didn't deal damage!")
            return False
    else:
        print(f"❌ Target {target.position} not valid for backstab")
        return False

if __name__ == "__main__":
    success = test_rogue_abilities()
    if success:
        print("\n🎉 ROGUE ABILITIES WORKING!")
    else:
        print("\n❌ ROGUE ABILITIES BROKEN!")

#!/usr/bin/env python3

import pygame
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state import Game
from units.rogue import Rogue
from units.warrior import Warrior

def test_rogue_assassination():
    """Test that Rogue assassination ability works without errors"""
    print("🗡️ TESTING ROGUE ASSASSINATION FIX")
    print("=" * 40)
    
    # Initialize pygame
    pygame.init()
    
    try:
        # Create a game instance
        game = Game()
        
        # Create units
        rogue = Rogue(player_id=1)
        target = Warrior(player_id=2)
        
        # Position rogue and target diagonally adjacent
        game.board.add_unit(rogue, 4, 4)
        game.board.add_unit(target, 5, 5)  # Diagonally adjacent
        
        # Set up game state
        game.current_player = 1
        game.current_player_ap = 10
        game.units_acted_this_turn = set()
        
        print(f"Rogue at {rogue.position}")
        print(f"Target at {target.position}")
        print(f"Initial AP: {game.current_player_ap}")
        print(f"Target HP: {target.health}/{target.max_health}")
        
        # Find assassination ability
        assassination_idx = None
        for i, ability in enumerate(rogue.abilities):
            if ability.name == "Assassination":
                assassination_idx = i
                break
        
        if assassination_idx is None:
            print("❌ Assassination ability not found!")
            return False
        
        print(f"✓ Found Assassination ability (index {assassination_idx})")
        print(f"  AP cost: {rogue.abilities[assassination_idx].ap_cost}")
        
        # Get valid targets
        valid_targets = rogue.get_ability_targets(assassination_idx, game.board)
        print(f"✓ Valid assassination targets: {valid_targets}")
        
        if target.position not in valid_targets:
            print(f"❌ Target {target.position} not in valid targets!")
            return False
        
        print(f"✓ Target {target.position} is valid for assassination")
        
        # Test assassination
        print(f"\n🗡️ TESTING ASSASSINATION")
        print(f"Calling rogue.use_ability({assassination_idx}, {target.position}, game)")
        
        success = rogue.use_ability(assassination_idx, target.position, game)
        
        print(f"Assassination success: {success}")
        print(f"Target HP after assassination: {target.health}/{target.max_health}")
        print(f"AP after assassination: {game.current_player_ap}")
        
        if success:
            print("✅ ASSASSINATION WORKS - NO MORE ERRORS!")
            return True
        else:
            print("❌ ASSASSINATION FAILED")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_rogue_assassination()
    if success:
        print("\n🎉 ROGUE ASSASSINATION FIX SUCCESSFUL!")
    else:
        print("\n💥 ROGUE ASSASSINATION STILL BROKEN!")

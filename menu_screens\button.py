import pygame

# Colors used by <PERSON><PERSON> (moved from main_menu.py)
LIGHT_GRAY = (200, 200, 200)
HIGHLIGHT_COLOR = (0, 150, 255)
BUTTON_COLOR = (45, 45, 45)

class Button:
    def __init__(self, x, y, width, height, text):
        self.rect = pygame.Rect(x, y, width, height)
        self.text = text
        self.is_hovered = False
        
    def draw(self, surface, font):
        color = HIGHLIGHT_COLOR if self.is_hovered else BUTTON_COLOR
        
        # Draw button background with anti-aliased corners
        pygame.draw.rect(surface, color, self.rect, border_radius=8)
        
        # Draw text
        text_surface = font.render(self.text, True, LIGHT_GRAY)
        text_rect = text_surface.get_rect(center=self.rect.center)
        surface.blit(text_surface, text_rect)
        
    def handle_event(self, event):
        if event.type == pygame.MOUSEMOTION:
            self.is_hovered = self.rect.collidepoint(event.pos)
            
        if event.type == pygame.MOUSEBUTTONDOWN:
            if self.is_hovered:
                return True
        return False 
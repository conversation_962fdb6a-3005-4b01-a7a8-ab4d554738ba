#!/usr/bin/env python3

import pygame
import sys
import os
import json

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_config_debug():
    """Debug the configuration system step by step"""
    print("🔍 CONFIGURATION DEBUG TEST")
    print("=" * 40)
    
    # Initialize pygame
    pygame.init()
    
    # Test 1: Check current JSON file
    print("\n📋 TEST 1: Current JSON File")
    with open("game_balance_config.json", 'r') as f:
        config = json.load(f)
    
    print(f"Warrior HP in JSON: {config['class_data']['Warrior']['hp']}")
    print(f"Has ability_data: {'ability_data' in config}")
    if 'ability_data' in config:
        print(f"Warrior abilities: {config['ability_data'].get('Warrior', 'None')}")
    
    # Test 2: Check config_loader
    print("\n📋 TEST 2: Config Loader State")
    from config_loader import config_loader
    
    print(f"Config loader has data: {config_loader.config_data is not None}")
    if config_loader.config_data:
        print(f"Config data keys: {list(config_loader.config_data.keys())}")
        print(f"Warrior HP from loader: {config_loader.get_class_hp('Warrior')}")
        print(f"Warrior Attack cost from loader: {config_loader.get_ability_ap_cost('Warrior', 'Attack')}")
    
    # Test 3: Modify JSON and reload
    print("\n📋 TEST 3: Modify and Reload")
    
    # Backup original
    original_config = config.copy()
    
    # Modify
    config["class_data"]["Warrior"]["hp"] = 88
    config["ability_data"] = config.get("ability_data", {})
    config["ability_data"]["Warrior"] = {"Attack": {"ap_cost": 8}}
    
    # Save
    with open("game_balance_config.json", 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ JSON file modified")
    
    # Reload
    from config_loader import reload_configuration
    reload_configuration()
    
    print("✅ Configuration reloaded")
    
    # Test 4: Check loader after reload
    print("\n📋 TEST 4: Config Loader After Reload")
    print(f"Config loader has data: {config_loader.config_data is not None}")
    if config_loader.config_data:
        print(f"Config data keys: {list(config_loader.config_data.keys())}")
        print(f"Warrior HP from loader: {config_loader.get_class_hp('Warrior')}")
        print(f"Warrior Attack cost from loader: {config_loader.get_ability_ap_cost('Warrior', 'Attack')}")
        
        # Check raw data
        print(f"Raw Warrior data: {config_loader.config_data['class_data']['Warrior']}")
        if 'ability_data' in config_loader.config_data:
            print(f"Raw Warrior abilities: {config_loader.config_data['ability_data'].get('Warrior', 'None')}")
    
    # Test 5: Create unit
    print("\n📋 TEST 5: Create Unit with Modified Config")
    from units.warrior import Warrior
    
    warrior = Warrior(player_id=1)
    print(f"Warrior HP: {warrior.max_health}")
    print(f"Warrior Attack Cost: {warrior.abilities[1].ap_cost}")
    
    # Restore original
    with open("game_balance_config.json", 'w') as f:
        json.dump(original_config, f, indent=2)
    
    print("✅ Original configuration restored")
    
    # Check results
    hp_correct = warrior.max_health == 88
    cost_correct = warrior.abilities[1].ap_cost == 8
    
    print(f"\nResults:")
    print(f"HP correct: {'✅' if hp_correct else '❌'} (Expected: 88, Got: {warrior.max_health})")
    print(f"Cost correct: {'✅' if cost_correct else '❌'} (Expected: 8, Got: {warrior.abilities[1].ap_cost})")
    
    return hp_correct and cost_correct

if __name__ == "__main__":
    success = test_config_debug()
    if success:
        print("\n🎉 CONFIGURATION SYSTEM WORKING!")
    else:
        print("\n❌ CONFIGURATION SYSTEM BROKEN!")

import pygame
import sys
from pygame import gfxdraw
from menu_screens.button import <PERSON><PERSON>
from game_settings import SELECTED_ABILITIES, save_ability_selections, SELECTED_PASSIVES, save_passive_selections # For reading and writing selected abilities and passives
from passive_system import get_available_passives_for_class, PASSIVE_ABILITIES

# Import unit classes directly (assuming they are in sys.path or accessible)
# This might need adjustment based on final project structure for units
from units.warrior import Warrior
from units.hunter import Hunter
from units.rogue import Rogue
from units.mage import Mage
from units.cleric import Cleric
from units.king import King
from units.pawn import Pawn # Assuming Pawn is a unit class too
from units.warlock import Warlock
from units.paladin import Paladin
from units.druid import Druid
from units.bard import Bard

# Constants - ideally from a shared source
WINDOW_WIDTH = 1280
WINDOW_HEIGHT = 720
FPS = 60
DARK_GRAY = (30, 30, 30)
LIGHT_GRAY = (200, 200, 200)
HIGHLIGHT_COLOR = (0, 150, 255) # For hover effects if not covered by Button
SELECTED_BUTTON_COLOR = (0, 100, 150) # Specific for selected abilities
BUTTON_COLOR = (45, 45, 45) # Default button color if Button class doesn't provide it
DISABLED_COLOR = (100, 100, 100)
DESCRIPTION_BG_COLOR = (40, 40, 40)

UNIT_CLASSES = {
    "Warrior": Warrior, "Hunter": Hunter, "Rogue": Rogue,
    "Mage": Mage, "Cleric": Cleric, "King": King, "Pawn": Pawn,
    "Warlock": Warlock, "Paladin": Paladin, "Druid": Druid, "Bard": Bard
}
MAX_SELECTED_SPECIAL_ABILITIES = 5 # Max special abilities a unit can have selected

class AbilitySelectionMenu:
    def __init__(self, screen, clock):
        self.screen = screen
        self.clock = clock
        self.running = True
        
        self.title_font = pygame.font.Font(None, 60)
        self.unit_name_font = pygame.font.Font(None, 48)
        self.ability_font = pygame.font.Font(None, 32)
        self.small_font = pygame.font.Font(None, 24)
        
        self.selected_unit_type = "Hunter"  # Default
        self.unit_buttons = self._create_unit_buttons()

        # Tab system for abilities vs passives
        self.current_tab = "abilities"  # "abilities" or "passives"
        self.tab_buttons = self._create_tab_buttons()

        self.scroll_y_offset = 0
        self.dragging_scrollbar = False
        self.scrollbar_rect = None
        self.scroll_content_height = 0

        # ability_display_rect is where abilities are listed
        self.ability_display_rect = pygame.Rect(WINDOW_WIDTH // 2 - 250, 220, 500, WINDOW_HEIGHT - 340)
        # selected_display_rect is where currently selected special abilities are shown
        self.selected_display_rect = pygame.Rect(WINDOW_WIDTH // 2 + 270, 220, 280, WINDOW_HEIGHT - 390)

        self.ability_buttons = {}  # Store buttons for each ability {ability_name: Button}
        self.passive_buttons = {}  # Store buttons for each passive {passive_name: Button}
        self.selected_ability_preview = None # Details of ability to show description for

        # Save feedback message
        self.save_message = ""
        self.save_message_timer = 0
        
        self._initialize_selected_abilities() # Ensure SELECTED_ABILITIES global has entries for all units
        self._update_ability_buttons() # Create buttons for the default selected unit

        self.back_button = Button(WINDOW_WIDTH - 150, WINDOW_HEIGHT - 70, 120, 40, "Back")
        self.save_button = Button(WINDOW_WIDTH - 150, WINDOW_HEIGHT - 130, 120, 40, "Save")


    def _initialize_selected_abilities(self):
        for unit_name in UNIT_CLASSES.keys():
            if unit_name not in SELECTED_ABILITIES:
                SELECTED_ABILITIES[unit_name] = [] # Initialize with empty list if not present

    def _create_unit(self, unit_type, player_id=0): # Removed unused team_id
        if unit_type in UNIT_CLASSES:
            try:
                unit = UNIT_CLASSES[unit_type](player_id=player_id) # Call with only player_id
                # Set board and position manually if needed by ability listing, though likely not.
                unit.board = None 
                unit.position = (-1,-1) 
                return unit
            except TypeError as e:
                print(f"Error creating unit {unit_type} for ability menu: {e}")
                print("Ensure unit __init__ takes player_id.")
                return None
        return None

    def _create_tab_buttons(self):
        """Create tab buttons for abilities and passives"""
        tab_buttons = {}
        tab_width = 150
        tab_height = 40
        start_x = WINDOW_WIDTH // 2 - tab_width
        tab_y = 170

        tab_buttons["abilities"] = Button(start_x, tab_y, tab_width, tab_height, "Abilities")
        tab_buttons["passives"] = Button(start_x + tab_width + 10, tab_y, tab_width, tab_height, "Passives")

        return tab_buttons

    def _create_unit_buttons(self):
        buttons = {}
        button_width = 100
        button_height = 40
        start_x = 50
        y_pos = 100
        spacing = 10
        
        for i, unit_name in enumerate(UNIT_CLASSES.keys()):
            x_offset = (i % 5) * (button_width + spacing) # Arrange in rows
            y_offset = (i // 5) * (button_height + spacing)
            buttons[unit_name] = Button(start_x + x_offset, y_pos + y_offset, button_width, button_height, unit_name)
        return buttons

    def _update_ability_buttons(self):
        self.ability_buttons.clear()
        unit = self._create_unit(self.selected_unit_type)
        if not unit:
            return

        # Display available special abilities (index 2 onwards)
        # Move and Attack are usually fixed (index 0 and 1)
        special_abilities = unit.abilities[2:] 
        
        button_width = self.ability_display_rect.width - 40
        button_height = 40
        x_pos = self.ability_display_rect.left + 20
        
        current_y = self.ability_display_rect.top + 20 - self.scroll_y_offset

        for i, ability in enumerate(special_abilities):
            # Check if this ability is already selected
            is_selected = False
            if self.selected_unit_type in SELECTED_ABILITIES:
                # SELECTED_ABILITIES stores indices relative to the *special* abilities list
                try:
                    # To check if current ability (from special_abilities) is selected,
                    # we need to find its original index in unit.abilities[2:]
                    # then see if THAT index is in SELECTED_ABILITIES[self.selected_unit_type]
                    # This logic was flawed if SELECTED_ABILITIES stored ability objects or names.
                    # Assuming SELECTED_ABILITIES stores the *indices* of special abilities (0-based for special list)
                    if i in SELECTED_ABILITIES[self.selected_unit_type]:
                        is_selected = True
                except Exception as e:
                    print(f"Error checking selected state for {ability.name}: {e}")


            btn = Button(x_pos, current_y, button_width, button_height, ability.name)
            btn.is_selected = is_selected # Custom attribute for visual state
            btn.ability_obj = ability # Store ability object with button
            btn.original_special_index = i # Store its index within the special_abilities list
            self.ability_buttons[ability.name] = btn
            current_y += button_height + 10
        
        self.scroll_content_height = (current_y + self.scroll_y_offset) - (self.ability_display_rect.top + 20)


    def draw_background(self):
        self.screen.fill(DARK_GRAY)
        # Could add a subtle gradient or image like other menus

    def _draw_scrollbar(self):
        # Ensure scroll_content_height is not zero before division
        if not hasattr(self, 'scroll_content_height') or self.scroll_content_height <= 0:
            self.scrollbar_rect = None
            return
            
        content_viewable_ratio = self.ability_display_rect.height / self.scroll_content_height
        if content_viewable_ratio >= 1: # No scrollbar needed
            self.scrollbar_rect = None
            return

        scrollbar_track_rect = pygame.Rect(self.ability_display_rect.right + 5, self.ability_display_rect.top, 15, self.ability_display_rect.height)
        pygame.draw.rect(self.screen, BUTTON_COLOR, scrollbar_track_rect, border_radius=4)

        handle_height = max(20, int(self.ability_display_rect.height * content_viewable_ratio))
        scroll_range = self.scroll_content_height - self.ability_display_rect.height
        # Ensure scroll_range is not zero for division, though handle_y_offset_ratio already checks scroll_range > 0
        if scroll_range <= 0:
            handle_y_offset_ratio = 0
        else:
            handle_y_offset_ratio = self.scroll_y_offset / scroll_range
            
        handle_y = scrollbar_track_rect.top + handle_y_offset_ratio * (scrollbar_track_rect.height - handle_height)
        
        self.scrollbar_rect = pygame.Rect(scrollbar_track_rect.left + 2, handle_y, scrollbar_track_rect.width - 4, handle_height)
        pygame.draw.rect(self.screen, HIGHLIGHT_COLOR, self.scrollbar_rect, border_radius=4)


    def run(self):
        self.running = True
        self._update_ability_buttons() # Ensure scroll_content_height is calculated before first draw
        last_mouse_y = 0 # For scrollbar dragging

        while self.running:
            mouse_pos = pygame.mouse.get_pos()
            current_screen_width, current_screen_height = self.screen.get_size()

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()

                # Handle back/save buttons
                if self.back_button.handle_event(event):
                    self.running = False
                if self.save_button.handle_event(event):
                    # Save ability selections to file permanently
                    if save_ability_selections(SELECTED_ABILITIES):
                        self.save_message = "Ability selections saved permanently!"
                        self.save_message_timer = 120  # Show message for 2 seconds at 60 FPS
                        print("Ability selections saved permanently!")
                    else:
                        self.save_message = "Failed to save ability selections."
                        self.save_message_timer = 120
                        print("Failed to save ability selections.")
                    # Don't close immediately, let user see the message
                    # self.running = False

                # Handle unit selection buttons
                for unit_name, button in self.unit_buttons.items():
                    if button.handle_event(event):
                        self.selected_unit_type = unit_name
                        self.scroll_y_offset = 0 # Reset scroll for new unit
                        self._update_ability_buttons()
                        self.selected_ability_preview = None 
                        break # Avoid multiple unit selections if buttons overlap somehow
                
                # Handle ability selection from the list
                for ability_name, btn in self.ability_buttons.items():
                    # Check if click is within the visible part of ability_display_rect
                    if btn.rect.colliderect(self.ability_display_rect) and btn.handle_event(event):
                        self.selected_ability_preview = btn.ability_obj # Show description
                        
                        current_selections = SELECTED_ABILITIES.get(self.selected_unit_type, [])
                        idx_in_special_list = btn.original_special_index

                        if idx_in_special_list in current_selections:
                            current_selections.remove(idx_in_special_list)
                            btn.is_selected = False
                        else:
                            if len(current_selections) < MAX_SELECTED_SPECIAL_ABILITIES:
                                current_selections.append(idx_in_special_list)
                                btn.is_selected = True
                            else:
                                print(f"Max {MAX_SELECTED_SPECIAL_ABILITIES} special abilities already selected for {self.selected_unit_type}.")
                        
                        SELECTED_ABILITIES[self.selected_unit_type] = sorted(current_selections) # Keep sorted
                        self._update_ability_buttons() # Redraw to reflect selection change
                        break 
                
                # Mouse wheel scrolling for ability list
                if event.type == pygame.MOUSEWHEEL:
                    if self.ability_display_rect.collidepoint(mouse_pos):
                        self.scroll_y_offset -= event.y * 20 # Adjust scroll speed
                        max_scroll = self.scroll_content_height - self.ability_display_rect.height
                        self.scroll_y_offset = max(0, min(self.scroll_y_offset, max_scroll if max_scroll > 0 else 0))
                        self._update_ability_buttons() # Re-position buttons based on new scroll

                # Scrollbar dragging
                if event.type == pygame.MOUSEBUTTONDOWN and event.button == 1:
                    if self.scrollbar_rect and self.scrollbar_rect.collidepoint(mouse_pos):
                        self.dragging_scrollbar = True
                        last_mouse_y = mouse_pos[1]
                if event.type == pygame.MOUSEBUTTONUP and event.button == 1:
                    self.dragging_scrollbar = False
                if event.type == pygame.MOUSEMOTION and self.dragging_scrollbar:
                    dy = mouse_pos[1] - last_mouse_y
                    last_mouse_y = mouse_pos[1]
                    
                    scroll_range = self.scroll_content_height - self.ability_display_rect.height
                    if scroll_range > 0 and self.scrollbar_rect:
                         # Calculate how much one pixel of mouse movement should translate to scroll_y_offset
                        pixels_per_scroll_offset = (self.ability_display_rect.height - self.scrollbar_rect.height) / scroll_range
                        self.scroll_y_offset += dy / pixels_per_scroll_offset if pixels_per_scroll_offset > 0 else 0

                        self.scroll_y_offset = max(0, min(self.scroll_y_offset, scroll_range))
                        self._update_ability_buttons()


            self.draw_background()
            
            # Draw title
            title_surface = self.title_font.render("SELECT ABILITIES", True, LIGHT_GRAY)
            title_rect = title_surface.get_rect(center=(current_screen_width//2, 50))
            self.screen.blit(title_surface, title_rect)

            # Draw unit selection buttons
            for unit_name, button in self.unit_buttons.items():
                button.draw(self.screen, self.small_font) # Use small font for these tabs

            # Draw "Selected Unit: Hunter" type of text
            unit_display_text = f"Configure: {self.selected_unit_type}"
            unit_text_surf = self.unit_name_font.render(unit_display_text, True, LIGHT_GRAY)
            unit_text_rect = unit_text_surf.get_rect(midtop=(self.ability_display_rect.centerx, self.ability_display_rect.top - 40))
            self.screen.blit(unit_text_surf, unit_text_rect)

            # Draw ability list panel (background)
            gfxdraw.box(self.screen, self.ability_display_rect, DESCRIPTION_BG_COLOR) # Slight transparency or solid
            pygame.draw.rect(self.screen, LIGHT_GRAY, self.ability_display_rect, 2, border_radius=4)


            # Draw ability buttons (clipped to their panel)
            # Create a temporary surface for clipping ability buttons
            ability_panel_surface = self.screen.subsurface(self.ability_display_rect).copy()
            ability_panel_surface.fill(DESCRIPTION_BG_COLOR) # Match panel background

            for ability_name, btn in self.ability_buttons.items():
                # Original button rects are relative to screen, adjust for panel surface
                # However, _update_ability_buttons positions them already considering scroll offset
                # We just need to ensure they are drawn only if visible within the panel.
                # This is complex. A simpler way is to adjust btn.rect.y for the panel's local coords.
                
                # Create a *new* rect for drawing on the panel_surface
                # The btn.rect is in screen coordinates, including scroll.
                # We need to translate screen-coord btn.rect into ability_panel_surface local coord.
                local_btn_rect_y = btn.rect.top - self.ability_display_rect.top
                
                # Only draw if visible within the panel's current scrolled view
                if btn.rect.bottom > self.ability_display_rect.top and btn.rect.top < self.ability_display_rect.bottom:
                    # Create a temporary button for drawing on the sub-surface, as its rect needs to be relative
                    temp_draw_button = Button(btn.rect.left - self.ability_display_rect.left, 
                                              local_btn_rect_y, 
                                              btn.rect.width, 
                                              btn.rect.height, 
                                              btn.text)
                    temp_draw_button.is_hovered = btn.is_hovered # Preserve hover state
                    
                    # Use a different color for selected abilities within the list
                    original_button_color = getattr(temp_draw_button, 'original_button_color', BUTTON_COLOR) # Store original
                    if hasattr(btn, 'is_selected') and btn.is_selected:
                         # This requires Button class to allow color override or use a different draw method.
                         # For now, this complex coloring won't work directly with Button.draw()
                         # Hack: temporarily change global BUTTON_COLOR (bad idea) or modify Button.draw
                         # A better way: Button.draw takes an optional color override.
                         # Or Button has a selected_color attribute.
                         # For this quick refactor: we'll just use the default Button.draw appearance.
                         # To show selection, we'll rely on the "Selected Special Abilities" panel.
                        pass # TODO: Visual distinction for selected abilities in the list

                    # Handle hover for buttons on the subsurface
                    # Mouse pos needs to be relative to the subsurface
                    local_mouse_pos = (mouse_pos[0] - self.ability_display_rect.left, mouse_pos[1] - self.ability_display_rect.top)
                    if temp_draw_button.rect.collidepoint(local_mouse_pos):
                         temp_draw_button.is_hovered = True
                    else:
                         temp_draw_button.is_hovered = False
                    
                    temp_draw_button.draw(ability_panel_surface, self.ability_font)
            
            self.screen.blit(ability_panel_surface, self.ability_display_rect.topleft)
            self._draw_scrollbar()


            # --- Draw Selected Special Abilities Panel ---
            gfxdraw.box(self.screen, self.selected_display_rect, DESCRIPTION_BG_COLOR)
            pygame.draw.rect(self.screen, LIGHT_GRAY, self.selected_display_rect, 2, border_radius=4)
            
            selected_title_surf = self.small_font.render(f"Selected ({self.selected_unit_type}):", True, LIGHT_GRAY)
            self.screen.blit(selected_title_surf, (self.selected_display_rect.left + 10, self.selected_display_rect.top + 10))

            current_selected_y = self.selected_display_rect.top + 40
            unit_for_abilities = self._create_unit(self.selected_unit_type)
            if unit_for_abilities and self.selected_unit_type in SELECTED_ABILITIES:
                # Get the actual ability objects for the selected indices
                special_abilities_for_unit = unit_for_abilities.abilities[2:]
                for selected_idx in SELECTED_ABILITIES[self.selected_unit_type]:
                    if 0 <= selected_idx < len(special_abilities_for_unit):
                        ability_obj = special_abilities_for_unit[selected_idx]
                        selected_text = f"- {ability_obj.name}"
                        sel_surf = self.small_font.render(selected_text, True, LIGHT_GRAY)
                        self.screen.blit(sel_surf, (self.selected_display_rect.left + 15, current_selected_y))
                        current_selected_y += 25


            # --- Draw Ability Description Panel ---
            desc_panel_height = 100
            desc_panel_rect = pygame.Rect(
                self.ability_display_rect.left, 
                self.ability_display_rect.bottom + 10, 
                self.ability_display_rect.width + self.selected_display_rect.width + 30, # Span both columns
                desc_panel_height
            )
            gfxdraw.box(self.screen, desc_panel_rect, DESCRIPTION_BG_COLOR)
            pygame.draw.rect(self.screen, LIGHT_GRAY, desc_panel_rect, 2, border_radius=4)

            if self.selected_ability_preview:
                ab = self.selected_ability_preview
                name_surf = self.ability_font.render(ab.name, True, LIGHT_GRAY)
                self.screen.blit(name_surf, (desc_panel_rect.left + 10, desc_panel_rect.top + 10))
                
                cost_surf = self.small_font.render(f"AP: {ab.ap_cost}", True, LIGHT_GRAY)
                self.screen.blit(cost_surf, (desc_panel_rect.left + 10, desc_panel_rect.top + 40))
                
                # AttributeError: 'Ability' object has no attribute 'range'
                # range_surf = self.small_font.render(f"Range: {ab.range}", True, LIGHT_GRAY)
                # self.screen.blit(range_surf, (desc_panel_rect.left + 120, desc_panel_rect.top + 40))

                desc_text = ab.description if hasattr(ab, 'description') else "No description."
                desc_surf = self.small_font.render(desc_text, True, LIGHT_GRAY)
                self.screen.blit(desc_surf, (desc_panel_rect.left + 10, desc_panel_rect.top + 65))


            # Draw Save/Back buttons
            self.save_button.draw(self.screen, self.small_font)
            self.back_button.draw(self.screen, self.small_font)

            # Draw save message if active
            if self.save_message_timer > 0:
                message_color = (0, 255, 0) if "saved permanently" in self.save_message else (255, 100, 100)
                message_surf = self.ability_font.render(self.save_message, True, message_color)
                message_rect = message_surf.get_rect(center=(current_screen_width//2, current_screen_height - 50))
                self.screen.blit(message_surf, message_rect)
                self.save_message_timer -= 1

            pygame.display.flip()
            self.clock.tick(FPS)

# Example Usage (if run directly for testing)
if __name__ == '__main__':
    pygame.init()
    screen_width = WINDOW_WIDTH
    screen_height = WINDOW_HEIGHT
    screen = pygame.display.set_mode((screen_width, screen_height))
    pygame.display.set_caption("Ability Selection Test")
    clock = pygame.time.Clock()

    # Initialize SELECTED_ABILITIES for testing if not already done by game_settings import
    if not SELECTED_ABILITIES: # If game_settings.py was empty or SELECTED_ABILITIES was not populated
         for unit_name_key in UNIT_CLASSES.keys():
            if unit_name_key not in SELECTED_ABILITIES:
                SELECTED_ABILITIES[unit_name_key] = []


    menu = AbilitySelectionMenu(screen, clock)
    menu.run()
    pygame.quit()
    sys.exit() 
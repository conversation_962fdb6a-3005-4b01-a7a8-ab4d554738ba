"""
Passive Ability System for the Tactical PvP Strategy Game

This module provides a comprehensive passive ability system that allows units
to have always-active effects that modify their behavior, stats, or interactions.

Key Components:
    - PassiveType: Enumeration of all available passive types
    - PassiveAbility: Data class defining passive properties
    - PassiveEffect: Base class for implementing passive behaviors
    - PassiveManager: Manages all passives for a unit

Features:
    - 15+ different passive types (defensive, offensive, utility)
    - Class-specific and universal passives
    - Configurable intensity levels
    - Automatic integration with unit systems
    - Save/load support for player selections

Example Usage:
    # Add knockback immunity to a warrior
    warrior = Warrior(1)
    immunity = PASSIVE_ABILITIES[PassiveType.KNOCKBACK_IMMUNITY]
    warrior.passive_manager.add_passive(immunity)

    # Check if unit has a specific passive
    if warrior.passive_manager.has_passive(PassiveType.KNOCKBACK_IMMUNITY):
        print("Warrior is immune to knockback!")
"""

from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod

class PassiveType(Enum):
    """Enumeration of all possible passive abilities"""
    # Defensive Passives
    KNOCKBACK_IMMUNITY = "knockback_immunity"
    DAMAGE_REDUCTION = "damage_reduction"
    STATUS_IMMUNITY = "status_immunity"
    REGENERATION = "regeneration"
    ARMOR_PLATING = "armor_plating"
    
    # Offensive Passives
    EXTRA_DAMAGE = "extra_damage"
    CRITICAL_STRIKES = "critical_strikes"
    PIERCING_ATTACKS = "piercing_attacks"
    LIFESTEAL = "lifesteal"
    BERSERKER_RAGE = "berserker_rage"
    
    # Mobility Passives
    EXTRA_MOVEMENT = "extra_movement"
    PHASE_WALK = "phase_walk"
    SWIFT_STRIKES = "swift_strikes"
    EVASION = "evasion"
    
    # Utility Passives
    EXTRA_AP = "extra_ap"
    ABILITY_COST_REDUCTION = "ability_cost_reduction"
    COOLDOWN_REDUCTION = "cooldown_reduction"
    VISION_ENHANCEMENT = "vision_enhancement"
    LEADERSHIP = "leadership"

@dataclass
class PassiveAbility:
    """Represents a passive ability"""
    passive_type: PassiveType
    name: str
    description: str
    intensity: int = 1  # Strength of the effect
    class_restriction: Optional[str] = None  # Which class can use this passive
    
    def __str__(self):
        return f"{self.name}: {self.description}"

class PassiveEffect(ABC):
    """Base class for passive effects"""
    
    def __init__(self, passive_ability: PassiveAbility, owner):
        self.passive_ability = passive_ability
        self.owner = owner
    
    @abstractmethod
    def apply_effect(self, context: Dict[str, Any]) -> Any:
        """Apply the passive effect in the given context"""
        pass

class KnockbackImmunityEffect(PassiveEffect):
    """Prevents knockback effects"""
    
    def apply_effect(self, context: Dict[str, Any]) -> Any:
        if context.get("effect_type") == "knockback":
            print(f"{self.owner.name} is immune to knockback!")
            return False  # Prevent knockback
        return context.get("default_result", True)

class DamageReductionEffect(PassiveEffect):
    """Reduces incoming damage"""
    
    def apply_effect(self, context: Dict[str, Any]) -> Any:
        if context.get("action") == "take_damage":
            damage = context.get("damage", 0)
            reduction = self.passive_ability.intensity
            reduced_damage = max(0, damage - reduction)
            print(f"{self.owner.name}'s armor reduces damage from {damage} to {reduced_damage}")
            return reduced_damage
        return context.get("damage", 0)

class ExtraDamageEffect(PassiveEffect):
    """Increases outgoing damage"""
    
    def apply_effect(self, context: Dict[str, Any]) -> Any:
        if context.get("action") == "deal_damage":
            damage = context.get("damage", 0)
            bonus = self.passive_ability.intensity
            enhanced_damage = damage + bonus
            print(f"{self.owner.name} deals +{bonus} bonus damage!")
            return enhanced_damage
        return context.get("damage", 0)

class ExtraMovementEffect(PassiveEffect):
    """Increases movement range"""
    
    def apply_effect(self, context: Dict[str, Any]) -> Any:
        if context.get("action") == "get_movement_range":
            base_range = context.get("base_range", 1)
            bonus = self.passive_ability.intensity
            return base_range + bonus
        return context.get("base_range", 1)

class ExtraAPEffect(PassiveEffect):
    """Increases maximum AP"""
    
    def apply_effect(self, context: Dict[str, Any]) -> Any:
        if context.get("action") == "get_max_ap":
            base_ap = context.get("base_ap", 6)
            bonus = self.passive_ability.intensity
            return base_ap + bonus
        return context.get("base_ap", 6)

class RegenerationEffect(PassiveEffect):
    """Heals at start of turn"""
    
    def apply_effect(self, context: Dict[str, Any]) -> Any:
        if context.get("action") == "turn_start":
            heal_amount = self.passive_ability.intensity
            if self.owner.health < self.owner.max_health:
                old_health = self.owner.health
                self.owner.health = min(self.owner.max_health, self.owner.health + heal_amount)
                actual_heal = self.owner.health - old_health
                if actual_heal > 0:
                    print(f"{self.owner.name} regenerates {actual_heal} health")
        return True

class PassiveManager:
    """Manages all passive abilities for a unit"""
    
    def __init__(self, unit):
        self.unit = unit
        self.passive_abilities: List[PassiveAbility] = []
        self.passive_effects: List[PassiveEffect] = []
    
    def add_passive(self, passive_ability: PassiveAbility):
        """Add a passive ability to the unit"""
        # Check if passive type already exists (better duplicate detection)
        if not self.has_passive(passive_ability.passive_type):
            self.passive_abilities.append(passive_ability)
            effect = self._create_effect(passive_ability)
            if effect:
                self.passive_effects.append(effect)
                print(f"{self.unit.name} gains passive: {passive_ability.name}")
    
    def remove_passive(self, passive_type: PassiveType):
        """Remove a passive ability"""
        self.passive_abilities = [p for p in self.passive_abilities if p.passive_type != passive_type]
        self.passive_effects = [e for e in self.passive_effects if e.passive_ability.passive_type != passive_type]
    
    def has_passive(self, passive_type: PassiveType) -> bool:
        """Check if unit has a specific passive"""
        return any(p.passive_type == passive_type for p in self.passive_abilities)
    
    def apply_passives(self, context: Dict[str, Any]) -> Any:
        """Apply all relevant passive effects for the given context"""
        result = context.get("default_result")
        
        for effect in self.passive_effects:
            result = effect.apply_effect(context)
            # Update context with new result for chaining effects
            context["default_result"] = result
        
        return result
    
    def _create_effect(self, passive_ability: PassiveAbility) -> Optional[PassiveEffect]:
        """Create the appropriate effect for a passive ability"""
        effect_map = {
            PassiveType.KNOCKBACK_IMMUNITY: KnockbackImmunityEffect,
            PassiveType.DAMAGE_REDUCTION: DamageReductionEffect,
            PassiveType.EXTRA_DAMAGE: ExtraDamageEffect,
            PassiveType.EXTRA_MOVEMENT: ExtraMovementEffect,
            PassiveType.EXTRA_AP: ExtraAPEffect,
            PassiveType.REGENERATION: RegenerationEffect,
        }
        
        effect_class = effect_map.get(passive_ability.passive_type)
        if effect_class:
            return effect_class(passive_ability, self.unit)
        return None
    
    def get_passive_descriptions(self) -> List[str]:
        """Get descriptions of all active passives"""
        return [str(passive) for passive in self.passive_abilities]

# Predefined passive abilities
PASSIVE_ABILITIES = {
    # Barbarian/Warrior Passives
    PassiveType.KNOCKBACK_IMMUNITY: PassiveAbility(
        PassiveType.KNOCKBACK_IMMUNITY,
        "Immovable",
        "Immune to knockback effects",
        class_restriction="Warrior"
    ),
    
    PassiveType.DAMAGE_REDUCTION: PassiveAbility(
        PassiveType.DAMAGE_REDUCTION,
        "Thick Skin",
        "Reduces incoming damage by 1",
        intensity=1
    ),
    
    PassiveType.REGENERATION: PassiveAbility(
        PassiveType.REGENERATION,
        "Regeneration",
        "Heals 1 HP at the start of each turn",
        intensity=1
    ),
    
    # Hunter Passives
    PassiveType.EXTRA_DAMAGE: PassiveAbility(
        PassiveType.EXTRA_DAMAGE,
        "Sharpshooter",
        "Deals +1 damage with all attacks",
        intensity=1,
        class_restriction="Hunter"
    ),
    
    # Rogue Passives
    PassiveType.EXTRA_MOVEMENT: PassiveAbility(
        PassiveType.EXTRA_MOVEMENT,
        "Fleet Footed",
        "Increases movement range by 1",
        intensity=1,
        class_restriction="Rogue"
    ),
    
    # Mage Passives
    PassiveType.EXTRA_AP: PassiveAbility(
        PassiveType.EXTRA_AP,
        "Arcane Mastery",
        "Increases maximum AP by 1",
        intensity=1,
        class_restriction="Mage"
    ),
    
    # Universal Passives
    PassiveType.ABILITY_COST_REDUCTION: PassiveAbility(
        PassiveType.ABILITY_COST_REDUCTION,
        "Efficiency",
        "Reduces all ability costs by 1 (minimum 1)",
        intensity=1
    ),
}

def get_available_passives_for_class(class_name: str) -> List[PassiveAbility]:
    """Get all passive abilities available for a specific class"""
    available = []
    for passive in PASSIVE_ABILITIES.values():
        if passive.class_restriction is None or passive.class_restriction == class_name:
            available.append(passive)
    return available

#!/usr/bin/env python3
"""
Test script for passive system integration with unit creation
"""

import pygame
from game_state import Game
from units.warrior import Warrior
from units.hunter import Hunter
from units.rogue import Rogue
from units.mage import Mage
from passive_system import PassiveType
from game_settings import SELECTED_PASSIVES

def test_passive_integration():
    """Test that units get their selected passives when created"""
    pygame.init()
    
    print("🔗 PASSIVE INTEGRATION TEST 🔗")
    print("=" * 40)
    
    # Test 1: Check default passive selections
    print("\n📋 TEST 1: Default Passive Selections")
    print("-" * 35)
    
    print("Default passive selections:")
    for class_name, passives in SELECTED_PASSIVES.items():
        print(f"  {class_name}: {passives}")
    
    # Test 2: Create units and check if they get their passives
    print("\n\n📋 TEST 2: Unit Creation with Passives")
    print("-" * 35)
    
    game = Game()
    
    # Create units like the game setup does
    warrior = Warrior(1)
    hunter = Hunter(1)
    rogue = Rogue(1)
    mage = Mage(1)
    
    # Apply the filtering (which includes passive application)
    units = [
        ("Warrior", warrior),
        ("Hunter", hunter),
        ("Rogue", rogue),
        ("Mage", mage)
    ]
    
    for unit_name, unit in units:
        print(f"\n{unit_name} before filtering:")
        print(f"  Passives: {len(unit.passive_manager.passive_abilities)}")
        
        # Apply the same filtering that happens in game setup
        game._filter_unit_abilities(unit)
        
        print(f"{unit_name} after filtering:")
        print(f"  Passives: {len(unit.passive_manager.passive_abilities)}")
        for passive in unit.passive_manager.passive_abilities:
            print(f"    • {passive.name}: {passive.description}")
    
    # Test 3: Test Warrior knockback immunity in practice
    print("\n\n📋 TEST 3: Warrior Knockback Immunity in Practice")
    print("-" * 45)
    
    # Set up a scenario
    warrior.position = (4, 4)
    hunter.position = (3, 3)
    warrior.board = game.board
    hunter.board = game.board
    game.board.units = {(4, 4): warrior, (3, 3): hunter}
    
    print(f"Warrior at {warrior.position}")
    print(f"Hunter at {hunter.position}")
    print(f"Warrior has knockback immunity: {warrior.passive_manager.has_passive(PassiveType.KNOCKBACK_IMMUNITY)}")
    
    # Test knockback
    original_pos = warrior.position
    print(f"\n🎯 Hunter uses Knockback Shot on Warrior...")
    result = hunter.use_ability(4, warrior.position, game)
    
    print(f"Knockback result: {result}")
    print(f"Warrior position after: {warrior.position}")
    print(f"✅ Immunity works: {warrior.position == original_pos}")
    
    # Test 4: Test Hunter extra damage
    print("\n\n📋 TEST 4: Hunter Extra Damage")
    print("-" * 25)
    
    # Create a target
    target_warrior = Warrior(2)
    target_warrior.position = (5, 5)
    target_warrior.board = game.board
    game.board.units[(5, 5)] = target_warrior
    
    print(f"Target Warrior health before: {target_warrior.health}")
    print(f"Hunter has extra damage: {hunter.passive_manager.has_passive(PassiveType.EXTRA_DAMAGE)}")
    
    # Hunter basic attack (should do 1 base + 1 extra = 2 damage)
    print(f"\n🎯 Hunter attacks target...")
    hunter.attack(target_warrior.position, game)
    
    print(f"Target Warrior health after: {target_warrior.health}")
    expected_health = 7 - 2  # 7 base - 2 damage (1 base + 1 extra)
    print(f"✅ Extra damage works: {target_warrior.health <= expected_health}")
    
    # Test 5: Test Mage extra AP
    print("\n\n📋 TEST 5: Mage Extra AP")
    print("-" * 20)
    
    original_max_ap = 9  # Mage base AP from config
    print(f"Mage base max AP: {original_max_ap}")
    print(f"Mage has extra AP: {mage.passive_manager.has_passive(PassiveType.EXTRA_AP)}")
    
    # Reset AP to trigger passive
    mage.reset_ap(game)
    
    print(f"Mage max AP after reset: {mage.max_ap}")
    print(f"Mage current AP: {mage.current_ap}")
    print(f"✅ Extra AP works: {mage.max_ap > original_max_ap}")
    
    # Test 6: Test Rogue extra movement
    print("\n\n📋 TEST 6: Rogue Extra Movement")
    print("-" * 25)
    
    rogue.position = (6, 6)
    rogue.board = game.board
    game.board.units = {(6, 6): rogue}  # Clear board except rogue
    
    print(f"Rogue has extra movement: {rogue.passive_manager.has_passive(PassiveType.EXTRA_MOVEMENT)}")
    
    # Get movement range
    moves = rogue.get_valid_moves(game.board)
    if moves:
        max_distance = max([max(abs(move[0] - 6), abs(move[1] - 6)) for move in moves])
        print(f"Rogue max movement distance: {max_distance}")
        print(f"Total moves available: {len(moves)}")
        print(f"✅ Extra movement works: {max_distance > 3}")  # Base knight range + 1
    
    # Test 7: Test passive persistence
    print("\n\n📋 TEST 7: Passive Persistence")
    print("-" * 25)
    
    # Create a new warrior and check if it gets the same passives
    warrior2 = Warrior(1)
    game._filter_unit_abilities(warrior2)
    
    print(f"New Warrior passives:")
    for passive in warrior2.passive_manager.passive_abilities:
        print(f"  • {passive.name}")
    
    print(f"✅ Passives applied consistently: {len(warrior2.passive_manager.passive_abilities) > 0}")
    
    print("\n" + "=" * 40)
    print("🎉 PASSIVE INTEGRATION TESTS COMPLETED!")
    print("\n✅ Integration Verified:")
    print("  • Units automatically get selected passives")
    print("  • Knockback immunity prevents knockback")
    print("  • Extra damage increases attack damage")
    print("  • Extra AP increases maximum AP")
    print("  • Extra movement increases range")
    print("  • Passives persist across unit creation")
    print("  • Game setup integration works")
    
    print("\n🛡️ Passive system fully integrated!")

if __name__ == "__main__":
    test_passive_integration()

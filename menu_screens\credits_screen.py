import pygame
import sys
from menu_screens.button import Button # Import Button

# Constants (ideally from a shared source)
WINDOW_WIDTH = 1280
WINDOW_HEIGHT = 720
FPS = 60
DARK_GRAY = (30, 30, 30)
LIGHT_GRAY = (200, 200, 200)

CREDIT_LINES = [
    "Game Design & Concept",
    "- Your Name Here",
    "",
    "Programming",
    "- Your Name Here",
    "- AI Co-pilot (Gemini)",
    "",
    "Artwork & Assets",
    "- Asset Store A (e.g. Kenney.nl)",
    "- Artist B",
    "",
    "Special Thanks",
    "- Pygame Community",
    "- Coffee",
    "",
    "Thanks for playing!"
]

class CreditsScreen:
    def __init__(self, screen, clock):
        self.screen = screen
        self.clock = clock
        self.running = True
        
        self.title_font = pygame.font.Font(None, 74)
        self.credit_font = pygame.font.Font(None, 36)
        self.button_font = pygame.font.Font(None, 48)

        self.scroll_y = 0
        self.scroll_speed = 1 # Pixels per frame
        self.total_credits_height = len(CREDIT_LINES) * 40 # Estimate

        current_screen_width, current_screen_height = self.screen.get_size()
        self.back_button = But<PERSON>(current_screen_width // 2 - 100, current_screen_height - 80, 200, 60, "Back")

    def draw_background(self):
        current_screen_width, current_screen_height = self.screen.get_size()
        for y in range(current_screen_height):
            color_value = int(30 + (y / current_screen_height) * 20)
            pygame.draw.line(self.screen, (color_value, color_value, color_value),
                           (0, y), (current_screen_width, y))

    def run(self):
        self.running = True
        self.scroll_y = self.screen.get_height() # Start credits off-screen at the bottom

        while self.running:
            current_screen_width, current_screen_height = self.screen.get_size()
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()
                
                if self.back_button.handle_event(event):
                    self.running = False
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        self.running = False
            
            # Scroll credits
            self.scroll_y -= self.scroll_speed
            # Reset scroll if it goes too far past the top
            if self.scroll_y < -self.total_credits_height - 100:
                self.scroll_y = current_screen_height

            self.draw_background()
            
            title_surface = self.title_font.render("CREDITS", True, LIGHT_GRAY)
            title_rect = title_surface.get_rect(center=(current_screen_width // 2, 80))
            self.screen.blit(title_surface, title_rect)

            # Draw credit lines
            current_y_render = self.scroll_y
            for line in CREDIT_LINES:
                if not line.strip(): # Empty line for spacing
                    current_y_render += 20 # Half a line space
                    continue
                
                credit_surface = self.credit_font.render(line, True, LIGHT_GRAY)
                credit_rect = credit_surface.get_rect(center=(current_screen_width // 2, current_y_render))
                
                # Only blit if visible
                if credit_rect.bottom > 0 and credit_rect.top < current_screen_height:
                    self.screen.blit(credit_surface, credit_rect)
                current_y_render += 40 # Line height
            
            self.back_button.draw(self.screen, self.button_font)
            
            pygame.display.flip()
            self.clock.tick(FPS) 
#!/usr/bin/env python3
"""
Visual demonstration of the Cleric + pattern system
Shows how all abilities use the same 1-tile orthogonal pattern
"""

def show_cleric_plus_pattern_demo():
    print("⛪ CLERIC + PATTERN SYSTEM DEMO ⛪")
    print("=" * 38)
    
    print("\n📋 How Cleric + Pattern Works:")
    print("1. ALL abilities affect 1 tile in orthogonal directions only")
    print("2. Creates a + shape pattern around the Cleric")
    print("3. No diagonal targeting - pure orthogonal")
    print("4. Consistent across movement and all abilities")
    
    print("\n🎯 Basic + Pattern:")
    print("┌─┬─┬─┬─┬─┐")
    print("│ │ │ │ │ │")
    print("├─┼─┼─┼─┼─┤")
    print("│ │ │N│ │ │  N = North (1 tile)")
    print("├─┼─┼─┼─┼─┤")
    print("│ │W│C│E│ │  C = Cleric, W = West, E = East")
    print("├─┼─┼─┼─┼─┤")
    print("│ │ │S│ │ │  S = South (1 tile)")
    print("├─┼─┼─┼─┼─┤")
    print("│ │ │ │ │ │")
    print("└─┴─┴─┴─┴─┘")
    
    print("\n🌟 All Abilities Use + Pattern:")
    
    print("\n⛪ MASS HEAL:")
    print("   Heals ALL allies in + pattern")
    print("   ┌─┬─┬─┬─┬─┐")
    print("   │ │ │ │ │ │")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │A│ │ │  A = Ally (healed)")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │A│C│A│ │  C = Cleric")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │A│ │ │  All 4 allies healed simultaneously")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │ │ │ │")
    print("   └─┴─┴─┴─┴─┘")
    
    print("\n🩹 HEAL (Single Target):")
    print("   Targets ONE ally in + pattern")
    print("   ┌─┬─┬─┬─┬─┐")
    print("   │ │ │ │ │ │")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │T│ │ │  T = Target ally")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │C│ │ │  C = Cleric")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │ │ │ │  Click on ally to heal")
    print("   └─┴─┴─┴─┴─┘")
    
    print("\n✨ CLEANSE:")
    print("   Removes debuffs from ally in + pattern")
    print("   ┌─┬─┬─┬─┬─┐")
    print("   │ │ │ │ │ │")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │ │ │ │")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │D│C│ │ │  D = Debuffed ally")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │ │ │ │  C = Cleric")
    print("   └─┴─┴─┴─┴─┘")
    print("   Click on debuffed ally to cleanse")
    
    print("\n🛡️ SANCTUARY:")
    print("   Protects ally in + pattern")
    print("   ┌─┬─┬─┬─┬─┐")
    print("   │ │ │ │ │ │")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │ │ │ │")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │C│S│ │  S = Ally to protect")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │ │ │ │  C = Cleric")
    print("   └─┴─┴─┴─┴─┘")
    
    print("\n⚡ HOLY SMITE:")
    print("   Attacks enemy in + pattern")
    print("   ┌─┬─┬─┬─┬─┐")
    print("   │ │ │ │ │ │")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │E│ │ │  E = Enemy")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │C│ │ │  C = Cleric")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │ │ │ │")
    print("   └─┴─┴─┴─┴─┘")
    
    print("\n🚶 MOVEMENT:")
    print("   Moves 1 tile in + pattern")
    print("   ┌─┬─┬─┬─┬─┐")
    print("   │ │ │ │ │ │")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │M│ │ │  M = Movement option")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │M│C│M│ │  C = Cleric")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │M│ │ │  4 movement options")
    print("   └─┴─┴─┴─┴─┘")
    
    print("\n🎮 Tactical Applications:")
    
    print("\n⚔️ FORMATION SUPPORT:")
    print("   Example: Healing formation")
    print("   ┌─┬─┬─┬─┬─┐")
    print("   │W│W│W│W│W│  W = Warriors in formation")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │W│W│C│W│W│  C = Cleric in center")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │W│W│W│W│W│  Mass Heal affects 4 warriors")
    print("   └─┴─┴─┴─┴─┘")
    
    print("\n🛡️ DEFENSIVE POSITIONING:")
    print("   Example: Sanctuary placement")
    print("   ┌─┬─┬─┬─┬─┐")
    print("   │ │E│E│E│ │  E = Enemies")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │E│A│C│A│E│  A = Allies, C = Cleric")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │E│A│E│ │  Sanctuary protects adjacent allies")
    print("   └─┴─┴─┴─┴─┘")
    
    print("\n⚡ COMBAT SUPPORT:")
    print("   Example: Cleanse + Heal combo")
    print("   ┌─┬─┬─┬─┬─┐")
    print("   │ │ │D│ │ │  D = Debuffed ally")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │W│C│W│ │  W = Wounded allies")
    print("   ├─┼─┼─┼─┼─┤")
    print("   │ │ │W│ │ │  C = Cleric")
    print("   └─┴─┴─┴─┴─┘")
    print("   Turn 1: Cleanse debuffed ally")
    print("   Turn 2: Mass Heal all wounded allies")
    
    print("\n🎯 Strategic Advantages:")
    
    print("\n⚡ ADVANTAGES:")
    print("   ✅ Consistent pattern across all abilities")
    print("   ✅ Predictable targeting and effects")
    print("   ✅ Simple positioning decisions")
    print("   ✅ Efficient area support with Mass Heal")
    print("   ✅ Precise single-target abilities")
    print("   ✅ No diagonal complexity")
    
    print("\n⚠️ TACTICAL CONSIDERATIONS:")
    print("   ❌ Limited to 4 adjacent positions")
    print("   ❌ Requires careful positioning")
    print("   ❌ No long-range support")
    print("   ❌ Vulnerable to area attacks")
    print("   ✅ But: Consistent and reliable")
    
    print("\n🎯 Comparison with Other Units:")
    
    print("\n📊 Range Patterns:")
    print("   Cleric:  + pattern (1 tile orthogonal)")
    print("   Mage:    2 tiles orthogonal (blink)")
    print("   Warrior: 2 tiles orthogonal (line)")
    print("   Rogue:   L-shape (knight pattern)")
    print("   Hunter:  Diagonal variable")
    
    print("\n🎮 Perfect Cleric Identity:")
    
    print("\n⛪ ROLE DEFINITION:")
    print("   - Close-range support specialist")
    print("   - Formation-based healing")
    print("   - Adjacent ally protection")
    print("   - Consistent + pattern abilities")
    
    print("\n🎯 GAMEPLAY FLOW:")
    print("   1. Position near allies (+ pattern)")
    print("   2. Use Mass Heal for group healing")
    print("   3. Use single abilities for specific needs")
    print("   4. Move 1 tile to optimize positioning")
    print("   5. Repeat with consistent pattern")
    
    print("\n" + "=" * 38)
    print("🎯 CLERIC + PATTERN SUMMARY")
    print("-" * 28)
    print("✅ Movement: 1 tile orthogonal")
    print("✅ Mass Heal: Heals all in + pattern")
    print("✅ Single Targets: + pattern only")
    print("✅ Consistency: All abilities same pattern")
    print("✅ Simplicity: Easy to understand")
    print("✅ Tactical: Formation-based support")
    
    print("\n⛪ Cleric is now the perfect close-range")
    print("   support unit with consistent + pattern!")

if __name__ == "__main__":
    show_cleric_plus_pattern_demo()

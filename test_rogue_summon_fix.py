#!/usr/bin/env python3

import pygame
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state import Game
from units.rogue import Rogue

def test_rogue_summon():
    """Test if Rogue summon ability works"""
    print("🗡️ TESTING ROGUE SUMMON FIX")
    print("=" * 40)
    
    # Initialize pygame (required for unit creation)
    pygame.init()
    
    # Create a game instance
    game = Game()
    
    # Create a Rogue
    rogue = Rogue(player_id=1)
    game.board.add_unit(rogue, 4, 4)
    
    # Set up game state
    game.current_player = 1
    game.current_player_ap = 10  # Plenty of AP
    game.units_acted_this_turn = set()
    
    print(f"Rogue at {rogue.position}")
    print(f"Initial AP: {game.current_player_ap}")
    print(f"Rogue acted: {rogue in game.units_acted_this_turn}")
    
    # Find the summon ability
    summon_idx = None
    for i, ability in enumerate(rogue.abilities):
        if ability.name == "Summon":
            summon_idx = i
            break
    
    if summon_idx is None:
        print("❌ Rogue doesn't have Summon ability!")
        return False
    
    print(f"✓ Rogue has Summon ability (index {summon_idx})")
    print(f"  AP cost: {rogue.abilities[summon_idx].ap_cost}")
    
    # Get valid summon targets
    valid_targets = rogue.get_ability_targets(summon_idx, game.board)
    print(f"✓ Valid summon targets: {len(valid_targets)}")
    
    if not valid_targets:
        print("❌ No valid summon targets!")
        return False
    
    # Test summon
    target_pos = valid_targets[0]
    print(f"  Testing summon at {target_pos}")
    
    # Use the summon ability
    success = rogue.use_ability(summon_idx, target_pos, game)
    
    print(f"  Summon success: {success}")
    print(f"  AP after: {game.current_player_ap}")
    print(f"  Rogue acted: {rogue in game.units_acted_this_turn}")
    
    if success:
        # Check if pawn was created
        if target_pos in game.board.units:
            pawn = game.board.units[target_pos]
            print(f"  ✓ Pawn created at {target_pos}")
            print(f"  ✓ Pawn player: {pawn.player_id}")
            print(f"  ✓ Pawn health: {pawn.health}/{pawn.max_health}")
            return True
        else:
            print(f"  ❌ No pawn found at {target_pos}")
            return False
    else:
        print(f"  ❌ Summon failed")
        return False

if __name__ == "__main__":
    success = test_rogue_summon()
    if success:
        print("\n🎉 ROGUE SUMMON FIX SUCCESSFUL!")
    else:
        print("\n❌ ROGUE SUMMON STILL BROKEN!")

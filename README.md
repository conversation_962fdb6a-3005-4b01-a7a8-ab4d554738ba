# Tactical PvP Strategy Game

A turn-based tactical strategy game where two players compete on a 9x9 chess-like board with unique units.

## Game Overview

- **1v1 Gameplay**: Two players take turns moving their units on a 9x9 chess board.
- **Multiple Unit Types**: Choose from Warriors, Archers, Mages, and Knights, each with unique abilities.
- **Tactical Combat**: Strategic positioning and unit selection are key to victory.

## How to Play

1. **Start the Game**: Run `python main_menu.py` to launch the main menu.
2. **Setup Phase**: 
   - Each player takes turns placing their units on their side of the board.
   - Player 1 places units on the bottom 3 rows, Player 2 on the top 3 rows.
   - Select a unit type from the right panel, then click on the board to place it.

3. **Gameplay Phase**:
   - Players take turns moving and attacking with their units.
   - Click on your unit to select it, then click on a highlighted square to move or attack.
   - When finished with your turn, click the "End Turn" button.

4. **Victory Condition**: Eliminate all of your opponent's units to win.

## Unit Types

1. **Warrior**:
   - Melee unit with high health
   - Moves and attacks in adjacent squares (including diagonals)

2. **Archer**:
   - Ranged unit with medium health
   - Moves 1-2 squares horizontally or vertically
   - Attacks targets 2-4 squares away with line-of-sight

3. **Mage**:
   - Spell caster with low health
   - Moves 1 square in any direction
   - Can attack enemies up to 3 squares away

4. **Knight**:
   - Mobile unit with L-shaped movement pattern (like a chess knight)
   - Can jump over other units
   - Attacks in the same L-shaped pattern

## Controls

- **Left-click**: Select units, move, attack, and interact with buttons
- **Any key**: Return to main menu after a game ends

## Requirements

- Python 3.x
- Pygame 2.5.2 (`pip install -r requirements.txt`)

Enjoy the game!

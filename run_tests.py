#!/usr/bin/env python3
"""
Automated Test Runner for Tactical PvP Strategy Game

This script runs all tests and provides detailed reporting.
Use this for continuous integration and development testing.
"""

import sys
import os
import time
import subprocess
from pathlib import Path

def run_test_suite():
    """Run the comprehensive test suite"""
    print("🧪 RUNNING AUTOMATED TEST SUITE 🧪")
    print("=" * 50)
    
    start_time = time.time()
    
    try:
        # Run the test suite
        result = subprocess.run([
            sys.executable, "tests/test_suite.py"
        ], capture_output=True, text=True, timeout=60)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"⏱️  Test Duration: {duration:.2f} seconds")
        print("\n📊 TEST OUTPUT:")
        print("-" * 30)
        print(result.stdout)
        
        if result.stderr:
            print("\n🚨 ERRORS:")
            print("-" * 20)
            print(result.stderr)
        
        # Parse results
        if "OK" in result.stdout and result.returncode == 0:
            print("\n✅ ALL TESTS PASSED!")
            return True
        else:
            print(f"\n❌ TESTS FAILED (Exit code: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ Tests timed out after 60 seconds")
        return False
    except Exception as e:
        print(f"🚨 Error running tests: {e}")
        return False

def check_code_quality():
    """Basic code quality checks"""
    print("\n🔍 CODE QUALITY CHECKS")
    print("-" * 25)
    
    issues = []
    
    # Check for common issues in Python files
    python_files = list(Path(".").rglob("*.py"))
    
    for file_path in python_files:
        if "test_" in str(file_path) or "__pycache__" in str(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
                
                # Check for long lines (>120 characters)
                for i, line in enumerate(lines, 1):
                    if len(line) > 120:
                        issues.append(f"{file_path}:{i} - Line too long ({len(line)} chars)")
                
                # Check for TODO comments
                for i, line in enumerate(lines, 1):
                    if "TODO" in line.upper() or "FIXME" in line.upper():
                        issues.append(f"{file_path}:{i} - TODO/FIXME found: {line.strip()}")
                        
        except Exception as e:
            issues.append(f"{file_path} - Could not read file: {e}")
    
    if issues:
        print(f"⚠️  Found {len(issues)} code quality issues:")
        for issue in issues[:10]:  # Show first 10
            print(f"  • {issue}")
        if len(issues) > 10:
            print(f"  ... and {len(issues) - 10} more")
    else:
        print("✅ No code quality issues found")
    
    return len(issues)

def check_documentation():
    """Check documentation completeness"""
    print("\n📚 DOCUMENTATION CHECK")
    print("-" * 25)
    
    required_docs = [
        "README.md",
        "PROJECT_STATUS.md", 
        "DEVELOPMENT_GUIDE.md",
        "TODO_LIST.md"
    ]
    
    missing_docs = []
    for doc in required_docs:
        if not Path(doc).exists():
            missing_docs.append(doc)
    
    if missing_docs:
        print(f"❌ Missing documentation: {', '.join(missing_docs)}")
    else:
        print("✅ All required documentation present")
    
    return len(missing_docs)

def generate_report():
    """Generate a development status report"""
    print("\n📋 DEVELOPMENT STATUS REPORT")
    print("=" * 35)
    
    # Count files and lines
    python_files = list(Path(".").rglob("*.py"))
    python_files = [f for f in python_files if "__pycache__" not in str(f)]
    
    total_lines = 0
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                total_lines += len(f.readlines())
        except:
            pass
    
    print(f"📁 Python Files: {len(python_files)}")
    print(f"📝 Total Lines of Code: {total_lines:,}")
    
    # Check key components
    key_files = [
        "game.py", "game_state.py", "passive_system.py",
        "units/hunter.py", "tests/test_suite.py"
    ]
    
    print(f"\n🔧 Key Components:")
    for file_path in key_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path} (missing)")
    
    # Check unit classes
    unit_files = list(Path("units").glob("*.py"))
    unit_files = [f for f in unit_files if f.name != "__init__.py"]
    print(f"\n👥 Unit Classes: {len(unit_files)}")
    for unit_file in unit_files:
        print(f"  • {unit_file.stem.title()}")

def main():
    """Main test runner function"""
    print("🚀 AUTOMATED DEVELOPMENT CHECKS")
    print("=" * 40)
    print(f"📅 Run Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python Version: {sys.version.split()[0]}")
    print(f"📂 Working Directory: {os.getcwd()}")
    
    # Run all checks
    test_passed = run_test_suite()
    quality_issues = check_code_quality()
    missing_docs = check_documentation()
    
    generate_report()
    
    # Final summary
    print("\n" + "=" * 40)
    print("🎯 FINAL SUMMARY")
    print("-" * 20)
    
    if test_passed:
        print("✅ Tests: PASSED")
    else:
        print("❌ Tests: FAILED")
    
    if quality_issues == 0:
        print("✅ Code Quality: GOOD")
    else:
        print(f"⚠️  Code Quality: {quality_issues} issues")
    
    if missing_docs == 0:
        print("✅ Documentation: COMPLETE")
    else:
        print(f"❌ Documentation: {missing_docs} missing")
    
    # Overall status
    if test_passed and quality_issues < 5 and missing_docs == 0:
        print("\n🎉 PROJECT STATUS: EXCELLENT")
        return 0
    elif test_passed and quality_issues < 10:
        print("\n👍 PROJECT STATUS: GOOD")
        return 0
    else:
        print("\n⚠️  PROJECT STATUS: NEEDS ATTENTION")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)

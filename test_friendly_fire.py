#!/usr/bin/env python3
"""
Test script to verify friendly fire is working correctly
"""

import pygame
from game_state import Game
from units.hunter import Hunter
from units.rogue import Rogue
from units.warrior import Warrior

def test_friendly_fire():
    """Test that abilities now damage allies (friendly fire enabled)"""
    pygame.init()
    
    print("🔥 TESTING FRIENDLY FIRE (DEFAULT BEHAVIOR) 🔥")
    print("=" * 50)
    
    game = Game()
    
    # Create units - same player ID for friendly fire test
    hunter = Hunter(1)
    ally_warrior = Warrior(1)  # Same player - should take friendly fire
    enemy_warrior = Warrior(2)  # Different player - should take damage
    
    # Position units
    hunter.position = (4, 4)
    ally_warrior.position = (3, 3)  # Diagonal from hunter (basic attack range)
    enemy_warrior.position = (5, 5)  # Diagonal from hunter
    
    # Set up board
    hunter.board = game.board
    ally_warrior.board = game.board
    enemy_warrior.board = game.board
    
    game.board.units = {
        (4, 4): hunter,
        (3, 3): ally_warrior,
        (5, 5): enemy_warrior
    }
    
    print(f"Setup:")
    print(f"  Hunter (Player 1) at {hunter.position}")
    print(f"  Ally Warrior (Player 1) at {ally_warrior.position} - HP: {ally_warrior.health}")
    print(f"  Enemy Warrior (Player 2) at {enemy_warrior.position} - HP: {enemy_warrior.health}")
    
    # Test 1: Basic Attack with Friendly Fire
    print(f"\n📋 TEST 1: Basic Attack Friendly Fire")
    print("-" * 35)
    
    # Check targeting - should be able to target ally
    targets = hunter.get_ability_targets(1, game.board)  # Basic attack
    print(f"Basic attack targets: {targets}")
    
    if ally_warrior.position in targets:
        print(f"✅ Can target ally warrior (friendly fire enabled)")
    else:
        print(f"❌ Cannot target ally warrior (friendly fire disabled)")
    
    # Attack the ally
    original_ally_hp = ally_warrior.health
    result = hunter.use_ability(1, ally_warrior.position, game)
    print(f"Attack ally result: {result}")
    print(f"Ally HP: {original_ally_hp} → {ally_warrior.health}")
    
    if ally_warrior.health < original_ally_hp:
        print(f"✅ FRIENDLY FIRE WORKING - Ally took damage")
    else:
        print(f"❌ FRIENDLY FIRE NOT WORKING - Ally unharmed")
    
    # Test 2: Knockback Shot with Friendly Fire
    print(f"\n📋 TEST 2: Knockback Shot Friendly Fire")
    print("-" * 38)
    
    # Reset positions
    ally_warrior.position = (2, 2)
    ally_warrior.health = Warrior(1).health  # Reset health
    game.board.units = {
        (4, 4): hunter,
        (2, 2): ally_warrior,
        (5, 5): enemy_warrior
    }
    
    original_ally_hp = ally_warrior.health
    original_ally_pos = ally_warrior.position
    
    # Use knockback shot on ally
    result = hunter.use_ability(4, ally_warrior.position, game)  # Knockback Shot
    print(f"Knockback ally result: {result}")
    print(f"Ally HP: {original_ally_hp} → {ally_warrior.health}")
    print(f"Ally position: {original_ally_pos} → {ally_warrior.position}")
    
    if ally_warrior.health < original_ally_hp:
        print(f"✅ FRIENDLY FIRE WORKING - Ally took knockback damage")
    else:
        print(f"❌ FRIENDLY FIRE NOT WORKING - Ally unharmed")
    
    # Test 3: Rogue Fan of Knives (should be enemies only)
    print(f"\n📋 TEST 3: Rogue Fan of Knives (Enemies Only)")
    print("-" * 45)
    
    rogue = Rogue(1)
    ally_hunter = Hunter(1)  # Same player
    enemy_hunter = Hunter(2)  # Different player
    
    # Position for Fan of Knives test
    rogue.position = (4, 4)
    ally_hunter.position = (2, 3)  # Knight move position
    enemy_hunter.position = (2, 5)  # Knight move position
    
    rogue.board = game.board
    ally_hunter.board = game.board
    enemy_hunter.board = game.board
    
    game.board.units = {
        (4, 4): rogue,
        (2, 3): ally_hunter,
        (2, 5): enemy_hunter
    }
    
    original_ally_hp = ally_hunter.health
    original_enemy_hp = enemy_hunter.health
    
    print(f"Before Fan of Knives:")
    print(f"  Ally Hunter HP: {original_ally_hp}")
    print(f"  Enemy Hunter HP: {original_enemy_hp}")
    
    # Use Fan of Knives
    result = rogue.use_ability(6, rogue.position, game)  # Fan of Knives
    
    print(f"After Fan of Knives:")
    print(f"  Ally Hunter HP: {ally_hunter.health}")
    print(f"  Enemy Hunter HP: {enemy_hunter.health}")
    
    if ally_hunter.health == original_ally_hp:
        print(f"✅ Fan of Knives: No friendly fire (enemies only ability)")
    else:
        print(f"❌ Fan of Knives: Friendly fire detected")
    
    if enemy_hunter.health < original_enemy_hp:
        print(f"✅ Fan of Knives: Enemy damaged correctly")
    else:
        print(f"❌ Fan of Knives: Enemy not damaged")
    
    print(f"\n" + "=" * 50)
    print("🎯 FRIENDLY FIRE TEST SUMMARY")
    print("-" * 30)
    print("✅ Default Behavior: Friendly fire enabled")
    print("✅ Hunter abilities: Damage allies and enemies")
    print("✅ Special abilities: Can be marked 'enemies only'")
    print("✅ Fan of Knives: Enemies only (as specified)")
    print("\n🔥 Friendly fire is now the default behavior!")

if __name__ == "__main__":
    test_friendly_fire()

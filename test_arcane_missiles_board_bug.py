#!/usr/bin/env python3
"""
Test to investigate the Arcane Missiles board reference bug
"""

import pygame
from game_state import Game
from units.mage import Mage
from units.warrior import Warrior

def test_arcane_missiles_board_bug():
    """Test Arcane Missiles board reference issue"""
    pygame.init()
    
    print("🐛 TESTING ARCANE MISSILES BOARD BUG 🐛")
    print("=" * 42)
    
    # Test 1: Arcane Missiles WITHOUT moving first
    print("📋 TEST 1: Arcane Missiles WITHOUT Moving First")
    print("-" * 47)
    
    game = Game()
    mage = Mage(1)
    target = Warrior(2)
    
    # Position units
    mage.position = (4, 4)
    target.position = (3, 4)  # 1 tile north
    
    # Set up board
    mage.board = game.board  # Manually set board reference
    game.board.units = {
        (4, 4): mage,
        (3, 4): target
    }
    
    print(f"Setup:")
    print(f"  Mage at {mage.position}")
    print(f"  Target at {target.position}")
    print(f"  Mage board reference: {mage.board is not None}")
    print(f"  Target HP before: {target.health}")
    
    # Find Arcane Missile ability
    arcane_ability_idx = None
    for i, ability in enumerate(mage.abilities):
        if ability.name == "Arcane Missile":
            arcane_ability_idx = i
            break
    
    # Use Arcane Missiles WITHOUT moving first
    north_target = (3, 4)
    print(f"\n🚀 Using Arcane Missiles WITHOUT moving first...")
    print(f"  Board reference before ability: {mage.board is not None}")
    
    result = mage.use_ability(arcane_ability_idx, north_target, game)
    
    print(f"\nResults:")
    print(f"  Ability result: {result}")
    print(f"  Target HP after: {target.health}")
    damage_dealt = 7 - target.health
    
    if damage_dealt > 0:
        print(f"✅ Damage dealt: {damage_dealt}")
    else:
        print(f"❌ NO DAMAGE DEALT - BUG CONFIRMED!")
    
    # Test 2: Arcane Missiles AFTER moving first
    print(f"\n📋 TEST 2: Arcane Missiles AFTER Moving First")
    print("-" * 44)
    
    game2 = Game()
    mage2 = Mage(1)
    target2 = Warrior(2)
    
    # Position units with space for movement
    mage2.position = (5, 4)
    target2.position = (3, 4)  # 2 tiles north from final mage position
    
    # Set up board
    mage2.board = game2.board
    game2.board.units = {
        (5, 4): mage2,
        (3, 4): target2
    }
    
    print(f"Setup:")
    print(f"  Mage at {mage2.position}")
    print(f"  Target at {target2.position}")
    print(f"  Target HP before: {target2.health}")
    
    # First, move the mage
    print(f"\n🚶 Moving Mage first...")
    move_result = mage2.use_ability(0, (4, 4), game2)  # Move to (4,4)
    if move_result:
        game2.board.move_unit((5, 4), (4, 4))  # Update board
        print(f"  Mage moved to: {mage2.position}")
        print(f"  Board reference after move: {mage2.board is not None}")
    
    # Now use Arcane Missiles
    north_target2 = (3, 4)
    print(f"\n🚀 Using Arcane Missiles AFTER moving...")
    result2 = mage2.use_ability(arcane_ability_idx, north_target2, game2)
    
    print(f"\nResults:")
    print(f"  Ability result: {result2}")
    print(f"  Target HP after: {target2.health}")
    damage_dealt2 = 7 - target2.health
    
    if damage_dealt2 > 0:
        print(f"✅ Damage dealt: {damage_dealt2}")
    else:
        print(f"❌ NO DAMAGE DEALT - Still buggy!")
    
    # Test 3: Board Reference Investigation
    print(f"\n📋 TEST 3: Board Reference Investigation")
    print("-" * 40)
    
    game3 = Game()
    mage3 = Mage(1)
    target3 = Warrior(2)
    
    # Position units
    mage3.position = (4, 4)
    target3.position = (3, 4)
    
    # Set up board
    game3.board.units = {
        (4, 4): mage3,
        (3, 4): target3
    }
    
    print(f"Board reference states:")
    print(f"  Before setting: mage.board = {mage3.board}")
    
    # Manually set board reference (like game logic does)
    mage3.board = game3.board
    print(f"  After manual set: mage.board = {mage3.board is not None}")
    print(f"  Board has units: {len(game3.board.units)} units")
    print(f"  Target in board: {target3.position in game3.board.units}")
    
    # Check if board.units lookup works
    lookup_result = game3.board.units.get((3, 4))
    print(f"  Board lookup (3,4): {lookup_result is not None}")
    if lookup_result:
        print(f"    Found unit: {lookup_result.name}")
    
    # Test the exact line that fails in Arcane Missiles
    print(f"\n🔍 Testing exact lookup from Arcane Missiles:")
    target_r, target_c = 3, 4
    target_unit = mage3.board.units.get((target_r, target_c))
    print(f"  mage3.board.units.get((3, 4)): {target_unit}")
    if target_unit:
        print(f"    Unit found: {target_unit.name}")
        print(f"    Unit HP: {target_unit.health}")
    else:
        print(f"    ❌ NO UNIT FOUND - This is the bug!")
    
    print(f"\n" + "=" * 42)
    print("🐛 BUG ANALYSIS")
    print("-" * 15)
    print("If Test 1 fails but Test 2 works, the issue is:")
    print("- Board reference is not properly set without movement")
    print("- Movement somehow refreshes/fixes the board reference")
    print("- The game logic board assignment might be inconsistent")

if __name__ == "__main__":
    test_arcane_missiles_board_bug()

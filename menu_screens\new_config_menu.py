#!/usr/bin/env python3
"""
New integrated configuration menu for the main game
Replaces the old config system with the clean ability-based system
"""

import pygame
import json
import sys
from typing import Dict, List, Optional, Tuple
try:
    from menu_screens.button import Button
    import game_constants as const
except ImportError:
    # When running directly from menu_screens directory
    from button import Button
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    import game_constants as const

class ConfigSlider:
    """A simple, reliable slider widget"""
    
    def __init__(self, x: int, y: int, width: int, label: str, 
                 value: float, min_val: float, max_val: float):
        self.rect = pygame.Rect(x, y + 20, width, 10)
        self.label = label
        self.value = value
        self.min_val = min_val
        self.max_val = max_val
        self.dragging = False
        self.label_pos = (x, y)
    
    def handle_event(self, event) -> bool:
        """Handle mouse events, return True if value changed"""
        if event.type == pygame.MOUSEBUTTONDOWN:
            if self.rect.collidepoint(event.pos):
                self.dragging = True
                self._update_value_from_mouse(event.pos[0])
                return True
        
        elif event.type == pygame.MOUSEBUTTONUP:
            self.dragging = False
        
        elif event.type == pygame.MOUSEMOTION and self.dragging:
            self._update_value_from_mouse(event.pos[0])
            return True
        
        return False
    
    def _update_value_from_mouse(self, mouse_x: int):
        """Update value based on mouse position"""
        relative_x = mouse_x - self.rect.x
        ratio = max(0, min(1, relative_x / self.rect.width))
        self.value = self.min_val + ratio * (self.max_val - self.min_val)
    
    def render(self, screen: pygame.Surface, font: pygame.font.Font):
        """Render the slider"""
        # Label
        label_text = f"{self.label}: {self.value:.1f}" if isinstance(self.value, float) else f"{self.label}: {int(self.value)}"
        label_surf = font.render(label_text, True, const.WHITE)
        screen.blit(label_surf, self.label_pos)
        
        # Track
        pygame.draw.rect(screen, const.DARK_GRAY, self.rect)
        pygame.draw.rect(screen, const.WHITE, self.rect, 1)
        
        # Handle
        if self.max_val > self.min_val:
            ratio = (self.value - self.min_val) / (self.max_val - self.min_val)
            handle_x = self.rect.x + ratio * self.rect.width
            handle_rect = pygame.Rect(handle_x - 5, self.rect.y - 2, 10, 14)
            pygame.draw.rect(screen, const.LIGHT_GRAY, handle_rect)

class NewConfigMenu:
    """New integrated configuration menu"""
    
    def __init__(self, screen, clock):
        self.screen = screen
        self.clock = clock
        self.running = True
        
        # Fonts
        self.title_font = pygame.font.Font(None, 48)
        self.button_font = pygame.font.Font(None, 32)
        self.small_font = pygame.font.Font(None, 24)
        
        # Current state
        self.selected_class = "Warrior"
        self.mode = "class"  # "class" or "abilities"
        self.selected_ability = None
        
        # Data - Load from game or use defaults
        self.class_data = {
            "Warrior": {"hp": 12, "movement": 2},
            "Mage": {"hp": 8, "movement": 2},
            "Cleric": {"hp": 10, "movement": 1},
            "Rogue": {"hp": 9, "movement": 3},
            "Hunter": {"hp": 10, "movement": 3},
            "Warlock": {"hp": 6, "movement": 2},
            "Paladin": {"hp": 8, "movement": 2},
            "Druid": {"hp": 7, "movement": 2},
            "Bard": {"hp": 5, "movement": 3}
        }
        
        self.ability_data = {
            "Warrior": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 2, "ap_cost": 2, "cooldown": 0},
                "Charge": {"damage": 3, "ap_cost": 3, "cooldown": 2},
                "Shield Bash": {"damage": 2, "ap_cost": 2, "cooldown": 1},
                "Whirlwind": {"damage": 4, "ap_cost": 4, "cooldown": 3},
                "Taunt": {"damage": 0, "ap_cost": 1, "cooldown": 2},
                "Berserker Rage": {"damage": 0, "ap_cost": 3, "cooldown": 4},
                "Shield Wall": {"damage": 0, "ap_cost": 2, "cooldown": 3}
            },
            "Mage": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 1, "ap_cost": 1, "cooldown": 0},
                "Fireball": {"damage": 3, "ap_cost": 4, "cooldown": 2},
                "Ice Spike": {"damage": 2, "ap_cost": 3, "cooldown": 1},
                "Teleport": {"damage": 0, "ap_cost": 2, "cooldown": 3},
                "Arcane Missiles": {"damage": 3, "ap_cost": 5, "cooldown": 2},
                "Frost Nova": {"damage": 1, "ap_cost": 3, "cooldown": 4},
                "Lightning Bolt": {"damage": 4, "ap_cost": 4, "cooldown": 2},
                "Cone of Cold": {"damage": 2, "ap_cost": 4, "cooldown": 3},
                "Meteor": {"damage": 6, "ap_cost": 6, "cooldown": 5}
            },
            "Cleric": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 1, "ap_cost": 1, "cooldown": 0},
                "Heal": {"damage": 0, "ap_cost": 2, "cooldown": 1, "heal_amount": 2},
                "Mass Heal": {"damage": 0, "ap_cost": 5, "cooldown": 3, "heal_amount": 1},
                "Cleanse": {"damage": 0, "ap_cost": 2, "cooldown": 2},
                "Holy Smite": {"damage": 3, "ap_cost": 3, "cooldown": 2},
                "Divine Protection": {"damage": 0, "ap_cost": 2, "cooldown": 3},
                "Sanctuary": {"damage": 0, "ap_cost": 3, "cooldown": 4},
                "Turn Undead": {"damage": 4, "ap_cost": 4, "cooldown": 3},
                "Blessing": {"damage": 0, "ap_cost": 2, "cooldown": 2}
            },
            "Rogue": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 2, "ap_cost": 1, "cooldown": 0},
                "Backstab": {"damage": 4, "ap_cost": 2, "cooldown": 2},
                "Fan of Knives": {"damage": 2, "ap_cost": 2, "cooldown": 2},
                "Assassination": {"damage": 5, "ap_cost": 3, "cooldown": 3},
                "Stealth": {"damage": 0, "ap_cost": 2, "cooldown": 4},
                "Poison Blade": {"damage": 3, "ap_cost": 2, "cooldown": 2},
                "Shadow Step": {"damage": 0, "ap_cost": 2, "cooldown": 3},
                "Smoke Bomb": {"damage": 0, "ap_cost": 3, "cooldown": 4},
                "Critical Strike": {"damage": 6, "ap_cost": 4, "cooldown": 3}
            },
            "Hunter": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 2, "ap_cost": 2, "cooldown": 0},
                "Multishot": {"damage": 2, "ap_cost": 3, "cooldown": 1},
                "Triple Shot": {"damage": 3, "ap_cost": 4, "cooldown": 2},
                "Piercing Shot": {"damage": 3, "ap_cost": 3, "cooldown": 2},
                "Crippling Shot": {"damage": 2, "ap_cost": 2, "cooldown": 1},
                "Explosive Shot": {"damage": 4, "ap_cost": 4, "cooldown": 3},
                "Hunter's Mark": {"damage": 0, "ap_cost": 1, "cooldown": 2},
                "Trap": {"damage": 3, "ap_cost": 3, "cooldown": 4},
                "Eagle Eye": {"damage": 0, "ap_cost": 2, "cooldown": 3}
            },
            "Warlock": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 1, "ap_cost": 1, "cooldown": 0},
                "Life Drain": {"damage": 2, "ap_cost": 3, "cooldown": 2},
                "Curse": {"damage": 0, "ap_cost": 2, "cooldown": 3},
                "Fear": {"damage": 1, "ap_cost": 3, "cooldown": 2},
                "Dark Pact": {"damage": 0, "ap_cost": 0, "cooldown": 4},
                "Soul Burn": {"damage": 1, "ap_cost": 3, "cooldown": 3},
                "Shadow Bolt": {"damage": 4, "ap_cost": 4, "cooldown": 2}
            },
            "Paladin": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 2, "ap_cost": 1, "cooldown": 0},
                "Lay on Hands": {"damage": 0, "ap_cost": 3, "cooldown": 3, "heal_amount": 4},
                "Divine Smite": {"damage": 3, "ap_cost": 3, "cooldown": 2},
                "Blessing": {"damage": 0, "ap_cost": 2, "cooldown": 3},
                "Consecrate": {"damage": 0, "ap_cost": 4, "cooldown": 4},
                "Turn Undead": {"damage": 2, "ap_cost": 3, "cooldown": 3},
                "Divine Shield": {"damage": 0, "ap_cost": 4, "cooldown": 5}
            },
            "Druid": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 1, "ap_cost": 1, "cooldown": 0},
                "Wild Shape": {"damage": 0, "ap_cost": 3, "cooldown": 4},
                "Entangle": {"damage": 1, "ap_cost": 2, "cooldown": 2},
                "Healing Spring": {"damage": 0, "ap_cost": 3, "cooldown": 3, "heal_amount": 2},
                "Thorn Barrier": {"damage": 2, "ap_cost": 3, "cooldown": 3},
                "Call Lightning": {"damage": 3, "ap_cost": 4, "cooldown": 3},
                "Nature's Wrath": {"damage": 4, "ap_cost": 4, "cooldown": 2}
            },
            "Bard": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 1, "ap_cost": 1, "cooldown": 0},
                "Inspire": {"damage": 0, "ap_cost": 2, "cooldown": 2},
                "Song of Healing": {"damage": 0, "ap_cost": 3, "cooldown": 3, "heal_amount": 2},
                "Discordant Note": {"damage": 2, "ap_cost": 3, "cooldown": 2},
                "Bardic Knowledge": {"damage": 0, "ap_cost": 1, "cooldown": 1},
                "Mass Inspiration": {"damage": 0, "ap_cost": 4, "cooldown": 4},
                "Shatter": {"damage": 5, "ap_cost": 4, "cooldown": 3}
            }
        }
        
        # UI elements
        self.sliders: List[ConfigSlider] = []
        self.back_button = None
        self.save_button = None
        self._create_buttons()
        self._create_sliders()
    
    def _create_buttons(self):
        """Create UI buttons"""
        screen_width, screen_height = self.screen.get_size()
        
        # Back and Save buttons
        self.back_button = Button(50, screen_height - 80, 120, 50, "Back")
        self.save_button = Button(200, screen_height - 80, 120, 50, "Save Config")
    
    def _create_sliders(self):
        """Create sliders based on current mode and selection"""
        self.sliders.clear()
        
        if self.mode == "class":
            # Class sliders
            class_stats = self.class_data[self.selected_class]
            y_start = 200
            
            self.sliders.append(ConfigSlider(400, y_start, 200, "HP", 
                                           class_stats["hp"], 5, 20))
            self.sliders.append(ConfigSlider(400, y_start + 50, 200, "Movement", 
                                           class_stats["movement"], 1, 5))
        
        elif self.mode == "abilities" and self.selected_ability:
            # Ability sliders
            ability_stats = self.ability_data[self.selected_class][self.selected_ability]
            y_start = 200
            
            if ability_stats["damage"] > 0:  # Only show damage slider if ability does damage
                self.sliders.append(ConfigSlider(400, y_start, 200, "Damage",
                                               ability_stats["damage"], 0, 10))
                y_start += 50

            # Add healing slider for healing abilities
            if "heal_amount" in ability_stats:
                self.sliders.append(ConfigSlider(400, y_start, 200, "Heal Amount",
                                               ability_stats["heal_amount"], 1, 5))
                y_start += 50
            
            self.sliders.append(ConfigSlider(400, y_start, 200, "AP Cost", 
                                           ability_stats["ap_cost"], 0, 8))
            
            if ability_stats["cooldown"] > 0:  # Only show cooldown if ability has one
                self.sliders.append(ConfigSlider(400, y_start + 50, 200, "Cooldown", 
                                               ability_stats["cooldown"], 0, 5))
    
    def handle_event(self, event) -> bool:
        """Handle events, return True if something changed"""
        changed = False
        
        # Handle button events
        if self.back_button.handle_event(event):
            self.running = False
            return True
        
        if self.save_button.handle_event(event):
            self._save_configuration()
            return True
        
        # Handle slider events
        for slider in self.sliders:
            if slider.handle_event(event):
                self._update_data_from_sliders()
                changed = True
        
        # Handle other clicks
        if event.type == pygame.MOUSEBUTTONDOWN:
            mouse_x, mouse_y = event.pos
            
            # Class selection buttons
            class_buttons = ["Warrior", "Mage", "Cleric", "Rogue", "Hunter"]
            for i, class_name in enumerate(class_buttons):
                button_rect = pygame.Rect(50 + i * 120, 50, 100, 30)
                if button_rect.collidepoint(mouse_x, mouse_y):
                    self.selected_class = class_name
                    self.selected_ability = None
                    self._create_sliders()
                    changed = True
            
            # Mode buttons
            class_button = pygame.Rect(50, 100, 100, 30)
            ability_button = pygame.Rect(160, 100, 100, 30)
            
            if class_button.collidepoint(mouse_x, mouse_y):
                self.mode = "class"
                self.selected_ability = None
                self._create_sliders()
                changed = True
            elif ability_button.collidepoint(mouse_x, mouse_y):
                self.mode = "abilities"
                self._create_sliders()
                changed = True
            
            # Ability buttons (only in ability mode)
            if self.mode == "abilities":
                abilities = list(self.ability_data[self.selected_class].keys())
                for i, ability_name in enumerate(abilities):
                    button_rect = pygame.Rect(50, 150 + i * 35, 150, 30)
                    if button_rect.collidepoint(mouse_x, mouse_y):
                        self.selected_ability = ability_name
                        self._create_sliders()
                        changed = True
        
        return changed
    
    def _update_data_from_sliders(self):
        """Update data based on current slider values"""
        if self.mode == "class":
            class_stats = self.class_data[self.selected_class]
            if len(self.sliders) >= 2:
                class_stats["hp"] = int(self.sliders[0].value)
                class_stats["movement"] = int(self.sliders[1].value)
        
        elif self.mode == "abilities" and self.selected_ability:
            ability_stats = self.ability_data[self.selected_class][self.selected_ability]
            slider_idx = 0
            
            # Update damage if slider exists
            if ability_stats["damage"] > 0 and slider_idx < len(self.sliders):
                ability_stats["damage"] = self.sliders[slider_idx].value
                slider_idx += 1

            # Update heal amount if slider exists
            if "heal_amount" in ability_stats and slider_idx < len(self.sliders):
                ability_stats["heal_amount"] = int(self.sliders[slider_idx].value)
                slider_idx += 1
            
            # Update AP cost
            if slider_idx < len(self.sliders):
                ability_stats["ap_cost"] = int(self.sliders[slider_idx].value)
                slider_idx += 1
            
            # Update cooldown if slider exists
            if ability_stats["cooldown"] > 0 and slider_idx < len(self.sliders):
                ability_stats["cooldown"] = int(self.sliders[slider_idx].value)
    
    def _save_configuration(self):
        """Save current configuration to file"""
        config_data = {
            "class_data": self.class_data,
            "ability_data": self.ability_data
        }

        try:
            with open("game_balance_config.json", 'w') as f:
                json.dump(config_data, f, indent=2)
            print("Configuration saved to game_balance_config.json")

            # Also update the game config that units use
            self._update_game_config()
        except Exception as e:
            print(f"Error saving configuration: {e}")

    def _update_game_config(self):
        """Update the game configuration that units actually use"""
        try:
            # Update the GAME_CONFIG that units import
            import game_config

            # Update class configurations
            for class_name, class_stats in self.class_data.items():
                config_key = f"{class_name.lower()}_config"
                if not hasattr(game_config, 'GAME_CONFIG'):
                    game_config.GAME_CONFIG = {}
                if config_key not in game_config.GAME_CONFIG:
                    game_config.GAME_CONFIG[config_key] = {}

                game_config.GAME_CONFIG[config_key]["hp"] = class_stats["hp"]
                game_config.GAME_CONFIG[config_key]["movement"] = class_stats["movement"]

            # Update ability configurations
            for class_name, abilities in self.ability_data.items():
                config_key = f"{class_name.lower()}_config"
                if config_key not in game_config.GAME_CONFIG:
                    game_config.GAME_CONFIG[config_key] = {}

                for ability_name, ability_stats in abilities.items():
                    # Convert ability names to config keys
                    ability_key = ability_name.lower().replace(" ", "_") + "_ap_cost"
                    game_config.GAME_CONFIG[config_key][ability_key] = ability_stats["ap_cost"]

                    # Store damage and cooldown for abilities that have them
                    if ability_stats["damage"] > 0:
                        damage_key = ability_name.lower().replace(" ", "_") + "_damage"
                        game_config.GAME_CONFIG[config_key][damage_key] = ability_stats["damage"]

                    if ability_stats["cooldown"] > 0:
                        cooldown_key = ability_name.lower().replace(" ", "_") + "_cooldown"
                        game_config.GAME_CONFIG[config_key][cooldown_key] = ability_stats["cooldown"]

            print("Game configuration updated successfully")
        except Exception as e:
            print(f"Error updating game config: {e}")
    
    def _load_configuration(self):
        """Load configuration from file if it exists"""
        try:
            with open("game_balance_config.json", 'r') as f:
                config_data = json.load(f)
            
            if "class_data" in config_data:
                self.class_data.update(config_data["class_data"])
            
            if "ability_data" in config_data:
                self.ability_data.update(config_data["ability_data"])
            
            print("Configuration loaded from game_balance_config.json")
        except FileNotFoundError:
            print("No saved configuration found, using defaults")
        except Exception as e:
            print(f"Error loading configuration: {e}")
    
    def draw_background(self):
        """Draw the background"""
        screen_width, screen_height = self.screen.get_size()
        for y in range(screen_height):
            color_value = int(30 + (y / screen_height) * 20)
            pygame.draw.line(self.screen, (color_value, color_value, color_value),
                           (0, y), (screen_width, y))
    
    def render(self):
        """Render the configuration UI"""
        # Clear background
        self.draw_background()
        
        # Title
        title = self.title_font.render("Game Configuration", True, const.WHITE)
        screen_width = self.screen.get_size()[0]
        title_rect = title.get_rect(center=(screen_width // 2, 25))
        self.screen.blit(title, title_rect)
        
        # Class selection buttons
        class_buttons = ["Warrior", "Mage", "Cleric", "Rogue", "Hunter"]
        for i, class_name in enumerate(class_buttons):
            button_rect = pygame.Rect(50 + i * 120, 50, 100, 30)
            color = const.PLAYER1_COLOR if class_name == self.selected_class else const.DARK_GRAY
            
            pygame.draw.rect(self.screen, color, button_rect)
            pygame.draw.rect(self.screen, const.WHITE, button_rect, 1)
            
            text = self.small_font.render(class_name, True, const.WHITE)
            text_rect = text.get_rect(center=button_rect.center)
            self.screen.blit(text, text_rect)
        
        # Mode buttons
        class_button = pygame.Rect(50, 100, 100, 30)
        ability_button = pygame.Rect(160, 100, 100, 30)
        
        class_color = const.PLAYER1_COLOR if self.mode == "class" else const.DARK_GRAY
        ability_color = const.PLAYER1_COLOR if self.mode == "abilities" else const.DARK_GRAY
        
        pygame.draw.rect(self.screen, class_color, class_button)
        pygame.draw.rect(self.screen, const.WHITE, class_button, 1)
        pygame.draw.rect(self.screen, ability_color, ability_button)
        pygame.draw.rect(self.screen, const.WHITE, ability_button, 1)
        
        class_text = self.small_font.render("Class Stats", True, const.WHITE)
        ability_text = self.small_font.render("Abilities", True, const.WHITE)
        
        self.screen.blit(class_text, (class_button.x + 10, class_button.y + 8))
        self.screen.blit(ability_text, (ability_button.x + 20, ability_button.y + 8))
        
        # Ability buttons (if in ability mode)
        if self.mode == "abilities":
            abilities = list(self.ability_data[self.selected_class].keys())
            for i, ability_name in enumerate(abilities):
                button_rect = pygame.Rect(50, 150 + i * 35, 150, 30)
                color = const.PLAYER1_COLOR if ability_name == self.selected_ability else const.DARK_GRAY
                
                pygame.draw.rect(self.screen, color, button_rect)
                pygame.draw.rect(self.screen, const.WHITE, button_rect, 1)
                
                text = self.small_font.render(ability_name, True, const.WHITE)
                self.screen.blit(text, (button_rect.x + 5, button_rect.y + 8))
        
        # Render sliders
        for slider in self.sliders:
            slider.render(self.screen, self.small_font)
        
        # Status info
        status_y = self.screen.get_size()[1] - 150
        if self.mode == "class":
            class_stats = self.class_data[self.selected_class]
            status_text = f"{self.selected_class}: HP={class_stats['hp']}, Movement={class_stats['movement']}"
        else:
            if self.selected_ability:
                ability_stats = self.ability_data[self.selected_class][self.selected_ability]
                status_text = f"{self.selected_ability}: Dmg={ability_stats['damage']}, AP={ability_stats['ap_cost']}, CD={ability_stats['cooldown']}"
            else:
                status_text = "Select an ability to configure"
        
        status_surf = self.small_font.render(status_text, True, const.LIGHT_GRAY)
        self.screen.blit(status_surf, (50, status_y))
        
        # Draw buttons
        self.back_button.draw(self.screen, self.button_font)
        self.save_button.draw(self.screen, self.button_font)
    
    def run(self):
        """Run the configuration menu"""
        self._load_configuration()  # Load saved config if available
        
        while self.running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()
                else:
                    self.handle_event(event)
            
            self.render()
            pygame.display.flip()
            self.clock.tick(const.FPS)

# For testing
if __name__ == "__main__":
    pygame.init()
    screen = pygame.display.set_mode((const.WINDOW_WIDTH, const.WINDOW_HEIGHT))
    pygame.display.set_caption("New Configuration Menu")
    clock = pygame.time.Clock()
    
    config_menu = NewConfigMenu(screen, clock)
    config_menu.run()
    
    pygame.quit()
    sys.exit()

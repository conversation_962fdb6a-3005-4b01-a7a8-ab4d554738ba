import sys
import pygame
# import numpy as np # Not used directly anymore
# from game_board import GameBoard, BOARD_SIZE, CELL_SIZE, BOARD_PADDING # GameBoard is in game_state.Game
# from game_units import <PERSON>, <PERSON>, <PERSON>, Pawn, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ge # These are in game_state.Game.available_units initialization or handled by game_setup
# from game_config import GAME_CONFIG # Used within game_state.Game if needed there
# from game_settings import SELECTED_ABILITIES # Used within game_state.Game
# import math # Not used directly anymore

# Import the refactored components
from game_state import Game
from game_loop import run_game_loop
import game_setup # Used by game_loop
import game_logic # Used by game_loop
import game_ui    # Used by game_loop (via game_draw)
import game_visuals # Used by game_loop
import game_draw  # Used by game_loop
from game_constants import STATE_SETUP, STATE_PLAYING, STATE_GAME_OVER # Potentially used if any logic remains here

# Constants previously in Game class or game.py top level
# WINDOW_WIDTH, WINDOW_HEIGHT, FPS are defined in game_constants.py
# Colors are defined in game_constants.py

# The Game class is now in game_state.py
# The run method is now in game_loop.py (run_game_loop function)
# Other helper methods like _create_unit_buttons, _filter_unit_abilities, board_to_screen are in game_state.Game

if __name__ == "__main__":
    # Pygame.init() is now called inside Game.__init__ in game_state.py
    # Consider moving pygame.init() to the absolute entry point (e.g. launcher.py or main_menu.py if they start the game)
    game_instance = Game(fullscreen=False) # Create an instance of the Game state object
    run_game_loop(game_instance)          # Run the game loop with this instance
    
    pygame.quit()
    sys.exit() # Ensure the game exits cleanly if run directly

#!/usr/bin/env python3
"""
Test if the synchronization and reset_turn fix works
"""

import pygame
import sys
from game_state import Game

def test_sync_fix():
    """Test that the sync and reset_turn fix works"""
    print("🔄 TESTING SYNC AND RESET_TURN FIX")
    print("=" * 35)
    
    pygame.init()
    
    try:
        # Test 1: Game creation
        print("📋 TEST 1: Game Creation")
        print("-" * 25)
        
        game = Game()
        print("✅ Game created successfully")
        
        # Test 2: New classes available
        print(f"\n📋 TEST 2: New Classes Available")
        print("-" * 32)
        
        new_classes = ["Warlock", "Paladin", "Druid", "Bard"]
        for class_name in new_classes:
            if class_name in game.available_units[1]:
                print(f"✅ {class_name}: Available")
            else:
                print(f"❌ {class_name}: Missing")
                return False
        
        # Test 3: Create new class instances and test reset_turn
        print(f"\n📋 TEST 3: Reset Turn Method Test")
        print("-" * 33)
        
        from units import Warlock, <PERSON>lad<PERSON>, Druid, Bard
        
        test_units = [
            ("<PERSON><PERSON>", <PERSON><PERSON>(1)),
            ("<PERSON>lad<PERSON>", <PERSON><PERSON><PERSON>(1)),
            ("Druid", Druid(1)),
            ("Bard", Bard(1))
        ]
        
        for class_name, unit in test_units:
            try:
                # Test reset_turn with game parameter
                unit.reset_turn(game=game)
                print(f"✅ {class_name}: reset_turn(game=game) works")
            except Exception as e:
                print(f"❌ {class_name}: reset_turn failed - {e}")
                return False
        
        # Test 4: Test unit buttons
        print(f"\n📋 TEST 4: Unit Buttons Test")
        print("-" * 27)
        
        for class_name in new_classes:
            if class_name in game.unit_buttons:
                print(f"✅ {class_name}: Button created")
            else:
                print(f"❌ {class_name}: Button missing")
                return False
        
        pygame.quit()
        
        print(f"\n" + "=" * 35)
        print("🎯 SYNC AND FIX TEST SUMMARY")
        print("-" * 29)
        print("✅ Game creation successful")
        print("✅ New classes available")
        print("✅ reset_turn(game=game) works for all new classes")
        print("✅ Unit buttons created for all new classes")
        
        print(f"\n🎉 SYNC AND FIX SUCCESSFUL!")
        print("The game should now work without errors!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_sync_fix()
    if success:
        print(f"\n🚀 READY TO LAUNCH!")
        print("The game is synchronized and fixed!")
    else:
        print(f"\n🔧 STILL NEEDS WORK!")
        print("Check the errors above.")
    
    sys.exit(0 if success else 1)

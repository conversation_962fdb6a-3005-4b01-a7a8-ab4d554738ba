import pygame
import os
from pathlib import Path

class SoundManager:
    """
    Centralized sound management system for the game.
    Handles loading, playing, and managing all game sounds.
    """
    
    def __init__(self):
        # Initialize pygame mixer if not already done
        if not pygame.mixer.get_init():
            pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
        
        self.sounds = {}
        self.music_volume = 0.7
        self.sfx_volume = 0.8
        self.enabled = True
        
        # Create sounds directory if it doesn't exist
        self.sounds_dir = Path("assets/sounds")
        self.sounds_dir.mkdir(parents=True, exist_ok=True)
        
        # Load all available sounds
        self._load_sounds()
    
    def _load_sounds(self):
        """Load all sound files from the assets/sounds directory"""
        
        # Define sound categories and their files
        sound_categories = {
            # Class-specific sounds
            "warrior": ["attack.wav", "shield_bash.wav", "charge.wav", "taunt.wav"],
            "mage": ["fireball.wav", "ice_spike.wav", "teleport.wav", "magic_missile.wav"],
            "hunter": ["bow_shot.wav", "multishot.wav", "trap.wav", "eagle_eye.wav"],
            "rogue": ["backstab.wav", "stealth.wav", "poison.wav", "fan_of_knives.wav"],
            "cleric": ["heal.wav", "holy_smite.wav", "blessing.wav", "sanctuary.wav"],
            "king": ["royal_decree.wav", "divine_shield.wav", "inspire.wav", "execution.wav"],
            "warlock": ["life_drain.wav", "curse.wav", "fear.wav", "shadow_bolt.wav"],
            "paladin": ["divine_smite.wav", "lay_on_hands.wav", "consecrate.wav", "turn_undead.wav"],
            "druid": ["wild_shape.wav", "entangle.wav", "lightning.wav", "nature_wrath.wav"],
            "bard": ["inspire.wav", "song_heal.wav", "discord.wav", "shatter.wav"],
            
            # General game sounds
            "ui": ["click.wav", "hover.wav", "error.wav", "success.wav"],
            "game": ["turn_start.wav", "victory.wav", "defeat.wav", "move.wav"],
            "effects": ["explosion.wav", "magic.wav", "hit.wav", "miss.wav"]
        }
        
        # Try to load each sound file
        for category, sound_files in sound_categories.items():
            category_dir = self.sounds_dir / category
            category_dir.mkdir(exist_ok=True)
            
            for sound_file in sound_files:
                sound_path = category_dir / sound_file
                sound_key = f"{category}_{sound_file.replace('.wav', '')}"
                
                if sound_path.exists():
                    try:
                        self.sounds[sound_key] = pygame.mixer.Sound(str(sound_path))
                        self.sounds[sound_key].set_volume(self.sfx_volume)
                    except pygame.error as e:
                        print(f"Could not load sound {sound_path}: {e}")
                else:
                    # Create placeholder sound or use default
                    self._create_placeholder_sound(sound_key)
    
    def _create_placeholder_sound(self, sound_key):
        """Create a simple placeholder sound for missing audio files"""
        try:
            # Create a simple beep sound as placeholder
            duration = 0.1  # seconds
            sample_rate = 22050
            frames = int(duration * sample_rate)
            
            # Generate a simple tone
            import numpy as np
            frequency = 440  # A note
            arr = np.zeros((frames, 2))
            
            for i in range(frames):
                wave = np.sin(2 * np.pi * frequency * i / sample_rate)
                arr[i][0] = wave * 0.1  # Left channel
                arr[i][1] = wave * 0.1  # Right channel
            
            # Convert to pygame sound
            sound_array = (arr * 32767).astype(np.int16)
            self.sounds[sound_key] = pygame.sndarray.make_sound(sound_array)
            self.sounds[sound_key].set_volume(self.sfx_volume * 0.3)  # Quieter for placeholder
            
        except ImportError:
            # If numpy not available, create silent placeholder
            self.sounds[sound_key] = None
        except Exception as e:
            print(f"Could not create placeholder sound for {sound_key}: {e}")
            self.sounds[sound_key] = None
    
    def play_sound(self, sound_key, volume_override=None):
        """Play a sound by its key"""
        if not self.enabled:
            return
        
        if sound_key in self.sounds and self.sounds[sound_key]:
            try:
                sound = self.sounds[sound_key]
                if volume_override is not None:
                    sound.set_volume(volume_override)
                sound.play()
            except pygame.error as e:
                print(f"Error playing sound {sound_key}: {e}")
    
    def play_class_ability_sound(self, class_name, ability_name):
        """Play sound for a specific class ability"""
        # Convert ability name to sound key format
        ability_key = ability_name.lower().replace(" ", "_").replace("'", "")
        sound_key = f"{class_name.lower()}_{ability_key}"
        
        # Try specific ability sound first
        if sound_key in self.sounds:
            self.play_sound(sound_key)
        else:
            # Fall back to generic ability sound
            self.play_sound("effects_magic")
    
    def play_ui_sound(self, action):
        """Play UI sound (click, hover, error, success)"""
        sound_key = f"ui_{action}"
        self.play_sound(sound_key)
    
    def play_game_sound(self, event):
        """Play game event sound (turn_start, victory, defeat, move)"""
        sound_key = f"game_{event}"
        self.play_sound(sound_key)
    
    def set_sfx_volume(self, volume):
        """Set sound effects volume (0.0 to 1.0)"""
        self.sfx_volume = max(0.0, min(1.0, volume))
        for sound in self.sounds.values():
            if sound:
                sound.set_volume(self.sfx_volume)
    
    def set_music_volume(self, volume):
        """Set music volume (0.0 to 1.0)"""
        self.music_volume = max(0.0, min(1.0, volume))
        pygame.mixer.music.set_volume(self.music_volume)
    
    def toggle_sound(self):
        """Toggle sound on/off"""
        self.enabled = not self.enabled
        return self.enabled
    
    def stop_all_sounds(self):
        """Stop all currently playing sounds"""
        pygame.mixer.stop()
    
    def get_available_sounds(self):
        """Get list of all available sound keys"""
        return list(self.sounds.keys())

# Global sound manager instance
sound_manager = SoundManager()

# Convenience functions for easy access
def play_ability_sound(class_name, ability_name):
    """Play sound for ability use"""
    sound_manager.play_class_ability_sound(class_name, ability_name)

def play_ui_sound(action):
    """Play UI sound"""
    sound_manager.play_ui_sound(action)

def play_game_sound(event):
    """Play game event sound"""
    sound_manager.play_game_sound(event)

def set_sound_volume(volume):
    """Set overall sound volume"""
    sound_manager.set_sfx_volume(volume)

def toggle_sound():
    """Toggle sound on/off"""
    return sound_manager.toggle_sound()

# Sound integration instructions
SOUND_SETUP_INSTRUCTIONS = """
🎵 SOUND SYSTEM SETUP INSTRUCTIONS

1. CREATE SOUND DIRECTORIES:
   Create these folders in your project:
   - assets/sounds/warrior/
   - assets/sounds/mage/
   - assets/sounds/hunter/
   - assets/sounds/rogue/
   - assets/sounds/cleric/
   - assets/sounds/king/
   - assets/sounds/warlock/
   - assets/sounds/paladin/
   - assets/sounds/druid/
   - assets/sounds/bard/
   - assets/sounds/ui/
   - assets/sounds/game/
   - assets/sounds/effects/

2. ADD SOUND FILES:
   Place .wav files in appropriate directories:
   - warrior/attack.wav, warrior/shield_bash.wav, etc.
   - mage/fireball.wav, mage/teleport.wav, etc.
   - ui/click.wav, ui/hover.wav, etc.

3. INTEGRATE WITH ABILITIES:
   Add this to ability execution:
   ```python
   from sound_system import play_ability_sound
   
   def use_ability(self, ability_name):
       play_ability_sound(self.name, ability_name)
       # ... rest of ability code
   ```

4. INTEGRATE WITH UI:
   Add this to button clicks:
   ```python
   from sound_system import play_ui_sound
   
   def on_button_click(self):
       play_ui_sound("click")
       # ... rest of click handling
   ```

5. FREE SOUND RESOURCES:
   - freesound.org
   - zapsplat.com
   - Adobe Audition (free sounds)
   - YouTube Audio Library
"""

if __name__ == "__main__":
    print(SOUND_SETUP_INSTRUCTIONS)

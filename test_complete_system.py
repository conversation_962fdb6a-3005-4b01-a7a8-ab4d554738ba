#!/usr/bin/env python3
"""
Test the complete system: AP system + Configuration integration
"""

import pygame
import sys
from game_state import Game
from units.warrior import Warrior
from units.mage import Mage

def test_complete_system():
    """Test the complete integrated system"""
    pygame.init()
    
    print("🎮 TESTING COMPLETE INTEGRATED SYSTEM")
    print("=" * 42)
    
    # Test 1: AP System - One Action Per Turn
    print("📋 TEST 1: AP System - One Action Per Turn")
    print("-" * 40)
    
    game = Game()
    
    # Create units
    warrior = Warrior(1)
    mage = Mage(1)
    
    # Position units
    warrior.position = (4, 4)
    mage.position = (4, 5)
    
    # Set up board
    game.board.units = {(4, 4): warrior, (4, 5): mage}
    warrior.board = game.board
    mage.board = game.board
    
    # Start turn with enough AP
    game.start_player_turn(1)
    game.current_player_ap = 5  # Give enough AP for testing
    
    print(f"Initial state:")
    print(f"  Game AP: {game.current_player_ap}")
    print(f"  Warrior acted: {warrior.has_acted_this_turn}")
    print(f"  Mage acted: {mage.has_acted_this_turn}")
    
    # Test warrior action
    print(f"\n🚶 Warrior attempts to move:")
    move_success = warrior.use_ability(0, (4, 3), game)
    print(f"  Move success: {move_success}")
    print(f"  Game AP after: {game.current_player_ap}")
    print(f"  Warrior acted: {warrior.has_acted_this_turn}")
    
    # Test warrior second action (should fail)
    print(f"\n⚔️ Warrior attempts second action:")
    attack_success = warrior.use_ability(1, (4, 2), game)
    print(f"  Attack success: {attack_success}")
    print(f"  Reason: {'Already acted this turn' if not attack_success else 'Unexpected success'}")
    
    # Test mage action (should work)
    print(f"\n🔮 Mage attempts to move:")
    mage_move = mage.use_ability(0, (4, 6), game)
    print(f"  Mage move success: {mage_move}")
    print(f"  Game AP after: {game.current_player_ap}")
    print(f"  Mage acted: {mage.has_acted_this_turn}")
    
    # Test mage second action (should fail)
    print(f"\n🔥 Mage attempts Fireball:")
    fireball_success = mage.use_ability(2, (3, 6), game)
    print(f"  Fireball success: {fireball_success}")
    print(f"  Reason: {'Already acted this turn' if not fireball_success else 'Unexpected success'}")
    
    # Verify one action per turn is enforced
    one_action_enforced = (move_success and not attack_success and 
                          mage_move and not fireball_success)
    
    if one_action_enforced:
        print("✅ One action per turn ENFORCED correctly!")
    else:
        print("❌ One action per turn NOT enforced!")
        return False
    
    # Test 2: Configuration Integration
    print(f"\n📋 TEST 2: Configuration Integration")
    print("-" * 35)
    
    # Check if configuration was applied
    print(f"Warrior stats:")
    print(f"  HP: {warrior.health}/{warrior.max_health}")
    print(f"  Move AP cost: {warrior.abilities[0].ap_cost}")
    print(f"  Attack AP cost: {warrior.abilities[1].ap_cost}")
    
    print(f"\nMage stats:")
    print(f"  HP: {mage.health}/{mage.max_health}")
    print(f"  Move AP cost: {mage.abilities[0].ap_cost}")
    print(f"  Fireball AP cost: {mage.abilities[2].ap_cost}")
    
    # Test 3: Configuration Changes
    print(f"\n📋 TEST 3: Configuration Changes")
    print("-" * 32)
    
    # Create new config menu and modify values
    from menu_screens.new_config_menu import NewConfigMenu
    screen = pygame.display.set_mode((800, 600))
    clock = pygame.time.Clock()
    
    config_menu = NewConfigMenu(screen, clock)
    
    # Modify warrior HP
    original_warrior_hp = config_menu.class_data["Warrior"]["hp"]
    config_menu.class_data["Warrior"]["hp"] = 15
    
    # Modify fireball AP cost
    original_fireball_ap = config_menu.ability_data["Mage"]["Fireball"]["ap_cost"]
    config_menu.ability_data["Mage"]["Fireball"]["ap_cost"] = 5
    
    print(f"Modified configuration:")
    print(f"  Warrior HP: {original_warrior_hp} → {config_menu.class_data['Warrior']['hp']}")
    print(f"  Fireball AP: {original_fireball_ap} → {config_menu.ability_data['Mage']['Fireball']['ap_cost']}")
    
    # Save configuration
    config_menu._save_configuration()
    
    # Create new units to test configuration
    print(f"\n📋 Creating new units with updated config:")
    
    # Reload configuration
    from config_loader import reload_configuration
    reload_configuration()
    
    # Create new units
    new_warrior = Warrior(1)
    new_mage = Mage(1)
    
    print(f"New Warrior stats:")
    print(f"  HP: {new_warrior.health}/{new_warrior.max_health}")
    
    print(f"New Mage stats:")
    print(f"  Fireball AP cost: {new_mage.abilities[2].ap_cost}")
    
    # Verify configuration was applied
    config_applied = (new_warrior.max_health == 15 and 
                     new_mage.abilities[2].ap_cost == 5)
    
    if config_applied:
        print("✅ Configuration changes APPLIED correctly!")
    else:
        print("❌ Configuration changes NOT applied!")
        print(f"  Expected Warrior HP: 15, Got: {new_warrior.max_health}")
        print(f"  Expected Fireball AP: 5, Got: {new_mage.abilities[2].ap_cost}")
        return False
    
    pygame.quit()
    
    print(f"\n" + "=" * 42)
    print("🎯 COMPLETE SYSTEM TEST SUMMARY")
    print("-" * 32)
    print("✅ One action per turn enforced")
    print("✅ Global AP system working")
    print("✅ Configuration integration working")
    print("✅ Real-time config changes applied")
    
    print(f"\n🎮 SYSTEM READY FOR GAMEPLAY!")
    print("   • Units can only do ONE action per turn")
    print("   • Configuration changes affect new units")
    print("   • Balance sliders work in real-time")
    
    return True

if __name__ == "__main__":
    success = test_complete_system()
    if success:
        print(f"\n🎉 ALL TESTS PASSED!")
        print("The complete system is working correctly!")
    else:
        print(f"\n❌ TESTS FAILED!")
        print("Check the errors above.")
    
    sys.exit(0 if success else 1)

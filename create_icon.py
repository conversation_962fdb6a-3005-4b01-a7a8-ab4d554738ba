import pygame
import os

def create_icon():
    """Create a simple game icon"""
    # Initialize pygame
    pygame.init()
    
    # Create a surface for the icon
    icon_size = 256
    icon = pygame.Surface((icon_size, icon_size), pygame.SRCALPHA)
    
    # Colors
    DARK_BLUE = (0, 80, 160)
    RED = (200, 60, 60)
    GOLD = (255, 215, 0)
    
    # Draw a chess board pattern in the background
    cell_size = icon_size // 8
    for row in range(8):
        for col in range(8):
            color = (60, 60, 70) if (row + col) % 2 == 0 else (150, 150, 160)
            pygame.draw.rect(icon, color, (col * cell_size, row * cell_size, cell_size, cell_size))
    
    # Draw a border
    pygame.draw.rect(icon, GOLD, (0, 0, icon_size, icon_size), 8)
    
    # Draw a blue knight piece
    knight_points = [
        (icon_size//4, icon_size*3//4),
        (icon_size//2, icon_size//3),
        (icon_size*3//4, icon_size//2),
        (icon_size*3//4, icon_size*3//4)
    ]
    pygame.draw.polygon(icon, DARK_BLUE, knight_points)
    pygame.draw.polygon(icon, GOLD, knight_points, 3)
    
    # Draw a red mage staff
    pygame.draw.line(icon, RED, (icon_size//3, icon_size*2//3), 
                    (icon_size*2//3, icon_size//3), 6)
    pygame.draw.circle(icon, GOLD, (icon_size*2//3, icon_size//3), icon_size//12)
    
    # Save as PNG first
    pygame.image.save(icon, "game_icon.png")
    print(f"Created game_icon.png")
    
    # Try to convert to ICO if PIL is available
    try:
        from PIL import Image
        img = Image.open("game_icon.png")
        img.save("icon.ico", sizes=[(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)])
        print(f"Created icon.ico")
    except ImportError:
        print("PIL not installed. Only PNG icon created.")
        print("To create ICO file, install Pillow: pip install pillow")

if __name__ == "__main__":
    create_icon()

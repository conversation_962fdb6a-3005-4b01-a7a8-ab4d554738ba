#!/usr/bin/env python3
"""
Test script for the fixed Hunter abilities: Triple Shot and Spread Shot
"""

import pygame
from game_state import Game
from units.hunter import Hunter
from units.warrior import Warrior
from units.pawn import Pawn

def test_hunter_abilities():
    """Test Triple Shot and Spread Shot abilities"""
    pygame.init()
    
    print("🏹 HUNTER ABILITIES TEST 🏹")
    print("=" * 50)
    
    # Test 1: Triple Shot
    print("\n📋 TEST 1: Triple Shot - 3 Arrows in Same Direction")
    print("-" * 45)
    
    game = Game()
    hunter = Hunter(1)
    enemies = [Warrior(2) for _ in range(3)]
    
    # Set up positions in a diagonal line
    hunter.position = (4, 4)
    enemy_positions = [(3, 5), (2, 6), (1, 7)]  # Diagonal line NE from hunter
    
    hunter.board = game.board
    game.board.units[(4, 4)] = hunter
    
    for i, enemy in enumerate(enemies):
        enemy.position = enemy_positions[i]
        game.board.units[enemy_positions[i]] = enemy
        print(f"Enemy {i+1} ({enemy.name}) at {enemy.position} - HP: {enemy.health}")
    
    print(f"\nHunter at: {hunter.position}")
    print(f"Enemies in diagonal line: {enemy_positions}")
    
    # Get Triple Shot targets
    targets = hunter.get_ability_targets(3, game.board)  # Triple Shot is index 3
    print(f"Available Triple Shot targets: {targets}")
    
    # Execute Triple Shot towards NE direction
    target_direction = (3, 5)  # First enemy position to define direction
    print(f"\n🎯 Executing Triple Shot towards {target_direction}...")
    result = hunter.use_ability(3, target_direction, game)
    print(f"✅ Triple Shot result: {result}")
    
    # Check enemy health after Triple Shot
    print(f"\nEnemy health after Triple Shot:")
    for i, pos in enumerate(enemy_positions):
        if pos in game.board.units:
            enemy = game.board.units[pos]
            print(f"  Enemy {i+1} at {pos}: {enemy.health} HP ({'alive' if enemy.is_alive() else 'dead'})")
        else:
            print(f"  Enemy {i+1} at {pos}: REMOVED (killed)")
    
    # Test 2: Spread Shot
    print("\n\n📋 TEST 2: Spread Shot - Penetrating Shot")
    print("-" * 40)
    
    game2 = Game()
    hunter2 = Hunter(1)
    # Use Pawns (lower HP) to test penetration better
    enemies2 = [Pawn(2) for _ in range(4)]
    
    # Set up positions in a diagonal line
    hunter2.position = (4, 4)
    enemy_positions2 = [(3, 3), (2, 2), (1, 1), (0, 0)]  # Diagonal line NW from hunter
    
    hunter2.board = game2.board
    game2.board.units[(4, 4)] = hunter2
    
    for i, enemy in enumerate(enemies2):
        enemy.position = enemy_positions2[i]
        game2.board.units[enemy_positions2[i]] = enemy
        print(f"Enemy {i+1} ({enemy.name}) at {enemy.position} - HP: {enemy.health}")
    
    print(f"\nHunter at: {hunter2.position}")
    print(f"Enemies in diagonal line: {enemy_positions2}")
    
    # Get Spread Shot targets
    targets2 = hunter2.get_ability_targets(5, game2.board)  # Spread Shot is index 5
    print(f"Available Spread Shot targets: {targets2}")
    
    # Execute Spread Shot towards NW direction
    target_direction2 = (3, 3)  # First enemy position to define direction
    print(f"\n🎯 Executing Spread Shot towards {target_direction2}...")
    result2 = hunter2.use_ability(5, target_direction2, game2)
    print(f"✅ Spread Shot result: {result2}")
    
    # Check enemy health after Spread Shot
    print(f"\nEnemy health after Spread Shot:")
    for i, pos in enumerate(enemy_positions2):
        if pos in game2.board.units:
            enemy = game2.board.units[pos]
            print(f"  Enemy {i+1} at {pos}: {enemy.health} HP ({'alive' if enemy.is_alive() else 'dead'})")
        else:
            print(f"  Enemy {i+1} at {pos}: REMOVED (killed)")
    
    # Test 3: Hunter Movement (verify it's fixed)
    print("\n\n📋 TEST 3: Hunter Movement - No Jumping")
    print("-" * 35)
    
    game3 = Game()
    hunter3 = Hunter(1)
    blocker = Warrior(2)
    
    hunter3.position = (4, 4)
    blocker.position = (5, 5)  # Diagonal from hunter
    
    hunter3.board = game3.board
    game3.board.units[(4, 4)] = hunter3
    game3.board.units[(5, 5)] = blocker
    
    moves = hunter3.get_valid_moves(game3.board)
    print(f"Hunter at: {hunter3.position}")
    print(f"Blocker at: {blocker.position}")
    print(f"Valid moves: {moves}")
    print(f"✅ Cannot jump to (6,6): {(6, 6) not in moves}")
    print(f"✅ Can move to adjacent diagonals: {len(moves) == 3}")
    
    print("\n" + "=" * 50)
    print("🎉 HUNTER ABILITIES TESTS COMPLETED!")
    print("\n✅ Features Tested:")
    print("  • Triple Shot: 3 arrows in same direction")
    print("  • Spread Shot: Penetrating shot hits up to 3 enemies")
    print("  • Hunter Movement: No jumping over entities")
    print("  • Diagonal targeting: Both abilities use diagonal directions")
    
    print("\n🎮 Hunter abilities are now working correctly!")

if __name__ == "__main__":
    test_hunter_abilities()

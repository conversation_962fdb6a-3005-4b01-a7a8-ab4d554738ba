#!/usr/bin/env python3

import pygame
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state import Game
from units.warrior import Warrior
from units.rogue import Rogue
from units.mage import Mage
from units.cleric import Cleric
from units.hunter import <PERSON>

def test_all_classes():
    """Test all unit classes to see which ones work"""
    print("🔍 TESTING ALL UNIT CLASSES")
    print("=" * 40)
    
    # Initialize pygame
    pygame.init()
    
    # Test each class
    classes_to_test = [
        ("Warrior", Warrior),
        ("Rogue", Rogue),
        ("Mage", Mage),
        ("<PERSON><PERSON><PERSON>", <PERSON><PERSON><PERSON>),
        ("<PERSON>", <PERSON>)
    ]
    
    results = {}
    
    for class_name, class_type in classes_to_test:
        print(f"\n📋 TESTING {class_name.upper()}")
        print("-" * 30)
        
        try:
            # Create a game instance
            game = Game()
            
            # Create units
            unit = class_type(player_id=1)
            target = Warrior(player_id=2)  # Use Warrior as target
            
            game.board.add_unit(unit, 4, 4)
            game.board.add_unit(target, 4, 5)  # Adjacent for attack
            
            # Set up game state
            game.current_player = 1
            game.current_player_ap = 10
            game.units_acted_this_turn = set()
            
            print(f"{class_name} at {unit.position}")
            print(f"Target at {target.position}")
            print(f"{class_name} HP: {unit.health}/{unit.max_health}")
            print(f"Target HP: {target.health}/{target.max_health}")
            print(f"Game AP: {game.current_player_ap}")
            print(f"{class_name} abilities: {[a.name for a in unit.abilities]}")
            
            # Test Basic Attack
            attack_idx = 1  # Basic Attack
            if len(unit.abilities) > attack_idx:
                print(f"Basic Attack AP cost: {unit.abilities[attack_idx].ap_cost}")
                
                success = unit.use_ability(attack_idx, target.position, game)
                print(f"Basic Attack success: {success}")
                print(f"Target HP after attack: {target.health}/{target.max_health}")
                print(f"Game AP after attack: {game.current_player_ap}")
                
                results[class_name] = {
                    "basic_attack_works": success,
                    "damage_dealt": target.max_health - target.health,
                    "ap_deducted": 10 - game.current_player_ap,
                    "abilities_count": len(unit.abilities)
                }
            else:
                print(f"❌ {class_name} has no Basic Attack ability!")
                results[class_name] = {
                    "basic_attack_works": False,
                    "damage_dealt": 0,
                    "ap_deducted": 0,
                    "abilities_count": len(unit.abilities)
                }
                
        except Exception as e:
            print(f"❌ {class_name} FAILED: {e}")
            results[class_name] = {
                "basic_attack_works": False,
                "damage_dealt": 0,
                "ap_deducted": 0,
                "abilities_count": 0,
                "error": str(e)
            }
    
    # Summary
    print(f"\n" + "=" * 40)
    print("🎯 SUMMARY OF ALL CLASSES")
    print("-" * 40)
    
    working_classes = []
    broken_classes = []
    
    for class_name, result in results.items():
        if result["basic_attack_works"]:
            working_classes.append(class_name)
            print(f"✅ {class_name}: Attack works, {result['damage_dealt']} damage, {result['ap_deducted']} AP used")
        else:
            broken_classes.append(class_name)
            error_msg = result.get("error", "Basic attack failed")
            print(f"❌ {class_name}: {error_msg}")
    
    print(f"\n📊 RESULTS:")
    print(f"✅ Working: {len(working_classes)}/{len(classes_to_test)} - {working_classes}")
    print(f"❌ Broken: {len(broken_classes)}/{len(classes_to_test)} - {broken_classes}")
    
    return len(working_classes) == len(classes_to_test)

if __name__ == "__main__":
    success = test_all_classes()
    if success:
        print("\n🎉 ALL CLASSES WORKING!")
    else:
        print("\n⚠️ SOME CLASSES NEED FIXING!")

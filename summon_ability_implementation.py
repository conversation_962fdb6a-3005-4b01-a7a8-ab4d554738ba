#!/usr/bin/env python3
"""
Implementation of the Summon ability for classes that can summon Pawn units.
This creates a universal summon system that can be added to any class.
"""

from units_core import Ability
from units.pawn import Pawn
from game_config import GAME_CONFIG

class SummonAbility(Ability):
    """
    Summon ability that creates a Pawn unit at target location.
    Can be added to any class that should have summoning capabilities.
    """
    
    def __init__(self, owner, ap_cost=4, cooldown=3):
        super().__init__(
            name="Summon",
            ap_cost=ap_cost,
            description="Summon a Pawn ally at target empty position",
            cooldown=cooldown,
            damage=0,  # Summon doesn't deal damage
            owner=owner
        )
    
    def execute(self, target_pos, game=None):
        """Execute the summon ability"""
        if not game or not game.board:
            print("Error: Game and board required for summoning")
            return False
        
        # Check if target position is valid and empty
        if target_pos not in game.board.units:
            # Create a new Pawn unit
            summoned_pawn = Pawn(self.owner.player_id)
            summoned_pawn.position = target_pos
            
            # Add the pawn to the board
            game.board.add_unit(summoned_pawn, target_pos[0], target_pos[1])
            
            print(f"{self.owner.name} summons a Pawn at {target_pos}")
            return True
        else:
            print(f"Cannot summon at {target_pos}: position occupied")
            return False

def add_summon_ability_to_class(unit_class, ap_cost=4, cooldown=3):
    """
    Helper function to add summon ability to a class.
    
    Args:
        unit_class: The class to add summon ability to
        ap_cost: AP cost for the summon ability (default: 4)
        cooldown: Cooldown for the summon ability (default: 3)
    """
    
    # Store original __init__ method
    original_init = unit_class.__init__
    
    def new_init(self, *args, **kwargs):
        # Call original __init__
        original_init(self, *args, **kwargs)
        
        # Add summon ability to the abilities list
        summon_ability = SummonAbility(self, ap_cost, cooldown)
        self.abilities.append(summon_ability)
    
    # Replace __init__ method
    unit_class.__init__ = new_init
    
    # Add method to get summon targets
    def get_summon_targets(self, board):
        """Get valid positions for summoning"""
        valid_positions = []
        row, col = self.position
        
        # Check all positions within summoning range (adjacent tiles)
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:  # Skip current position
                    continue
                
                new_row, new_col = row + dr, col + dc
                
                # Check if position is on board and empty
                if (0 <= new_row < 8 and 0 <= new_col < 8 and  # Assuming 8x8 board
                    (new_row, new_col) not in board.units):
                    valid_positions.append((new_row, new_col))
        
        return valid_positions
    
    # Add the targeting method to the class
    unit_class.get_summon_targets = get_summon_targets
    
    # Modify get_ability_targets to handle summon ability
    original_get_ability_targets = getattr(unit_class, 'get_ability_targets', None)
    
    def new_get_ability_targets(self, ability_idx, board):
        if ability_idx < len(self.abilities) and self.abilities[ability_idx].name == "Summon":
            return self.get_summon_targets(board)
        elif original_get_ability_targets:
            return original_get_ability_targets(self, ability_idx, board)
        else:
            return []
    
    unit_class.get_ability_targets = new_get_ability_targets

# Example usage - add summon ability to specific classes
def setup_summon_abilities():
    """
    Setup summon abilities for classes that should have them.
    Call this after importing all unit classes.
    """
    from units.mage import Mage
    from units.cleric import Cleric
    from units.king import King
    from units.warlock import Warlock
    from units.druid import Druid
    
    # Add summon ability to classes that should have it
    # Mage: Summon magical constructs
    add_summon_ability_to_class(Mage, ap_cost=4, cooldown=3)
    
    # Cleric: Summon blessed allies
    add_summon_ability_to_class(Cleric, ap_cost=3, cooldown=2)
    
    # King: Summon loyal subjects
    add_summon_ability_to_class(King, ap_cost=5, cooldown=4)
    
    # Warlock: Summon dark minions
    add_summon_ability_to_class(Warlock, ap_cost=4, cooldown=3)
    
    # Druid: Summon nature spirits
    add_summon_ability_to_class(Druid, ap_cost=3, cooldown=2)
    
    print("✅ Summon abilities added to: Mage, Cleric, King, Warlock, Druid")

if __name__ == "__main__":
    print("🔮 SUMMON ABILITY IMPLEMENTATION")
    print("=" * 35)
    print()
    print("This module provides:")
    print("• SummonAbility class - Creates Pawn units")
    print("• add_summon_ability_to_class() - Adds summon to any class")
    print("• setup_summon_abilities() - Adds to specific classes")
    print()
    print("Usage:")
    print("1. Import this module")
    print("2. Call setup_summon_abilities() after importing unit classes")
    print("3. Classes will have Summon ability that creates Pawns")
    print()
    print("Classes that get summon ability:")
    print("• Mage (4 AP, 3 cooldown)")
    print("• Cleric (3 AP, 2 cooldown)")
    print("• King (5 AP, 4 cooldown)")
    print("• Warlock (4 AP, 3 cooldown)")
    print("• Druid (3 AP, 2 cooldown)")
    print()
    print("To use: Call setup_summon_abilities() in your main game initialization")

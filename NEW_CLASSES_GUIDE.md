# 🎭 NEW CLASSES GUIDE

## **Four New Classes Added to Your Tactical Strategy Game**

### **✅ SUCCESSFULLY IMPLEMENTED:**
- **🌑 Warlock** - Dark Magic Specialist
- **⚔️ Paladin** - Holy Warrior
- **🌿 Druid** - Nature Magic Specialist  
- **🎵 Bard** - Support and Inspiration Specialist

---

## **🌑 WARLOCK - Dark Magic Specialist**

### **Core Identity:**
- **Role:** Damage over time specialist, life manipulation
- **Theme:** Dark magic, sacrifice, curses
- **Playstyle:** High risk/high reward, tactical positioning

### **Movement:**
- **Pattern:** Orthogonal only (N, S, E, W)
- **Range:** 2 tiles in chosen direction
- **Special:** Cannot jump over entities

### **Abilities (8 total):**
1. **Move** - Orthogonal movement (2 tiles)
2. **Basic Attack** - Ranged dark bolt (3 tile range)
3. **Life Drain** - Steal 2 HP from target, heal self
4. **Curse** - Target takes +1 damage for 3 turns
5. **Fear** - Force target to move away from Warlock
6. **Dark Pact** - Sacrifice 2 HP for 3 AP (once per turn)
7. **Soul Burn** - Target takes 1 damage per turn for 3 turns
8. **Shadow Bolt** - High damage dark magic attack

### **Tactical Role:**
- Battlefield control through debuffs
- Sustain through life stealing
- Resource management (health for power)

---

## **⚔️ PALADIN - Holy Warrior**

### **Core Identity:**
- **Role:** Tank/Support hybrid, ally protection
- **Theme:** Holy magic, protection, righteousness
- **Playstyle:** Defensive support with burst potential

### **Movement:**
- **Pattern:** Orthogonal only (N, S, E, W)
- **Range:** 2 tiles in chosen direction
- **Special:** Cannot jump over entities

### **Abilities (8 total):**
1. **Move** - Orthogonal movement (2 tiles)
2. **Basic Attack** - Melee attack with holy damage
3. **Lay on Hands** - Heal ally for 4 HP
4. **Divine Smite** - Extra damage attack with stun chance
5. **Blessing** - Grant ally damage resistance for 3 turns
6. **Consecrate** - Create holy ground that heals allies
7. **Turn Undead** - Fear effect on cursed enemies
8. **Divine Shield** - Become immune to damage for 1 turn

### **Tactical Role:**
- Primary tank and protector
- Anti-curse/debuff specialist
- Emergency healing support

---

## **🌿 DRUID - Nature Magic Specialist**

### **Core Identity:**
- **Role:** Versatile support/damage, environmental control
- **Theme:** Nature magic, shapeshifting, balance
- **Playstyle:** Adaptable, area control, transformation

### **Movement:**
- **Pattern:** All directions (8-directional)
- **Range:** 2 tiles in any direction
- **Special:** High mobility for positioning

### **Abilities (8 total):**
1. **Move** - 8-directional movement (2 tiles)
2. **Basic Attack** - Ranged nature bolt (3 tile range)
3. **Wild Shape** - Transform for enhanced abilities (3 turns)
4. **Entangle** - Root target in place for 2 turns
5. **Healing Spring** - Create healing area (4 turns)
6. **Thorn Barrier** - Create damaging wall
7. **Call Lightning** - Area lightning damage (3x3)
8. **Nature's Wrath** - Powerful nature attack

### **Tactical Role:**
- Battlefield control specialist
- Environmental manipulation
- Flexible support/damage hybrid

---

## **🎵 BARD - Support and Inspiration Specialist**

### **Core Identity:**
- **Role:** Primary support, force multiplier
- **Theme:** Music, inspiration, knowledge
- **Playstyle:** Team enabler, information gathering

### **Movement:**
- **Pattern:** All directions (8-directional)
- **Range:** 3 tiles in any direction
- **Special:** Highest mobility for optimal positioning

### **Abilities (8 total):**
1. **Move** - 8-directional movement (3 tiles)
2. **Basic Attack** - Weak ranged sonic attack (4 tile range)
3. **Inspire** - Grant ally +2 AP and +1 damage
4. **Song of Healing** - Heal all nearby allies
5. **Discordant Note** - Confuse and damage enemy
6. **Bardic Knowledge** - Reveal enemy information
7. **Mass Inspiration** - Buff all allies
8. **Shatter** - High damage sonic attack

### **Tactical Role:**
- Team support and buffing
- Information gathering
- AP manipulation and resource management

---

## **🎮 INTEGRATION WITH EXISTING SYSTEMS**

### **✅ Fully Compatible:**
- **Global AP System** - All classes follow one action per turn
- **Configuration System** - All abilities configurable via sliders
- **Status Effects** - Support for new effects (cursed, blessed, etc.)
- **Damage System** - All abilities have configurable damage
- **Healing System** - Healing abilities have configurable amounts

### **✅ Configuration Available:**
- **Class HP:** 5-20 for all new classes
- **Movement Range:** 1-5 tiles for all new classes
- **Ability Damage:** 0-10 for all abilities
- **Healing Amounts:** 1-5 for healing spells
- **AP Costs:** 0-8 for all abilities
- **Cooldowns:** 0-5 turns for all abilities

---

## **🚀 HOW TO USE THE NEW CLASSES**

### **1. Access in Game:**
- New classes are automatically available
- Use existing unit selection system
- All classes work with current game modes

### **2. Configure Balance:**
1. Launch game: `python main_menu.py`
2. Go to Options → Game Configuration
3. Select new class from dropdown
4. Adjust HP, movement, and abilities
5. Save changes

### **3. Strategic Combinations:**
- **Warlock + Paladin:** Dark/Light synergy
- **Druid + Bard:** Nature support combo
- **All Four:** Ultimate tactical variety

---

## **🎯 DESIGN PHILOSOPHY**

### **Unique Movement Patterns:**
- **Warlock/Paladin:** Orthogonal (tactical positioning)
- **Druid:** 8-directional (versatile positioning)
- **Bard:** 8-directional + extended range (optimal support positioning)

### **Distinct Roles:**
- **Warlock:** Risk/reward damage dealer
- **Paladin:** Defensive support tank
- **Druid:** Flexible battlefield controller
- **Bard:** Team enabler and multiplier

### **Balanced Complexity:**
- Each class has 8 abilities (consistent with existing classes)
- Mix of offensive, defensive, and utility abilities
- Unique mechanics that don't overlap with existing classes

---

## **🔧 TECHNICAL IMPLEMENTATION**

### **Files Added:**
- `units/warlock.py` - Warlock class implementation
- `units/paladin.py` - Paladin class implementation
- `units/druid.py` - Druid class implementation
- `units/bard.py` - Bard class implementation

### **Files Modified:**
- `menu_screens/new_config_menu.py` - Added new classes to configuration
- Configuration system automatically supports new classes

### **Features Implemented:**
- ✅ Complete class implementations
- ✅ Unique movement patterns
- ✅ 8 abilities per class
- ✅ Global AP system integration
- ✅ Configuration system integration
- ✅ Damage and healing configurability
- ✅ Status effect support
- ✅ Comprehensive testing

---

## **🎉 READY TO PLAY!**

Your tactical strategy game now has **10 total classes:**

**Original Classes:**
- Warrior, Mage, Hunter, Rogue, Cleric, King

**New Classes:**
- **Warlock, Paladin, Druid, Bard**

**All classes are fully functional, configurable, and ready for strategic gameplay!**

Launch the game and experiment with the new tactical possibilities! 🚀

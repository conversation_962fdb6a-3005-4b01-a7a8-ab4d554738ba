#!/usr/bin/env python3
"""
Visual demo of the new ability configuration system
Shows how the UI would look and work
"""

import pygame
import sys
from ability_config_system import AbilityConfigSystem

def run_ability_config_demo():
    """Run a visual demo of the ability configuration system"""
    pygame.init()
    
    # Screen setup
    screen = pygame.display.set_mode((1000, 700))
    pygame.display.set_caption("New Ability Configuration System Demo")
    clock = pygame.time.Clock()
    
    # Initialize system
    config_system = AbilityConfigSystem()
    
    # Demo state
    selected_unit_class = "Warrior"
    unit_classes = ["Warrior", "Mage", "Cleric", "Rogue", "Hunter"]
    class_buttons = {}
    
    # Create class selection buttons
    for i, unit_class in enumerate(unit_classes):
        class_buttons[unit_class] = pygame.Rect(50 + i * 120, 50, 100, 40)
    
    font = pygame.font.Font(None, 36)
    small_font = pygame.font.Font(None, 24)
    
    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    mouse_pos = event.pos
                    
                    # Check class selection buttons
                    for unit_class, button_rect in class_buttons.items():
                        if button_rect.collidepoint(mouse_pos):
                            selected_unit_class = unit_class
                            config_system.selected_ability = None  # Reset selection
                            break
                    
                    # Check ability configuration UI
                    config_system.handle_click(mouse_pos, selected_unit_class, 140)
        
        # Clear screen
        screen.fill((30, 30, 30))
        
        # Title
        title_text = font.render("New Ability Configuration System", True, (255, 255, 255))
        screen.blit(title_text, (50, 10))
        
        # Class selection buttons
        for unit_class, button_rect in class_buttons.items():
            if unit_class == selected_unit_class:
                color = (100, 150, 200)
            else:
                color = (70, 70, 70)
            
            pygame.draw.rect(screen, color, button_rect)
            pygame.draw.rect(screen, (255, 255, 255), button_rect, 2)
            
            text = small_font.render(unit_class, True, (255, 255, 255))
            text_rect = text.get_rect(center=button_rect.center)
            screen.blit(text, text_rect)
        
        # Render ability configuration UI
        config_system.render_ui(screen, 50, 100, selected_unit_class)

        # Instructions (moved to avoid overlap)
        instructions = [
            "Instructions:",
            "• Click unit class tabs to switch",
            "• Click 'Class Stats' or 'Abilities' mode",
            "• Click ability buttons to select",
            "• Drag sliders to adjust values",
            "• Values update in real-time",
            "",
            "New Features:",
            "• Class-level HP, Movement, Max AP",
            "• Individual ability configuration",
            "• Damage, AP cost, cooldown sliders",
            "• Save/load configurations",
            "• Better organized layout"
        ]

        instruction_x = 550
        instruction_y = 150
        for instruction in instructions:
            if instruction.startswith("•"):
                color = (200, 200, 200)
            elif instruction.endswith(":"):
                color = (255, 255, 100)
            else:
                color = (255, 255, 255)

            text = small_font.render(instruction, True, color)
            screen.blit(text, (instruction_x, instruction_y))
            instruction_y += 22

        # Show current mode and selection info (moved down)
        info_y = 450
        mode_text = f"Mode: {config_system.config_mode.title()}"
        text = small_font.render(mode_text, True, (100, 200, 255))
        screen.blit(text, (instruction_x, info_y))
        info_y += 25

        if config_system.config_mode == "abilities" and config_system.selected_ability:
            ability = config_system.abilities[config_system.selected_ability]

            info_texts = [
                f"Selected: {ability.name}",
                f"Damage: {ability.damage}",
                f"AP Cost: {ability.ap_cost}",
                f"Cooldown: {ability.cooldown}"
            ]

            for info_text in info_texts:
                text = small_font.render(info_text, True, (100, 255, 100))
                screen.blit(text, (instruction_x, info_y))
                info_y += 22
        elif config_system.config_mode == "class" and selected_unit_class in config_system.classes:
            class_config = config_system.classes[selected_unit_class]

            info_texts = [
                f"Class: {class_config.name}",
                f"HP: {class_config.hp}",
                f"Movement: {class_config.movement_range}",
                f"Max AP: {class_config.max_ap}"
            ]

            for info_text in info_texts:
                text = small_font.render(info_text, True, (255, 150, 100))
                screen.blit(text, (instruction_x, info_y))
                info_y += 22
        
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()

def show_system_comparison():
    """Show comparison between old and new systems"""
    print("🔄 CONFIGURATION SYSTEM COMPARISON")
    print("=" * 45)
    
    print("\n❌ OLD SYSTEM PROBLEMS:")
    print("• Limited to basic stats (HP, Max AP)")
    print("• No individual ability control")
    print("• Hard to find specific settings")
    print("• Not intuitive for ability balancing")
    print("• No damage/cooldown configuration")
    
    print("\n✅ NEW SYSTEM BENEFITS:")
    print("• Individual button for each ability")
    print("• Dedicated sliders for damage, AP cost, cooldown")
    print("• Visual feedback with real-time updates")
    print("• Organized by unit class")
    print("• Easy to find and adjust specific abilities")
    print("• Save/load different configurations")
    
    print("\n🎯 NEW SYSTEM FEATURES:")
    print("• 30+ abilities across all unit classes")
    print("• 3 sliders per ability (damage, AP, cooldown)")
    print("• Smart slider ranges (no invalid values)")
    print("• Visual selection highlighting")
    print("• Configuration persistence")
    
    print("\n🎮 USER EXPERIENCE:")
    print("1. Select unit class (Warrior, Mage, etc.)")
    print("2. Click ability button (Fireball, Charge, etc.)")
    print("3. Adjust sliders for damage, AP cost, cooldown")
    print("4. See changes immediately")
    print("5. Save configuration for later use")
    
    print("\n🚀 IMPLEMENTATION PLAN:")
    print("1. Replace current config system")
    print("2. Integrate with game UI")
    print("3. Connect to actual ability values")
    print("4. Add save/load functionality")
    print("5. Test with real gameplay")

if __name__ == "__main__":
    print("Choose demo mode:")
    print("1. Visual demo (interactive)")
    print("2. System comparison (text)")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        run_ability_config_demo()
    else:
        show_system_comparison()

#!/usr/bin/env python3
"""
Test the configuration fix for JSON structure compatibility.
"""

import pygame
pygame.init()

from core.configuration_manager import get_config_manager
from units.warrior import Warrior
from units.mage import Mage

def test_config_fix():
    print("🔧 TESTING CONFIGURATION FIX")
    print("=" * 40)
    
    # Create a fresh config manager to reload the JSON
    config_manager = get_config_manager()
    
    print("📋 Testing Warrior Configuration:")
    warrior_config = config_manager.get_unit_config("Warrior")
    warrior_health = warrior_config.get("health", "NOT_FOUND")
    print(f"  Configured health: {warrior_health}")
    
    # Create warrior instance
    warrior = Warrior(1)
    print(f"  Actual health: {warrior.health}")
    
    # Check if they match
    if warrior_health == warrior.health:
        print(f"  ✅ Configuration working correctly!")
    else:
        print(f"  ❌ Configuration mismatch!")
    
    print(f"\n📋 Testing Mage Configuration:")
    mage_config = config_manager.get_unit_config("Mage")
    mage_health = mage_config.get("health", "NOT_FOUND")
    print(f"  Configured health: {mage_health}")
    
    # Create mage instance
    mage = Mage(1)
    print(f"  Actual health: {mage.health}")
    
    # Check if they match
    if mage_health == mage.health:
        print(f"  ✅ Configuration working correctly!")
    else:
        print(f"  ❌ Configuration mismatch!")
    
    print(f"\n🎯 Dynamic Config Structure:")
    print(f"  Warrior config: {config_manager.dynamic_config.get('Warrior', 'NOT_FOUND')}")
    print(f"  Mage config: {config_manager.dynamic_config.get('Mage', 'NOT_FOUND')}")
    
    # Test if JSON values are now being used
    if warrior.health == 17:  # JSON value
        print(f"\n🎉 SUCCESS: Using JSON configuration values!")
        print(f"   Health sliders are now working correctly!")
    elif warrior.health == 7:  # Static value
        print(f"\n⚠️  Still using static configuration values")
        print(f"   JSON structure transformation may need adjustment")
    else:
        print(f"\n❓ Unexpected health value: {warrior.health}")

if __name__ == "__main__":
    test_config_fix()

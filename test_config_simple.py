#!/usr/bin/env python3

import pygame
import sys
import os
import json

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state import Game
from units.warrior import Warrior
from units.mage import Mage

def test_config_simple():
    """Simple test to see if configuration changes work"""
    print("🔧 SIMPLE CONFIGURATION TEST")
    print("=" * 40)
    
    # Initialize pygame
    pygame.init()
    
    # Test 1: Create unit with current config
    print("\n📋 TEST 1: Current Configuration")
    warrior1 = Warrior(player_id=1)
    print(f"Warrior HP: {warrior1.max_health}")
    print(f"Warrior Attack Cost: {warrior1.abilities[1].ap_cost}")
    
    # Test 2: Modify JSON config
    print("\n📋 TEST 2: Modifying JSON Configuration")
    
    # Read current config
    with open("game_balance_config.json", 'r') as f:
        config = json.load(f)
    
    # Backup original
    original_warrior_hp = config["class_data"]["Warrior"]["hp"]
    print(f"Original Warrior HP in JSON: {original_warrior_hp}")
    
    # Modify config
    config["class_data"]["Warrior"]["hp"] = 99
    config["ability_data"] = config.get("ability_data", {})
    config["ability_data"]["Warrior"] = {"Attack": {"ap_cost": 9}}
    
    # Save modified config
    with open("game_balance_config.json", 'w') as f:
        json.dump(config, f, indent=2)

    # Reload configuration to pick up changes
    from config_loader import reload_configuration
    reload_configuration()

    print("Modified JSON config: Warrior HP=99, Attack Cost=9")
    print("✅ Configuration reloaded")
    
    # Test 3: Create new unit with modified config
    print("\n📋 TEST 3: New Unit with Modified Config")
    warrior2 = Warrior(player_id=1)
    print(f"New Warrior HP: {warrior2.max_health}")
    print(f"New Warrior Attack Cost: {warrior2.abilities[1].ap_cost}")
    
    # Test 4: Check if changes applied
    print("\n📋 TEST 4: Verification")
    hp_changed = warrior2.max_health == 99
    cost_changed = warrior2.abilities[1].ap_cost == 9
    
    print(f"HP changed correctly: {'✅' if hp_changed else '❌'} (Expected: 99, Got: {warrior2.max_health})")
    print(f"Cost changed correctly: {'✅' if cost_changed else '❌'} (Expected: 9, Got: {warrior2.abilities[1].ap_cost})")
    
    # Restore original config
    config["class_data"]["Warrior"]["hp"] = original_warrior_hp
    if "ability_data" in config and "Warrior" in config["ability_data"]:
        del config["ability_data"]["Warrior"]
    
    with open("game_balance_config.json", 'w') as f:
        json.dump(config, f, indent=2)
    
    print("✅ Original configuration restored")
    
    success = hp_changed and cost_changed
    return success

if __name__ == "__main__":
    success = test_config_simple()
    if success:
        print("\n🎉 CONFIGURATION SYSTEM WORKING!")
    else:
        print("\n❌ CONFIGURATION SYSTEM BROKEN!")

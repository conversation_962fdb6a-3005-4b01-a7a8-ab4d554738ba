#!/usr/bin/env python3
"""
Test if the UI fix resolves the KeyError for new classes
"""

import pygame
import sys
from game_state import Game

def test_ui_fix():
    """Test that UI buttons are created for all classes"""
    print("🎮 TESTING UI FIX")
    print("=" * 20)
    
    pygame.init()
    
    # Test 1: Check unit buttons creation
    print("📋 TEST 1: Unit Buttons Creation")
    print("-" * 32)
    
    try:
        game = Game()
        
        print("Unit buttons created:")
        for unit_type, button_rect in game.unit_buttons.items():
            print(f"  {unit_type}: {button_rect}")
        
        # Check if all new classes have buttons
        new_classes = ["Warlock", "Paladin", "Druid", "Bard"]
        missing_buttons = []
        
        for class_name in new_classes:
            if class_name not in game.unit_buttons:
                missing_buttons.append(class_name)
        
        if missing_buttons:
            print(f"\n❌ Missing buttons: {missing_buttons}")
            return False
        else:
            print(f"\n✅ All new classes have UI buttons")
        
        # Test 2: Check available units match buttons
        print(f"\n📋 TEST 2: Available Units vs Buttons")
        print("-" * 37)
        
        available_units = set(game.available_units[1].keys())
        button_units = set(game.unit_buttons.keys())
        
        print(f"Available units: {sorted(available_units)}")
        print(f"Button units: {sorted(button_units)}")
        
        missing_from_buttons = available_units - button_units
        extra_buttons = button_units - available_units
        
        if missing_from_buttons:
            print(f"\n❌ Units without buttons: {missing_from_buttons}")
            return False
        
        if extra_buttons:
            print(f"\n⚠️ Extra buttons (not critical): {extra_buttons}")
        
        if not missing_from_buttons:
            print(f"\n✅ All available units have corresponding buttons")
        
        # Test 3: Simulate UI drawing (the part that was failing)
        print(f"\n📋 TEST 3: UI Drawing Simulation")
        print("-" * 33)
        
        try:
            # Simulate the failing code from game_ui.py
            for unit_type, count in game.available_units[game.setup_phase].items():
                button_rect = game.unit_buttons[unit_type]  # This was failing before
                print(f"  {unit_type}: Button at {button_rect}")
            
            print(f"\n✅ UI drawing simulation successful")
            
        except KeyError as e:
            print(f"\n❌ UI drawing still fails: {e}")
            return False
        
        pygame.quit()
        
        print(f"\n" + "=" * 20)
        print("🎯 UI FIX TEST SUMMARY")
        print("-" * 22)
        print("✅ Unit buttons created for all classes")
        print("✅ Available units match button units")
        print("✅ UI drawing simulation successful")
        
        print(f"\n🎉 UI FIX SUCCESSFUL!")
        print("The KeyError should now be resolved!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ui_fix()
    if success:
        print(f"\n🚀 READY TO LAUNCH GAME!")
        print("The new classes should now be visible!")
    else:
        print(f"\n🔧 STILL NEEDS WORK!")
        print("Check the errors above.")
    
    sys.exit(0 if success else 1)

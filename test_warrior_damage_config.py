#!/usr/bin/env python3

import pygame
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state import Game
from units.warrior import Warrior
from units.rogue import Rogue

def test_warrior_damage_config():
    """Test that Warrior damage configuration is working"""
    print("⚔️ TESTING WARRIOR DAMAGE CONFIGURATION")
    print("=" * 50)
    
    # Initialize pygame
    pygame.init()
    
    try:
        # Create a game instance
        game = Game()
        
        # Create units
        warrior = Warrior(player_id=1)
        target = Rogue(player_id=2)
        
        game.board.add_unit(warrior, 4, 4)
        game.board.add_unit(target, 4, 5)  # Adjacent for attack
        
        # Set up game state
        game.current_player = 1
        game.current_player_ap = 10
        game.units_acted_this_turn = set()
        
        print(f"Warrior at {warrior.position}")
        print(f"Target at {target.position}")
        print(f"Target HP: {target.health}/{target.max_health}")
        print(f"Initial AP: {game.current_player_ap}")
        
        # Test configured damage values
        from config_loader import ConfigLoader
        config_loader = ConfigLoader()
        
        print(f"\n📋 CONFIGURED DAMAGE VALUES:")
        print(f"Basic Attack: {config_loader.get_ability_damage('Warrior', 'Attack')}")
        print(f"Cleave Attack: {config_loader.get_ability_damage('Warrior', 'Cleave Attack')}")
        print(f"Shield Bash: {config_loader.get_ability_damage('Warrior', 'Shield Bash')}")
        print(f"Charge: {config_loader.get_ability_damage('Warrior', 'Charge')}")
        print(f"Riposte: {config_loader.get_ability_damage('Warrior', 'Riposte')}")
        
        # Test 1: Basic Attack
        print(f"\n⚔️ TEST 1: Basic Attack")
        initial_hp = target.health
        attack_success = warrior.use_ability(1, target.position, game)
        damage_dealt = initial_hp - target.health
        
        print(f"Attack success: {attack_success}")
        print(f"Damage dealt: {damage_dealt}")
        print(f"Expected damage: {config_loader.get_ability_damage('Warrior', 'Attack')}")
        
        if abs(damage_dealt - config_loader.get_ability_damage('Warrior', 'Attack')) < 0.01:
            print("✅ Basic Attack damage configuration WORKING!")
        else:
            print("❌ Basic Attack damage configuration BROKEN!")
        
        # Reset for next test
        target.health = target.max_health
        game.current_player_ap = 10
        game.units_acted_this_turn = set()
        warrior.has_acted_this_turn = False
        
        # Test 2: Shield Bash
        print(f"\n🛡️ TEST 2: Shield Bash")
        shield_bash_idx = None
        for i, ability in enumerate(warrior.abilities):
            if ability.name == "Shield Bash":
                shield_bash_idx = i
                break
        
        if shield_bash_idx is not None:
            initial_hp = target.health
            bash_success = warrior.use_ability(shield_bash_idx, target.position, game)
            damage_dealt = initial_hp - target.health
            
            print(f"Shield Bash success: {bash_success}")
            print(f"Damage dealt: {damage_dealt}")
            print(f"Expected damage: {config_loader.get_ability_damage('Warrior', 'Shield Bash')}")
            
            if abs(damage_dealt - config_loader.get_ability_damage('Warrior', 'Shield Bash')) < 0.01:
                print("✅ Shield Bash damage configuration WORKING!")
            else:
                print("❌ Shield Bash damage configuration BROKEN!")
        else:
            print("❌ Shield Bash ability not found!")
        
        # Reset for next test
        target.health = target.max_health
        game.current_player_ap = 10
        game.units_acted_this_turn = set()
        warrior.has_acted_this_turn = False
        
        # Test 3: Cleave Attack
        print(f"\n⚔️ TEST 3: Cleave Attack")
        cleave_idx = None
        for i, ability in enumerate(warrior.abilities):
            if ability.name == "Cleave Attack":
                cleave_idx = i
                break
        
        if cleave_idx is not None:
            initial_hp = target.health
            cleave_success = warrior.use_ability(cleave_idx, target.position, game)
            damage_dealt = initial_hp - target.health
            
            print(f"Cleave Attack success: {cleave_success}")
            print(f"Damage dealt: {damage_dealt}")
            print(f"Expected damage: {config_loader.get_ability_damage('Warrior', 'Cleave Attack')}")
            
            if abs(damage_dealt - config_loader.get_ability_damage('Warrior', 'Cleave Attack')) < 0.01:
                print("✅ Cleave Attack damage configuration WORKING!")
            else:
                print("❌ Cleave Attack damage configuration BROKEN!")
        else:
            print("❌ Cleave Attack ability not found!")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_warrior_damage_config()
    if success:
        print("\n🎉 WARRIOR DAMAGE CONFIGURATION TEST COMPLETE!")
    else:
        print("\n💥 WARRIOR DAMAGE CONFIGURATION TEST FAILED!")

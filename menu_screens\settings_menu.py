import pygame
import sys
from menu_screens.button import Button # Import Button

# Import constants from main_menu.py or a future central constants module
# For now, assume these might be passed or accessed via a config object if main_menu is further refactored
# Temporarily, these might need to be duplicated or imported if main_menu still holds them.

# Mocking necessary constants that would typically come from main_menu.py or a settings file
WINDOW_WIDTH = 1280 # Example, should be dynamic or from a shared source
WINDOW_HEIGHT = 720 # Example
FPS = 60
RESOLUTIONS = [
    (800, 600),
    (1024, 768),
    (1280, 720),
    (1366, 768),
    (1600, 900),
    (1920, 1080)
]
DARK_GRAY = (30, 30, 30)
LIGHT_GRAY = (200, 200, 200)
# HIGHLIGHT_COLOR and BUTTON_COLOR are now part of the Button class logic

class Settings:
    def __init__(self, screen, clock, main_menu_ref):
        self.screen = screen
        self.clock = clock
        self.main_menu_ref = main_menu_ref # To access/update global settings like WINDOW_WIDTH/HEIGHT
        self.running = True
        
        self.title_font = pygame.font.Font(None, 74)
        self.button_font = pygame.font.Font(None, 48)
        self.option_font = pygame.font.Font(None, 36)
        
        # Current settings - try to read from main_menu_ref or use defaults
        self.current_resolution_idx = RESOLUTIONS.index(main_menu_ref.current_resolution if hasattr(main_menu_ref, 'current_resolution') else (WINDOW_WIDTH, WINDOW_HEIGHT))
        self.fullscreen = main_menu_ref.fullscreen_preference if hasattr(main_menu_ref, 'fullscreen_preference') else False
        
        self._update_buttons()
    
    def _update_buttons(self):
        # Use current screen dimensions for button positioning
        current_screen_width, current_screen_height = self.screen.get_size()

        self.resolution_left = Button(current_screen_width//2 - 250, 250, 50, 50, "<")
        self.resolution_right = Button(current_screen_width//2 + 200, 250, 50, 50, ">")
        
        self.fullscreen_toggle = Button(current_screen_width//2 - 100, 350, 200, 50, f"Fullscreen: {'On' if self.fullscreen else 'Off'}")
        
        self.apply_button = Button(current_screen_width//2 - 150, 450, 300, 60, "Apply Changes")
        self.back_button = Button(current_screen_width//2 - 100, 550, 200, 60, "Back")

    def draw_background(self):
        current_screen_width, current_screen_height = self.screen.get_size()
        for y in range(current_screen_height):
            color_value = int(30 + (y / current_screen_height) * 20)
            pygame.draw.line(self.screen, (color_value, color_value, color_value),
                           (0, y), (current_screen_width, y))
    
    def run(self):
        self.running = True
        self._update_buttons() # Ensure buttons are positioned correctly for current screen

        while self.running:
            current_screen_width, current_screen_height = self.screen.get_size()
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()
                
                if self.resolution_left.handle_event(event):
                    self.current_resolution_idx = max(0, self.current_resolution_idx - 1)
                
                if self.resolution_right.handle_event(event):
                    self.current_resolution_idx = min(len(RESOLUTIONS) - 1, self.current_resolution_idx + 1)
                
                if self.fullscreen_toggle.handle_event(event):
                    self.fullscreen = not self.fullscreen
                    self.fullscreen_toggle.text = f"Fullscreen: {'On' if self.fullscreen else 'Off'}"
                
                if self.apply_button.handle_event(event):
                    new_width, new_height = RESOLUTIONS[self.current_resolution_idx]
                    flags = pygame.FULLSCREEN if self.fullscreen else 0
                    # Update screen in main_menu_ref, which should handle global screen update
                    self.main_menu_ref.update_screen_settings(new_width, new_height, flags)
                    self.screen = self.main_menu_ref.screen # Get the updated screen reference
                    # Update preferences in main_menu_ref
                    self.main_menu_ref.current_resolution = (new_width, new_height)
                    self.main_menu_ref.fullscreen_preference = self.fullscreen
                    self._update_buttons() # Re-position buttons for new resolution
                
                if self.back_button.handle_event(event):
                    self.running = False
            
            self.draw_background()
            
            title_surface = self.title_font.render("SETTINGS", True, LIGHT_GRAY)
            title_rect = title_surface.get_rect(center=(current_screen_width//2, 100))
            self.screen.blit(title_surface, title_rect)
            
            res_text = f"Resolution: {RESOLUTIONS[self.current_resolution_idx][0]}x{RESOLUTIONS[self.current_resolution_idx][1]}"
            res_surface = self.option_font.render(res_text, True, LIGHT_GRAY)
            res_rect = res_surface.get_rect(center=(current_screen_width//2, 250))
            self.screen.blit(res_surface, res_rect)
            
            self.resolution_left.draw(self.screen, self.button_font)
            self.resolution_right.draw(self.screen, self.button_font)
            self.fullscreen_toggle.draw(self.screen, self.option_font)
            self.apply_button.draw(self.screen, self.button_font)
            self.back_button.draw(self.screen, self.button_font)
            
            pygame.display.flip()
            self.clock.tick(FPS) 
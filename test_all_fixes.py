#!/usr/bin/env python3
"""
Test all the fixes: Damage configuration + AP system for all classes
"""

import pygame
import sys
from game_state import Game
from units.warrior import Warrior
from units.mage import Mage
from units.hunter import Hunter
from units.rogue import Rogue
from units.cleric import <PERSON><PERSON><PERSON>

def test_all_fixes():
    """Test that all fixes are working"""
    print("🎮 TESTING ALL FIXES")
    print("=" * 25)
    
    pygame.init()
    
    # Test 1: AP System - One Action Per Turn for All Classes
    print("📋 TEST 1: AP System - All Classes")
    print("-" * 35)
    
    game = Game()
    
    # Create all unit types
    warrior = Warrior(1)
    mage = Mage(1)
    hunter = Hunter(1)
    rogue = Rogue(1)
    cleric = Cleric(1)
    
    units = [warrior, mage, hunter, rogue, cleric]
    
    # Position units
    for i, unit in enumerate(units):
        unit.position = (i, 0)
        game.board.units[(i, 0)] = unit
        unit.board = game.board
    
    # Start turn
    game.start_player_turn(1)
    game.current_player_ap = 10  # Give plenty of AP
    
    print("Testing one action per turn for each class:")
    
    results = []
    for unit in units:
        print(f"\n🔸 Testing {unit.name}:")
        
        # Reset acted flag
        unit.has_acted_this_turn = False
        
        # Try first action (should work)
        first_action = unit.use_ability(0, (unit.position[0], unit.position[1] + 1), game)
        print(f"  First action (Move): {first_action}")
        
        # Try second action (should fail)
        second_action = unit.use_ability(1, (unit.position[0], unit.position[1] + 2), game)
        print(f"  Second action (Attack): {second_action}")
        
        # Check if one action per turn is enforced
        one_action_enforced = first_action and not second_action
        results.append(one_action_enforced)
        
        if one_action_enforced:
            print(f"  ✅ {unit.name}: One action per turn enforced")
        else:
            print(f"  ❌ {unit.name}: Multiple actions allowed!")
    
    # Test 2: Damage Configuration
    print(f"\n📋 TEST 2: Damage Configuration")
    print("-" * 32)
    
    print("Checking damage attributes for all abilities:")
    
    damage_results = []
    for unit in units:
        print(f"\n🔸 {unit.name} abilities:")
        unit_damage_working = True
        
        for i, ability in enumerate(unit.abilities):
            has_damage = hasattr(ability, 'damage')
            damage_value = ability.damage if has_damage else "NO ATTRIBUTE"
            print(f"  {ability.name}: Damage = {damage_value}")
            
            if not has_damage:
                unit_damage_working = False
        
        damage_results.append(unit_damage_working)
        
        if unit_damage_working:
            print(f"  ✅ {unit.name}: All abilities have damage attribute")
        else:
            print(f"  ❌ {unit.name}: Some abilities missing damage attribute")
    
    # Test 3: Configuration Application
    print(f"\n📋 TEST 3: Configuration Application")
    print("-" * 35)
    
    # Test configuration changes
    from menu_screens.new_config_menu import NewConfigMenu
    screen = pygame.display.set_mode((800, 600))
    clock = pygame.time.Clock()
    
    config_menu = NewConfigMenu(screen, clock)
    
    # Modify some values
    config_menu.class_data["Warrior"]["hp"] = 25
    config_menu.ability_data["Warrior"]["Shield Bash"]["damage"] = 8
    config_menu.ability_data["Mage"]["Fireball"]["damage"] = 6
    config_menu.ability_data["Hunter"]["Multishot"]["damage"] = 4
    
    # Save configuration
    config_menu._save_configuration()
    
    # Reload and create new units
    from config_loader import reload_configuration
    reload_configuration()
    
    new_warrior = Warrior(1)
    new_mage = Mage(1)
    new_hunter = Hunter(1)
    
    print("Configuration changes applied:")
    print(f"  Warrior HP: {new_warrior.max_health} (expected: 25)")
    print(f"  Shield Bash Damage: {new_warrior.abilities[3].damage} (expected: 8)")
    print(f"  Fireball Damage: {new_mage.abilities[2].damage} (expected: 6)")
    print(f"  Multishot Damage: {new_hunter.abilities[5].damage} (expected: 4)")
    
    config_working = (new_warrior.max_health == 25 and 
                     new_warrior.abilities[3].damage == 8 and
                     new_mage.abilities[2].damage == 6 and
                     new_hunter.abilities[5].damage == 4)
    
    pygame.quit()
    
    # Summary
    ap_working = all(results)
    damage_working = all(damage_results)
    
    print(f"\n" + "=" * 25)
    print("🎯 TEST SUMMARY")
    print("-" * 15)
    print(f"AP System (One Action): {'✅ WORKING' if ap_working else '❌ BROKEN'}")
    print(f"Damage Attributes: {'✅ WORKING' if damage_working else '❌ BROKEN'}")
    print(f"Configuration: {'✅ WORKING' if config_working else '❌ BROKEN'}")
    
    all_working = ap_working and damage_working and config_working
    
    if all_working:
        print(f"\n🎉 ALL FIXES SUCCESSFUL!")
        print("✅ Hunter can only do one action per turn")
        print("✅ Mage and Rogue can do special actions")
        print("✅ All abilities have configurable damage")
        print("✅ Configuration changes apply to gameplay")
        return True
    else:
        print(f"\n❌ SOME ISSUES REMAIN!")
        if not ap_working:
            print("❌ AP system not working for all classes")
        if not damage_working:
            print("❌ Damage attributes missing for some abilities")
        if not config_working:
            print("❌ Configuration not applying correctly")
        return False

if __name__ == "__main__":
    success = test_all_fixes()
    if success:
        print(f"\n🚀 READY TO PLAY!")
        print("All issues have been resolved!")
    else:
        print(f"\n🔧 NEEDS MORE WORK!")
        print("Check the errors above.")
    
    sys.exit(0 if success else 1)

#!/usr/bin/env python3

import pygame
import sys
import os

# Add the parent directory to the path so we can import game modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game import Game
from units.hunter import Hunter
from units.warrior import Warrior
from game_config import GAME_CONFIG

def test_hunter_damage_configuration():
    """Test that Hunter abilities use configurable damage values instead of hardcoded ones"""
    pygame.init()
    
    print("🎯 HUNTER DAMAGE CONFIGURATION TEST 🎯")
    print("=" * 50)
    
    # Test 1: Basic Attack Damage
    print("\n📋 TEST 1: Basic Attack Damage Configuration")
    print("-" * 45)
    
    game = Game()
    hunter = Hunter(1)
    target = Warrior(2)
    
    # Set up positions
    hunter.position = (4, 4)
    target.position = (3, 3)  # Diagonal from hunter
    
    hunter.board = game.board
    game.board.units[(4, 4)] = hunter
    game.board.units[(3, 3)] = target
    
    print(f"Hunter at: {hunter.position}")
    print(f"Target at: {target.position}")
    print(f"Target initial HP: {target.health}")
    print(f"Configured basic attack damage: {GAME_CONFIG['hunter_config']['basic_attack_damage']}")
    
    # Test basic attack damage
    damage = hunter.get_attack_damage((3, 3))
    print(f"Hunter basic attack damage: {damage}")
    
    # Execute basic attack
    result = hunter.use_ability(1, (3, 3), game)  # Basic attack is index 1
    print(f"✅ Basic attack result: {result}")
    print(f"Target HP after attack: {target.health}")
    
    # Test 2: Ability Damage Configuration
    print("\n📋 TEST 2: Ability Damage Configuration")
    print("-" * 45)
    
    # Reset for next test
    game2 = Game()
    hunter2 = Hunter(1)
    targets = [Warrior(2) for _ in range(4)]
    
    hunter2.position = (4, 4)
    hunter2.board = game2.board
    game2.board.units[(4, 4)] = hunter2
    
    # Set up targets for different abilities
    target_positions = [(3, 3), (2, 2), (1, 1), (5, 5)]
    for i, target in enumerate(targets):
        target.position = target_positions[i]
        game2.board.units[target_positions[i]] = target
        print(f"Target {i+1} at {target.position} - Initial HP: {target.health}")
    
    print(f"\nConfigured damage values:")
    print(f"  Ricochet Shot: {GAME_CONFIG['hunter_config']['ricochet_shot_damage']}")
    print(f"  Triple Shot: {GAME_CONFIG['hunter_config']['triple_shot_damage']}")
    print(f"  Knockback Shot: {GAME_CONFIG['hunter_config']['knockback_shot_damage']}")
    print(f"  Multishot (Spread Shot): {GAME_CONFIG['hunter_config']['multishot_damage']}")
    print(f"  Crippling Shot: {GAME_CONFIG['hunter_config']['crippling_shot_damage']}")
    
    # Test Knockback Shot
    print(f"\n🎯 Testing Knockback Shot...")
    knockback_target = targets[0]
    print(f"Target HP before: {knockback_target.health}")
    result = hunter2.use_ability(4, (3, 3), game2)  # Knockback Shot
    print(f"✅ Knockback Shot result: {result}")
    print(f"Target HP after: {knockback_target.health}")
    
    # Test 3: Verify Configuration Changes Affect Damage
    print("\n📋 TEST 3: Configuration Changes Affect Damage")
    print("-" * 45)
    
    # Temporarily modify config to test
    original_damage = GAME_CONFIG['hunter_config']['crippling_shot_damage']
    GAME_CONFIG['hunter_config']['crippling_shot_damage'] = 5  # Increase damage
    
    game3 = Game()
    hunter3 = Hunter(1)
    target3 = Warrior(2)
    
    hunter3.position = (4, 4)
    target3.position = (3, 3)
    
    hunter3.board = game3.board
    game3.board.units[(4, 4)] = hunter3
    game3.board.units[(3, 3)] = target3
    
    print(f"Modified Crippling Shot damage to: {GAME_CONFIG['hunter_config']['crippling_shot_damage']}")
    print(f"Target HP before: {target3.health}")
    
    result = hunter3.use_ability(6, (3, 3), game3)  # Crippling Shot
    print(f"✅ Crippling Shot result: {result}")
    print(f"Target HP after: {target3.health}")
    print(f"Expected damage dealt: {5}")
    print(f"Actual damage dealt: {7 - target3.health}")  # Warrior starts with 7 HP
    
    # Restore original config
    GAME_CONFIG['hunter_config']['crippling_shot_damage'] = original_damage
    
    print("\n" + "=" * 50)
    print("🎉 HUNTER DAMAGE CONFIGURATION TEST COMPLETE!")
    print("\n✅ All Hunter abilities now use configurable damage values:")
    print("  • Basic Attack: Uses hunter_config.basic_attack_damage")
    print("  • Ricochet Shot: Uses hunter_config.ricochet_shot_damage")
    print("  • Triple Shot: Uses hunter_config.triple_shot_damage")
    print("  • Knockback Shot: Uses hunter_config.knockback_shot_damage")
    print("  • Spread Shot: Uses hunter_config.multishot_damage")
    print("  • Crippling Shot: Uses hunter_config.crippling_shot_damage")
    print("  • Shot Helper: Uses hunter_config.triple_shot_damage")
    
    print("\n🎮 No more hardcoded damage values in Hunter class!")

if __name__ == "__main__":
    test_hunter_damage_configuration()

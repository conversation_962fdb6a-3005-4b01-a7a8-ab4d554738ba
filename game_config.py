GAME_CONFIG = {
    "global_settings": {
        "move_ap_cost": 1,
        "attack_ap_cost": 2,
        "default_ability_damage": 1,
        "xp_to_level": 3,
        "max_level": 5
    },
    # Hunter configuration
    "hunter_config": {
        "health": 5,
        "max_ap": 7,
        "movement_range": 2,
        "ricochet_shot_ap_cost": 3,
        "ricochet_shot_damage": 1,
        "triple_shot_ap_cost": 3,
        "triple_shot_damage": 1,
        "knockback_shot_ap_cost": 2,
        "knockback_shot_damage": 1,
        "multishot_ap_cost": 3,
        "multishot_damage": 1,
        "piercing_shot_ap_cost": 3,
        "piercing_shot_damage": 1,
        "crippling_shot_ap_cost": 2,
        "crippling_shot_damage": 1,
        "basic_attack_damage": 1
    },
    
    # Warrior configuration
    "warrior_config": {
        "health": 7,
        "max_ap": 6,
        "movement_range": 5,
        "cleave_attack_ap_cost": 2,
        "shield_bash_ap_cost": 2,
        "charge_ap_cost": 3,
        "defensive_stance_ap_cost": 2,
        "riposte_ap_cost": 2
    },
    
    # Rogue configuration
    "rogue_config": {
        "health": 4,
        "max_ap": 8,
        "movement_range": 5,
        "backstab_ap_cost": 2,
        "poison_strike_ap_cost": 2,
        "smoke_bomb_ap_cost": 3,
        "shadow_step_ap_cost": 2,
        "assassination_ap_cost": 4
    },
    
    # Pawn configuration
    "pawn_config": {
        "health": 3,
        "max_ap": 5,
        "movement_range": 3
    },
    
    # King configuration
    "king_config": {
        "health": 10,
        "movement_range": 3,
        "max_ap": 10,
        "royal_decree_ap_cost": 3,
        "divine_shield_ap_cost": 3,
        "tactical_retreat_ap_cost": 3,
        "inspire_ap_cost": 4,
        "royal_execution_ap_cost": 5
    },
    
    # Cleric configuration
    "cleric_config": {
        "health": 6,
        "max_ap": 7,
        "movement_range": 5,
        "heal_ap_cost": 2,
        "mass_heal_ap_cost": 4,
        "cleanse_ap_cost": 2,
        "sanctuary_ap_cost": 5,
        "divine_protection_ap_cost": 3,
        "holy_smite_ap_cost": 3,
        "summon_ap_cost": 3
    },
    
    # Mage configuration
    "mage_config": {
        "health": 4,
        "max_ap": 9,
        "movement_range": 5,
        "fireball_ap_cost": 3,
        "ice_spike_ap_cost": 2,
        "teleport_ap_cost": 2,
        "frost_nova_ap_cost": 4,
        "arcane_missile_ap_cost": 3,
        "cone_of_cold_ap_cost": 3,
        "summon_ap_cost": 4
    },
    
    # New class configurations
    "warlock_config": {
        "health": 6,
        "max_ap": 7,
        "movement_range": 2,
        "life_drain_ap_cost": 2,
        "curse_ap_cost": 2,
        "fear_ap_cost": 3,
        "dark_pact_ap_cost": 1,
        "soul_burn_ap_cost": 3,
        "shadow_bolt_ap_cost": 4
    },
    
    "paladin_config": {
        "health": 8,
        "max_ap": 6,
        "movement_range": 2,
        "lay_on_hands_ap_cost": 2,
        "divine_smite_ap_cost": 3,
        "blessing_ap_cost": 2,
        "consecrate_ap_cost": 4,
        "turn_undead_ap_cost": 3,
        "divine_shield_ap_cost": 3
    },
    
    "druid_config": {
        "health": 7,
        "max_ap": 7,
        "movement_range": 2,
        "wild_shape_ap_cost": 3,
        "entangle_ap_cost": 2,
        "healing_spring_ap_cost": 4,
        "thorn_barrier_ap_cost": 3,
        "call_lightning_ap_cost": 4,
        "natures_wrath_ap_cost": 5
    },
    
    "bard_config": {
        "health": 5,
        "max_ap": 8,
        "movement_range": 3,
        "inspire_ap_cost": 2,
        "song_of_healing_ap_cost": 3,
        "discordant_note_ap_cost": 2,
        "bardic_knowledge_ap_cost": 1,
        "mass_inspiration_ap_cost": 4,
        "shatter_ap_cost": 4
    }
}

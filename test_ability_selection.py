import unittest
import pygame
import sys
from unittest.mock import patch, MagicMock
from game_units import Cler<PERSON>, Ability, config.GAME_SETTINGS
from game_config import SELECTED_ABILITIES
from main_menu import AbilitySelectionMenu, Button

class TestAbilitySelection(unittest.TestCase):
    """Tests for the ability selection system in the main menu"""
    
    def setUp(self):
        """Set up the test environment"""
        pygame.init()
        self.screen = pygame.Surface((1280, 720))
        self.clock = MagicMock()
        
        # Store original abilities to restore later
        self.original_cleric_abilities = []
        self.original_selected_abilities = SELECTED_ABILITIES.copy()
        
        # Create a test cleric to modify
        self.test_cleric = Cleric(player_id=1)
        
        # Store original abilities to restore after test
        for ability in self.test_cleric.abilities:
            self.original_cleric_abilities.append(ability)
    
    def tearDown(self):
        """Clean up after tests"""
        pygame.quit()
        
        # Restore the original abilities
        Cleric_instance = Cleric(player_id=1)
        Cleric_instance.abilities = self.original_cleric_abilities
        
        # Restore the original SELECTED_ABILITIES
        for unit_type, abilities in self.original_selected_abilities.items():
            SELECTED_ABILITIES[unit_type] = abilities.copy()
    
    @patch('main_menu.AbilitySelectionMenu._create_unit')
    def test_add_sixth_ability(self, mock_create_unit):
        """Test adding a 6th ability to the Cleric class and ensuring it appears in the ability selection menu"""
        # Create a test cleric with 6 abilities (original 5 plus new one)
        test_cleric = Cleric(player_id=1)
        
        # Add a 6th placeholder ability to the Cleric class
        placeholder_ability = Ability("Placeholder Ability", 4, "Test ability for selection system", cooldown=3)
        test_cleric.abilities.append(placeholder_ability)
        
        # Set up the mock to return our modified test_cleric
        mock_create_unit.return_value = test_cleric
        
        # Modify the SELECTED_ABILITIES dictionary to include the new ability
        # Note: We're adding index 5 which translates to the 7th ability (after 0-indexed Move, Attack, and abilities 2-6)
        SELECTED_ABILITIES["Cleric"] = [0, 1, 2, 3, 4, 5]
        
        # Create an instance of AbilitySelectionMenu
        menu = AbilitySelectionMenu(self.screen, self.clock)
        
        # Select the Cleric in the menu
        menu.selected_unit = "Cleric"
        
        # Update ability buttons for the Cleric
        menu._update_ability_buttons()
        
        # Retrieve the abilities from our test_cleric, skipping Move and Attack (the first 2)
        all_abilities = test_cleric.abilities[2:]
        
        # Check that six ability buttons were created for the Cleric (excluding Move and Attack)
        self.assertEqual(len(menu.ability_buttons), 6, 
                        "Menu should show all 6 Cleric abilities excluding Move and Attack")
        
        # Check that the ability list includes our new placeholder ability
        ability_names = [ability[2].name for ability in menu.ability_buttons]
        self.assertIn("Placeholder Ability", ability_names, 
                     "The Placeholder Ability should appear in the ability list")

if __name__ == "__main__":
    unittest.main()

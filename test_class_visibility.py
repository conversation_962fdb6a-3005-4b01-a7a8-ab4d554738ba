#!/usr/bin/env python3
"""
Test if new classes are visible in the game
"""

import pygame
import sys
from game_state import Game

def test_class_visibility():
    """Test that new classes appear in unit selection"""
    print("🎮 TESTING CLASS VISIBILITY")
    print("=" * 30)
    
    pygame.init()
    
    # Test 1: Check available units in game state
    print("📋 TEST 1: Available Units in Game State")
    print("-" * 40)
    
    game = Game()
    
    print("Available units for Player 1:")
    for unit_type, count in game.available_units[1].items():
        print(f"  {unit_type}: {count}")
    
    print("\nAvailable units for Player 2:")
    for unit_type, count in game.available_units[2].items():
        print(f"  {unit_type}: {count}")
    
    # Check if new classes are present
    new_classes = ["Warlock", "Paladin", "Druid", "Bard"]
    missing_classes = []
    
    for class_name in new_classes:
        if class_name not in game.available_units[1]:
            missing_classes.append(class_name)
    
    if missing_classes:
        print(f"\n❌ Missing classes: {missing_classes}")
        return False
    else:
        print(f"\n✅ All new classes present in game state")
    
    # Test 2: Check ability selection menu
    print(f"\n📋 TEST 2: Ability Selection Menu")
    print("-" * 35)
    
    try:
        from menu_screens.ability_selection_menu import UNIT_CLASSES
        
        print("Unit classes in ability selection menu:")
        for class_name in UNIT_CLASSES.keys():
            print(f"  {class_name}")
        
        missing_in_menu = []
        for class_name in new_classes:
            if class_name not in UNIT_CLASSES:
                missing_in_menu.append(class_name)
        
        if missing_in_menu:
            print(f"\n❌ Missing from ability menu: {missing_in_menu}")
            return False
        else:
            print(f"\n✅ All new classes present in ability selection menu")
    
    except Exception as e:
        print(f"❌ Error checking ability selection menu: {e}")
        return False
    
    # Test 3: Check unit imports
    print(f"\n📋 TEST 3: Unit Imports")
    print("-" * 25)
    
    try:
        from units import Warlock, Paladin, Druid, Bard
        print("✅ All new classes can be imported")
        
        # Try creating instances
        warlock = Warlock(1)
        paladin = Paladin(1)
        druid = Druid(1)
        bard = Bard(1)
        
        print("✅ All new classes can be instantiated")
        print(f"  Warlock: {warlock.name}")
        print(f"  Paladin: {paladin.name}")
        print(f"  Druid: {druid.name}")
        print(f"  Bard: {bard.name}")
        
    except Exception as e:
        print(f"❌ Error importing/creating new classes: {e}")
        return False
    
    pygame.quit()
    
    print(f"\n" + "=" * 30)
    print("🎯 CLASS VISIBILITY TEST SUMMARY")
    print("-" * 32)
    print("✅ New classes present in game state")
    print("✅ New classes present in ability selection menu")
    print("✅ New classes can be imported and instantiated")
    
    print(f"\n🎉 ALL NEW CLASSES ARE VISIBLE!")
    print("You should now see Warlock, Paladin, Druid, and Bard in the game!")
    
    return True

if __name__ == "__main__":
    success = test_class_visibility()
    if success:
        print(f"\n🚀 NEW CLASSES READY TO USE!")
        print("Launch the game to see them in action!")
    else:
        print(f"\n🔧 VISIBILITY ISSUES REMAIN!")
        print("Check the errors above.")
    
    sys.exit(0 if success else 1)

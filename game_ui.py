import pygame
# from game_board import CELL_SIZE, BOARD_SIZE, BOARD_PADDING # Now from const
import game_constants as const # Import constants
# Import color constants and fonts if they are defined in a central place (e.g., game_settings.py or directly in game.py)
# For now, assume they are passed via the 'game' object or defined locally if simple.

# Colors (examples, ideally from game object or settings)
# DARK_GRAY = (30, 30, 30)
# LIGHT_GRAY = (200, 200, 200)
# BLUE = (0, 100, 200)
# RED = (200, 60, 60)
# GREEN = (60, 180, 75)
# YELLOW = (255, 200, 0)
# Add other colors as needed by the draw functions


def update_ability_buttons(game):
    """Update the ability buttons for the currently selected unit.
    'game' is the main Game object.
    """
    game.ability_buttons = []

    if not game.selected_unit:
        return

    board_right_edge = const.BOARD_PADDING + (const.CELL_SIZE * const.BOARD_SIZE) + 20 # Use const

    button_width = 250 
    button_height = 30
    start_x = board_right_edge + 10 
    start_y = 140 # Start a bit lower to make space for unit info

    spacing = 65 # Increased spacing for readability

    for i, ability in enumerate(game.selected_unit.abilities):
        if i > 6: # Max 7 abilities shown
            break
        button_rect = pygame.Rect(start_x, start_y + i * spacing, button_width, button_height)
        game.ability_buttons.append((button_rect, i, ability))


def draw_setup_ui(game):
    """Draw UI elements during the setup phase.
    'game' is the main Game object.
    """
    player_text = game.title_font.render(f"Player {game.setup_phase} Setup", True, const.LIGHT_GRAY) # Use const
    game.screen.blit(player_text, (20, 20))

    y_offset = 100
    for unit_type, count in game.available_units[game.setup_phase].items():
        color = const.YELLOW if game.selected_unit_type == unit_type else const.LIGHT_GRAY # Use const
        
        text = f"{unit_type}: {count if count < 99 else '∞'}" 
        button_text = game.ui_font.render(text, True, color)
        
        button_rect = game.unit_buttons[unit_type]
        pygame.draw.rect(game.screen, const.DARK_GRAY, button_rect) # Use const
        pygame.draw.rect(game.screen, const.LIGHT_GRAY, button_rect, 2) # Use const
        game.screen.blit(button_text, (button_rect.x + 10, button_rect.y + 5))
        y_offset += 50

    pygame.draw.rect(game.screen, const.GREEN, game.done_button) # Use const
    done_text = game.ui_font.render("Done Placing", True, const.DARK_GRAY) # Use const
    game.screen.blit(done_text, (game.done_button.x + 10, game.done_button.y + 5))

    instructions = [
        f"Player {game.setup_phase}, place your units.",
        f"Select a unit type, then click on your starting rows.",
        f"Player 1: Bottom 3 rows. Player 2: Top 3 rows.",
        "You must place your King.",
        "Click 'Done Placing' when finished."
    ]
    for i, line in enumerate(instructions):
        inst_text = game.small_font.render(line, True, const.LIGHT_GRAY) # Use const
        game.screen.blit(inst_text, (20, const.WINDOW_HEIGHT - 120 + i * 20)) # Use const.WINDOW_HEIGHT


def draw_game_ui(game):
    """Draw UI elements during the gameplay phase.
    'game' is the main Game object.
    """
    # NEW GLOBAL AP SYSTEM - Display turn info and AP
    turn_info_text = f"Player {game.current_player}'s Turn {game.turn_count}"
    turn_text_surf = game.title_font.render(turn_info_text, True, const.LIGHT_GRAY) # Use const
    game.screen.blit(turn_text_surf, (20, 20))

    # Display current AP
    ap_text = f"AP: {game.current_player_ap}"
    ap_color = const.GREEN if game.current_player_ap > 3 else const.YELLOW if game.current_player_ap > 1 else const.RED
    ap_text_surf = game.ui_font.render(ap_text, True, ap_color)
    game.screen.blit(ap_text_surf, (20, 60))

    # Display balance UI toggle hint
    if not game.show_balance_ui:
        hint_text = "F1: Balance Sliders | F2-F4: Presets"
        hint_surf = game.small_font.render(hint_text, True, const.DARK_GRAY)
        game.screen.blit(hint_surf, (20, const.WINDOW_HEIGHT - 30))

    pygame.draw.rect(game.screen, const.RED, game.end_turn_button) # Use const
    end_turn_surf = game.ui_font.render("End Turn", True, const.LIGHT_GRAY) # Use const
    game.screen.blit(end_turn_surf, (game.end_turn_button.x + 15, game.end_turn_button.y + 10))
    
    pygame.draw.rect(game.screen, const.DARK_GRAY, game.escape_button) # Use const
    escape_surf = game.ui_font.render("Main Menu", True, const.LIGHT_GRAY) # Use const
    game.screen.blit(escape_surf, (game.escape_button.x + 10, game.escape_button.y + 10))

    if game.selected_unit:
        unit = game.selected_unit
        info_y = 80
        name_surf = game.ui_font.render(f"{unit.name} (Lvl {unit.level})", True, const.LIGHT_GRAY) # Use const
        game.screen.blit(name_surf, (game.end_turn_button.x - 280, info_y)) 
        
        hp_surf = game.ui_font.render(f"HP: {unit.health}/{unit.max_health}", True, const.GREEN if unit.health > unit.max_health * 0.3 else const.RED) # Use const
        game.screen.blit(hp_surf, (game.end_turn_button.x - 280, info_y + 30))
        
        ap_surf = game.ui_font.render(f"AP: {unit.current_ap}/{unit.max_ap}", True, const.PLAYER1_COLOR) # Use const (example, could be const.BLUE)
        game.screen.blit(ap_surf, (game.end_turn_button.x - 280, info_y + 60))

        status_effects = []

        # New status system
        if hasattr(unit, 'status_manager'):
            status_effects.extend(unit.status_manager.get_status_display())

        # New status effect system (unified)
        if hasattr(unit, 'status_effects') and isinstance(unit.status_effects, dict):
            for effect_type, effect in unit.status_effects.items():
                status_effects.append(f"{effect_type.value} ({effect.duration}t)")

        # Legacy status effects from new system (list-based)
        if hasattr(unit, 'legacy_status_effects'):
            for effect in unit.legacy_status_effects:
                status_effects.append(f"{effect.name} ({effect.duration}t)")

        # Legacy status effects (for backward compatibility with older units)
        if hasattr(unit, 'stunned') and unit.stunned:
            status_effects.append(f"Stunned ({unit.stunned_until - game.turn_count +1}t)")
        if hasattr(unit, 'immobilized') and unit.immobilized:
            status_effects.append(f"Immobilized ({unit.immobilized_until - game.turn_count +1}t)")
        if hasattr(unit, 'chilled') and unit.chilled:
            status_effects.append("Chilled")
        if hasattr(unit, 'poisoned') and unit.poisoned:
            status_effects.append(f"Poisoned ({unit.poison_remaining}t)")
        if hasattr(unit, 'divine_protection') and unit.divine_protection:
            status_effects.append("Protected")
        if hasattr(unit, 'sanctuary') and unit.sanctuary and hasattr(unit, 'sanctuary_until') and unit.sanctuary_until > game.turn_count:
            status_effects.append(f"Sanctuary ({unit.sanctuary_until - game.turn_count +1}t)")


        if status_effects:
            status_surf = game.small_font.render("Status: " + ", ".join(status_effects), True, const.YELLOW) # Use const
            game.screen.blit(status_surf, (game.end_turn_button.x -280, info_y + 90))


        for button_rect, ability_idx, ability in game.ability_buttons:
            can_use = unit.can_use_ability(ability_idx)
            color = const.BUTTON_COLOR # Use const
            text_color = const.BUTTON_TEXT_COLOR # Use const
            if game.selected_ability == ability_idx:
                color = const.SELECTED_ABILITY_BUTTON_COLOR # Use const (e.g. PLAYER1_COLOR or a specific blue)
            elif not can_use:
                color = const.BUTTON_DISABLED_COLOR # Use const
                text_color = const.BUTTON_DISABLED_TEXT_COLOR # Use const

            pygame.draw.rect(game.screen, color, button_rect)
            pygame.draw.rect(game.screen, const.BUTTON_BORDER_COLOR, button_rect, 1) # Use const
            
            # Display ability name and AP cost (and cooldown)
            # Check for chilled status using both old and new systems
            is_chilled = False
            if hasattr(unit, 'chilled'):
                is_chilled = unit.chilled
            elif hasattr(unit, 'has_status'):
                is_chilled = unit.has_status('Chilled')

            ap_cost_display = ability.ap_cost + (1 if is_chilled else 0)
            ability_text = f"{ability.name} ({ap_cost_display} AP)"
            if ability.cooldown_remaining > 0:
                ability_text += f" CD: {ability.cooldown_remaining}"

            ability_surf = game.small_font.render(ability_text, True, text_color)
            game.screen.blit(ability_surf, (button_rect.x + 5, button_rect.y + 7))
            
            # Display ability description on hover (optional, simple version below)
            # if button_rect.collidepoint(pygame.mouse.get_pos()):
            #    desc_surf = game.tiny_font.render(ability.description, True, LIGHT_GRAY)
            #    game.screen.blit(desc_surf, (button_rect.x, button_rect.y + button_height + 2))


    if game.selected_unit or game.directional_ability: 
        highlight_sets = [
            (game.valid_moves, const.MOVE_HIGHLIGHT_COLOR), # Use const
            (game.valid_attacks, const.ATTACK_HIGHLIGHT_COLOR), # Use const
            (game.valid_ability_targets, const.ABILITY_TARGET_HIGHLIGHT_COLOR) # Use const
        ]
        if game.directional_ability and game.highlighted_positions: 
             highlight_sets.append((game.highlighted_positions, game.cone_color if game.cone_color else const.DIRECTIONAL_PREVIEW_COLOR)) # Use const

        for targets, color_val in highlight_sets:
            for r, c in targets:
                s = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA) # Use const
                s.fill(color_val)
                game.screen.blit(s, (const.BOARD_PADDING + c * const.CELL_SIZE, const.BOARD_PADDING + r * const.CELL_SIZE)) # Use const
    
    if game.directional_ability and game.selected_unit and game.hover_pos:
        start_screen_pos = game.board_to_screen(game.selected_unit.position)
        end_screen_pos = game.board_to_screen(game.hover_pos) 
        if start_screen_pos and end_screen_pos:
             pygame.draw.line(game.screen, const.YELLOW, start_screen_pos, end_screen_pos, 2) # Use const

    # BALANCE SLIDER UI - Draw if enabled
    if game.show_balance_ui:
        # Draw semi-transparent background
        overlay = pygame.Surface((600, 500), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 200))  # Black with alpha
        game.screen.blit(overlay, (50, 100))

        # Draw balance sliders
        game.balance_sliders.render_sliders(game.screen, 50, 100)


def draw_game_over_ui(game):
    """Draw game over screen.
    'game' is the main Game object.
    """
    overlay = pygame.Surface((game.screen.get_width(), game.screen.get_height()), pygame.SRCALPHA) 
    overlay.fill((0, 0, 0, 180))  # Black with alpha (could be const.BLACK with alpha)
    game.screen.blit(overlay, (0, 0))

    title_text = "Game Over!"
    if game.winner:
        title_text = f"Player {game.winner} Wins!"
    
    title_surf = game.title_font.render(title_text, True, const.YELLOW) # Use const
    title_rect = title_surf.get_rect(center=(game.screen.get_width() // 2, game.screen.get_height() // 3))
    game.screen.blit(title_surf, title_rect)

    instruction_text = "Click anywhere to return to Main Menu" 
    inst_surf = game.ui_font.render(instruction_text, True, const.LIGHT_GRAY) # Use const
    inst_rect = inst_surf.get_rect(center=(game.screen.get_width() // 2, game.screen.get_height() // 2))
    game.screen.blit(inst_surf, inst_rect)

# Remove placeholder WINDOW_HEIGHT and WINDOW_WIDTH definitions
# WINDOW_HEIGHT = 720 
# WINDOW_WIDTH = 1280 
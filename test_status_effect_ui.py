#!/usr/bin/env python3
"""
Test status effect UI display after Shield Bash.
"""

import pygame
pygame.init()

from game_state import Game
from units.warrior import Warrior
from units.rogue import Rogue

def test_status_effect_ui():
    print("🛡️ TESTING STATUS EFFECT UI AFTER SHIELD BASH")
    print("=" * 50)
    
    # Create game and units
    game = Game()
    warrior = Warrior(1)
    target = Rogue(2)
    
    # Set up board
    game.board.add_unit(warrior, 4, 4)
    game.board.add_unit(target, 4, 5)
    
    # Set up game state
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Before Shield Bash:")
    print(f"  Target stunned: {target.has_status('Stunned')}")
    print(f"  Target status_effects: {target.status_effects}")
    
    # Use Shield Bash
    success = warrior.use_ability(3, target.position, game)
    
    print(f"\nAfter Shield Bash:")
    print(f"  Shield Bash success: {success}")
    print(f"  Target stunned: {target.has_status('Stunned')}")
    print(f"  Target status_effects: {target.status_effects}")
    
    # Test the UI status effect display logic
    print(f"\nTesting UI status effect display:")
    
    # Simulate the UI code
    status_effects = []
    
    # New status effect system (unified)
    if hasattr(target, 'status_effects') and isinstance(target.status_effects, dict):
        for effect_type, effect in target.status_effects.items():
            status_effects.append(f"{effect_type.value} ({effect.duration}t)")
            print(f"  Found effect: {effect_type.value} with duration {effect.duration}")
    
    # Legacy status effects from new system (list-based)
    if hasattr(target, 'legacy_status_effects'):
        for effect in target.legacy_status_effects:
            status_effects.append(f"{effect.name} ({effect.duration}t)")
            print(f"  Found legacy effect: {effect.name} with duration {effect.duration}")
    
    print(f"\nFinal status effects list: {status_effects}")
    
    if status_effects:
        print("✅ Status effect UI display working correctly!")
    else:
        print("❌ No status effects found in UI display")
    
    return len(status_effects) > 0

if __name__ == "__main__":
    success = test_status_effect_ui()
    print(f"\nTest result: {'PASS' if success else 'FAIL'}")

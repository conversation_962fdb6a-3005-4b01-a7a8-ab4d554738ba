import pygame
# from game_state import Game, DARK_GRAY # DARK_GRAY removed
from game_state import Game # For type hinting
# from game_constants import STATE_SETUP, STATE_PLAYING, STATE_GAME_OVER # Now use const.* for these
import game_constants as const # Import constants
import game_ui # For the specific UI drawing functions
# game_board is implicitly used via game_instance.board.draw

def draw_game(game_instance: Game):
    """Main drawing function to render the entire game state."""
    
    # 1. Fill the screen
    game_instance.screen.fill(const.DARK_GRAY)
    
    # 2. Draw the board (which includes grid and units)
    # GameBoard.draw() seems to handle its own unit drawing internally.
    # It also draws highlights for valid_moves, valid_attacks, selected_unit path.
    game_instance.board.draw(valid_moves=game_instance.valid_moves, 
                             valid_attacks=game_instance.valid_attacks, 
                             selected_unit=game_instance.selected_unit)
    
    # 3. Draw appropriate UI based on game state
    if game_instance.state == const.STATE_SETUP:
        game_ui.draw_setup_ui(game_instance)
    elif game_instance.state == const.STATE_PLAYING:
        game_ui.draw_game_ui(game_instance) # This also draws highlights for ability targets and directional previews
    elif game_instance.state == const.STATE_GAME_OVER:
        game_ui.draw_game_over_ui(game_instance)
    
    # 4. Update the display
    pygame.display.flip() 
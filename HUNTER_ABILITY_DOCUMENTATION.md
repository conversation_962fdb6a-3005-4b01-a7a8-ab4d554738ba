# 🏹 Hunter Ability Documentation

## 📋 **Complete Hunter Ability Specifications**

Based on user preferences and memories, here are the correct specifications for all Hunter abilities:

### **1. Basic Attack**
- **Pattern**: Diagonal directions only
- **Range**: Single target
- **Damage**: Configurable via sliders

### **2. Ricochet Shot**
- **Pattern**: Diagonal targeting
- **Behavior**: Shot bounces **twice** to hit up to **3 targets total**
- **Description**: "Shot bounces twice to hit up to 3 targets"
- **Mechanics**: Hit primary target → ricochet to 2nd target → ricochet to 3rd target

### **3. Triple Shot**
- **Pattern**: Diagonal directions
- **Behavior**: Fire 3 arrows in the **same diagonal direction**
- **Mechanics**: All 3 arrows travel in the selected diagonal, piercing through targets

### **4. Knockback Shot**
- **Pattern**: Diagonal targeting
- **Behavior**: Push target back 1 tile
- **Mechanics**: Damage + knockback effect

### **5. Spread Shot (Multishot)**
- **Pattern**: Orthogonal directions (N, S, E, W)
- **Behavior**: Fire arrows in **orthogonal direction plus the two diagonals that contain that orthogonal**
- **Example**: If firing North, also fire NW and NE (3 arrows total in spread pattern)
- **User Preference**: "Multishot ability (previously Spread Shot) should fire arrows in orthogonal direction plus the two diagonals that contain that orthogonal (3 arrows total in a spread pattern)."

### **6. Crippling Shot**
- **Pattern**: Diagonal targeting
- **Behavior**: Damage + apply Crippled status effect
- **Duration**: **1 turn** (not infinite)
- **Effect**: Prevents movement for 1 turn
- **User Preference**: "Crippling Shot ability should apply the Crippled effect."

## 🐛 **Current Issues Identified**

### **Issue 1: Spread Shot Wrong Pattern**
- **Current**: Fires in straight line (like piercing shot)
- **Should Be**: Fires in spread pattern (orthogonal + 2 diagonals)
- **Fix Needed**: Complete rewrite of `_use_spread_shot` method

### **Issue 2: Ricochet Shot Multiple Bounces**
- **Current**: Only bounces once (correct behavior)
- **User Report**: "doesn't ricochet a second time"
- **Analysis**: This might be a misunderstanding - Ricochet Shot should only bounce once

### **Issue 3: Crippling Shot Infinite Duration**
- **Current**: Applied with duration 1
- **User Report**: "effect is infinite"
- **Analysis**: Likely issue with status effect duration system not counting down properly

## 🎯 **Targeting Patterns Summary**

| Ability | Targeting Pattern | Special Mechanics |
|---------|------------------|-------------------|
| Basic Attack | Diagonal | Single target |
| Ricochet Shot | Diagonal | Bounces to 1 additional target |
| Triple Shot | Diagonal | 3 arrows same direction |
| Knockback Shot | Diagonal | Damage + knockback |
| Spread Shot | Orthogonal | 3 arrows in spread pattern |
| Crippling Shot | Diagonal | Damage + Crippled (1 turn) |

## 🔧 **Required Fixes**

1. **Fix Spread Shot**: Implement proper spread pattern
2. **Verify Ricochet Shot**: Ensure it bounces exactly once
3. **Fix Crippling Duration**: Ensure status effect expires after 1 turn
4. **Create Comprehensive Tests**: Test all abilities thoroughly

## 📝 **User Preferences Reference**

From memories:
- "Multishot ability (previously Spread Shot) should fire arrows in orthogonal direction plus the two diagonals that contain that orthogonal (3 arrows total in a spread pattern)."
- "Crippling Shot ability should apply the Crippled effect."
- Status effects should have duration based on the unit owner's turns
- Crippled X: Prevents movement for X turns

---

**Status**: 🔧 FIXES NEEDED  
**Priority**: HIGH (Core gameplay mechanics)  
**Impact**: User experience and game balance

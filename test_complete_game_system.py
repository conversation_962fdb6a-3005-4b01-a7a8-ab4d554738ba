#!/usr/bin/env python3

import pygame
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state import Game
from units.warrior import Warrior
from units.rogue import Rogue
from units.mage import Mage
from units.cleric import Cleric
from units.hunter import <PERSON>

def test_complete_game_system():
    """Comprehensive test of the entire game system"""
    print("🎮 COMPREHENSIVE GAME SYSTEM TEST")
    print("=" * 50)
    
    # Initialize pygame
    pygame.init()
    
    # Test results
    results = {
        "basic_attacks": 0,
        "special_abilities": 0,
        "summon_abilities": 0,
        "status_effects": 0,
        "ap_system": 0,
        "total_tests": 0
    }
    
    classes_to_test = [
        ("Warrior", Warrior),
        ("Rogue", Rogue),
        ("Mage", Mage),
        ("<PERSON><PERSON><PERSON>", <PERSON>leric),
        ("<PERSON>", <PERSON>)
    ]
    
    for class_name, class_type in classes_to_test:
        print(f"\n🔍 TESTING {class_name.upper()}")
        print("-" * 30)
        
        try:
            # Create a game instance
            game = Game()
            
            # Create units
            unit = class_type(player_id=1)
            target = Warrior(player_id=2)  # Use Warrior as target
            
            game.board.add_unit(unit, 4, 4)
            game.board.add_unit(target, 4, 5)  # Adjacent for attack
            
            # Set up game state
            game.current_player = 1
            game.current_player_ap = 10
            game.units_acted_this_turn = set()
            
            print(f"{class_name} HP: {unit.health}/{unit.max_health}")
            print(f"Target HP: {target.health}/{target.max_health}")
            print(f"Initial AP: {game.current_player_ap}")
            
            # Test 1: Basic Attack
            print(f"\n📋 Test 1: {class_name} Basic Attack")
            attack_success = unit.use_ability(1, target.position, game)
            target_damage = target.max_health - target.health
            ap_used = 10 - game.current_player_ap
            
            print(f"  Attack success: {attack_success}")
            print(f"  Damage dealt: {target_damage}")
            print(f"  AP used: {ap_used}")
            
            if attack_success and target_damage > 0 and ap_used > 0:
                results["basic_attacks"] += 1
                print(f"  ✅ Basic attack working")
            else:
                print(f"  ❌ Basic attack failed")
            
            # Reset for next test
            target.health = target.max_health
            game.current_player_ap = 10
            game.units_acted_this_turn = set()
            unit.has_acted_this_turn = False
            
            # Test 2: Special Ability (if available)
            print(f"\n📋 Test 2: {class_name} Special Ability")
            special_abilities = [a for a in unit.abilities if a.name not in ["Move", "Basic Attack", "Summon"]]
            
            if special_abilities:
                special_ability = special_abilities[0]
                special_idx = unit.abilities.index(special_ability)
                
                print(f"  Testing: {special_ability.name}")
                print(f"  AP cost: {special_ability.ap_cost}")
                
                # Get valid targets
                valid_targets = unit.get_ability_targets(special_idx, game.board)
                if valid_targets and target.position in valid_targets:
                    special_success = unit.use_ability(special_idx, target.position, game)
                    special_ap_used = 10 - game.current_player_ap
                    
                    print(f"  Special ability success: {special_success}")
                    print(f"  AP used: {special_ap_used}")
                    
                    if special_success and special_ap_used > 0:
                        results["special_abilities"] += 1
                        print(f"  ✅ Special ability working")
                    else:
                        print(f"  ❌ Special ability failed")
                else:
                    print(f"  ⚠️ No valid targets for {special_ability.name}")
            else:
                print(f"  ⚠️ No special abilities to test")
            
            # Reset for next test
            target.health = target.max_health
            game.current_player_ap = 10
            game.units_acted_this_turn = set()
            unit.has_acted_this_turn = False
            
            # Test 3: Summon Ability
            print(f"\n📋 Test 3: {class_name} Summon")
            summon_idx = None
            for i, ability in enumerate(unit.abilities):
                if ability.name == "Summon":
                    summon_idx = i
                    break
            
            if summon_idx is not None:
                valid_summon_targets = unit.get_ability_targets(summon_idx, game.board)
                if valid_summon_targets:
                    summon_target = valid_summon_targets[0]
                    summon_success = unit.use_ability(summon_idx, summon_target, game)
                    summon_ap_used = 10 - game.current_player_ap
                    
                    # Check if unit was actually summoned
                    summoned_unit = game.board.units.get(summon_target)
                    unit_created = summoned_unit and summoned_unit != unit
                    
                    print(f"  Summon success: {summon_success}")
                    print(f"  AP used: {summon_ap_used}")
                    print(f"  Unit created: {unit_created}")
                    
                    if summon_success and summon_ap_used > 0 and unit_created:
                        results["summon_abilities"] += 1
                        print(f"  ✅ Summon working")
                    else:
                        print(f"  ❌ Summon failed")
                else:
                    print(f"  ⚠️ No valid summon targets")
            else:
                print(f"  ❌ No summon ability found")
            
            results["total_tests"] += 1
            
        except Exception as e:
            print(f"❌ {class_name} FAILED: {e}")
    
    # Final Summary
    print(f"\n" + "=" * 50)
    print("🎯 COMPREHENSIVE TEST RESULTS")
    print("=" * 50)
    
    total_classes = len(classes_to_test)
    
    print(f"✅ Basic Attacks Working: {results['basic_attacks']}/{total_classes}")
    print(f"✅ Special Abilities Working: {results['special_abilities']}/{total_classes}")
    print(f"✅ Summon Abilities Working: {results['summon_abilities']}/{total_classes}")
    
    # Calculate overall score
    max_possible = total_classes * 3  # 3 tests per class
    actual_score = results['basic_attacks'] + results['special_abilities'] + results['summon_abilities']
    percentage = (actual_score / max_possible) * 100
    
    print(f"\n📊 OVERALL SCORE: {actual_score}/{max_possible} ({percentage:.1f}%)")
    
    if percentage >= 90:
        print("🎉 EXCELLENT! Game system is fully functional!")
        return True
    elif percentage >= 70:
        print("✅ GOOD! Most systems working, minor issues remain.")
        return True
    else:
        print("⚠️ NEEDS WORK! Major issues detected.")
        return False

if __name__ == "__main__":
    success = test_complete_game_system()
    if success:
        print("\n🎮 GAME READY FOR PLAY! 🎮")
    else:
        print("\n🔧 GAME NEEDS MORE FIXES 🔧")

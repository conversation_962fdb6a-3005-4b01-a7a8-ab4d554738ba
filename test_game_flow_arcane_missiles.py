#!/usr/bin/env python3
"""
Test to simulate the exact game flow that might cause the Arcane Missiles bug
"""

import pygame
from game_state import Game
from units.mage import Mage
from units.warrior import Warrior

def test_game_flow_arcane_missiles():
    """Test Arcane Missiles through the actual game flow"""
    pygame.init()
    
    print("🎮 TESTING GAME FLOW ARCANE MISSILES 🎮")
    print("=" * 45)
    
    # Test 1: Simulate exact game initialization
    print("📋 TEST 1: Game Initialization Flow")
    print("-" * 35)
    
    # Create game exactly like the real game does
    game = Game()
    
    # Add units to the game (simulating game setup)
    mage = Mage(1)
    target = Warrior(2)
    
    # Position units
    mage.position = (4, 4)
    target.position = (3, 4)
    
    # Add to board units dictionary
    game.board.units[(4, 4)] = mage
    game.board.units[(3, 4)] = target
    
    print(f"Game setup:")
    print(f"  Mage at {mage.position}")
    print(f"  Target at {target.position}")
    print(f"  Mage board reference: {mage.board}")
    print(f"  Game board units: {len(game.board.units)}")
    
    # Test 2: Simulate selecting the mage (like clicking on it)
    print(f"\n📋 TEST 2: Unit Selection Flow")
    print("-" * 32)
    
    # Simulate selecting the mage
    game.selected_unit = mage
    print(f"  Selected unit: {game.selected_unit.name}")
    print(f"  Selected unit board: {game.selected_unit.board}")
    
    # Test 3: Simulate ability selection and usage WITHOUT movement
    print(f"\n📋 TEST 3: Ability Usage WITHOUT Movement")
    print("-" * 42)
    
    # Find Arcane Missile ability index
    arcane_ability_idx = None
    for i, ability in enumerate(mage.abilities):
        if ability.name == "Arcane Missile":
            arcane_ability_idx = i
            break
    
    print(f"  Arcane Missile ability index: {arcane_ability_idx}")
    
    # Simulate the exact game logic flow for ability usage
    # This is what happens in game_logic.py when you click to use an ability
    
    # Step 1: Set board reference (like game_logic.py line 72)
    game.selected_unit.board = game.board
    print(f"  Board reference set: {game.selected_unit.board is not None}")
    
    # Step 2: Check if target is valid (simulating game logic)
    target_pos = (3, 4)
    valid_targets = mage.get_ability_targets(arcane_ability_idx, game.board)
    print(f"  Valid targets: {len(valid_targets)}")
    print(f"  Target {target_pos} is valid: {target_pos in valid_targets}")
    
    # Step 3: Record state before ability
    original_hp = target.health
    original_ap = mage.current_ap
    print(f"  Target HP before: {original_hp}")
    print(f"  Mage AP before: {original_ap}")
    
    # Step 4: Use ability (exact same call as game logic)
    print(f"\n🚀 Using Arcane Missiles (simulating game flow)...")
    result = game.selected_unit.use_ability(arcane_ability_idx, target_pos, game)
    
    print(f"\nResults:")
    print(f"  Ability result: {result}")
    print(f"  Target HP after: {target.health}")
    print(f"  Mage AP after: {mage.current_ap}")
    
    damage_dealt = original_hp - target.health
    ap_used = original_ap - mage.current_ap
    
    if damage_dealt > 0:
        print(f"✅ Damage dealt: {damage_dealt}")
    else:
        print(f"❌ NO DAMAGE DEALT - BUG REPRODUCED!")
    
    if ap_used > 0:
        print(f"  AP consumed: {ap_used}")
    else:
        print(f"  No AP consumed")
    
    # Test 4: Check for error messages
    print(f"\n📋 TEST 4: Error Detection")
    print("-" * 25)
    
    # Check if there were any error conditions
    if not result and ap_used > 0:
        print(f"❌ Ability failed but AP was consumed - indicates board reference issue")
    elif not result and ap_used == 0:
        print(f"⚠️  Ability failed and no AP consumed - indicates validation failure")
    elif result and damage_dealt == 0:
        print(f"❌ Ability succeeded but no damage - indicates targeting issue")
    elif result and damage_dealt > 0:
        print(f"✅ Ability worked correctly")
    
    # Test 5: Direct board lookup test
    print(f"\n📋 TEST 5: Direct Board Lookup Test")
    print("-" * 35)
    
    print(f"  Testing board lookup from mage perspective:")
    print(f"  mage.board: {mage.board}")
    print(f"  mage.board.units: {mage.board.units if mage.board else 'None'}")
    
    if mage.board:
        lookup_result = mage.board.units.get((3, 4))
        print(f"  mage.board.units.get((3, 4)): {lookup_result}")
        if lookup_result:
            print(f"    Found: {lookup_result.name} with {lookup_result.health} HP")
        else:
            print(f"    ❌ Target not found in board lookup!")
    
    # Test 6: Compare with movement flow
    print(f"\n📋 TEST 6: After Movement Flow")
    print("-" * 31)
    
    # Reset for movement test
    game2 = Game()
    mage2 = Mage(1)
    target2 = Warrior(2)
    
    # Position with room for movement
    mage2.position = (5, 4)
    target2.position = (3, 4)
    
    game2.board.units[(5, 4)] = mage2
    game2.board.units[(3, 4)] = target2
    game2.selected_unit = mage2
    
    print(f"  Setup for movement test:")
    print(f"  Mage at {mage2.position}, Target at {target2.position}")
    
    # Move first
    print(f"  Moving mage...")
    move_result = mage2.use_ability(0, (4, 4), game2)
    if move_result:
        # Update board position
        del game2.board.units[(5, 4)]
        game2.board.units[(4, 4)] = mage2
        mage2.position = (4, 4)
        print(f"  Mage moved to {mage2.position}")
    
    # Now use Arcane Missiles
    original_hp2 = target2.health
    result2 = mage2.use_ability(arcane_ability_idx, (3, 4), game2)
    damage_dealt2 = original_hp2 - target2.health
    
    print(f"  Arcane Missiles after movement:")
    print(f"    Result: {result2}")
    print(f"    Damage: {damage_dealt2}")
    
    if damage_dealt2 > 0:
        print(f"✅ Works after movement")
    else:
        print(f"❌ Still doesn't work after movement")
    
    print(f"\n" + "=" * 45)
    print("🎯 GAME FLOW TEST SUMMARY")
    print("-" * 25)
    print("This test simulates the exact game flow")
    print("to identify where the bug occurs.")

if __name__ == "__main__":
    test_game_flow_arcane_missiles()

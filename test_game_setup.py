#!/usr/bin/env python3
"""
Test script to check game setup and passive application
"""

import pygame
from game_state import Game
from game_setup import setup_game
from units.hunter import <PERSON>
from units.king import King

def test_game_setup():
    """Test the actual game setup process"""
    pygame.init()
    
    print("🎮 GAME SETUP TEST 🎮")
    print("=" * 30)
    
    # Test 1: Manual unit creation (like our tests)
    print("\n📋 TEST 1: Manual Unit Creation")
    print("-" * 30)
    
    hunter = Hunter(1)
    king = King(1)
    
    print(f"Hunter passives before filtering: {len(hunter.passive_manager.passive_abilities)}")
    print(f"King passives before filtering: {len(king.passive_manager.passive_abilities)}")
    
    # Apply filtering like the game does
    game = Game()
    game._filter_unit_abilities(hunter)
    game._filter_unit_abilities(king)
    
    print(f"Hunter passives after filtering: {len(hunter.passive_manager.passive_abilities)}")
    print(f"King passives after filtering: {len(king.passive_manager.passive_abilities)}")
    
    for passive in hunter.passive_manager.passive_abilities:
        print(f"  Hunter: {passive.name}")
    for passive in king.passive_manager.passive_abilities:
        print(f"  King: {passive.name}")
    
    # Test 2: Full game setup
    print("\n\n📋 TEST 2: Full Game Setup")
    print("-" * 25)
    
    try:
        game2 = setup_game()
        print(f"Game setup successful: {game2 is not None}")
        
        if game2:
            print(f"Board units: {len(game2.board.units)}")
            
            # Check a few units for passives
            hunter_found = None
            king_found = None
            
            for pos, unit in game2.board.units.items():
                if unit.name == "Hunter" and hunter_found is None:
                    hunter_found = unit
                elif unit.name == "King" and king_found is None:
                    king_found = unit
            
            if hunter_found:
                print(f"Game Hunter passives: {len(hunter_found.passive_manager.passive_abilities)}")
                for passive in hunter_found.passive_manager.passive_abilities:
                    print(f"  • {passive.name}")
                
                # Test movement
                moves = hunter_found.get_valid_moves(game2.board)
                print(f"Game Hunter valid moves: {len(moves)}")
                print(f"Game Hunter can use move: {hunter_found.can_use_ability(0)}")
                print(f"Game Hunter AP: {hunter_found.current_ap}/{hunter_found.max_ap}")
            
            if king_found:
                print(f"Game King passives: {len(king_found.passive_manager.passive_abilities)}")
                for passive in king_found.passive_manager.passive_abilities:
                    print(f"  • {passive.name}")
                
                # Test movement
                moves = king_found.get_valid_moves(game2.board)
                print(f"Game King valid moves: {len(moves)}")
                print(f"Game King can use move: {king_found.can_use_ability(0)}")
                print(f"Game King AP: {king_found.current_ap}/{king_found.max_ap}")
    
    except Exception as e:
        print(f"❌ Error in game setup: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Check passive selections
    print("\n\n📋 TEST 3: Passive Selections Check")
    print("-" * 35)
    
    from game_settings import SELECTED_PASSIVES
    print("Current passive selections:")
    for class_name, passives in SELECTED_PASSIVES.items():
        print(f"  {class_name}: {passives}")
    
    # Test 4: Check if there's a UI issue
    print("\n\n📋 TEST 4: UI Interaction Simulation")
    print("-" * 35)
    
    # Create a simple game state
    game3 = Game()
    hunter3 = Hunter(1)
    hunter3.position = (4, 4)
    hunter3.board = game3.board
    game3.board.units = {(4, 4): hunter3}
    game3._filter_unit_abilities(hunter3)
    
    print(f"Hunter position: {hunter3.position}")
    print(f"Hunter AP: {hunter3.current_ap}/{hunter3.max_ap}")
    print(f"Hunter passives: {len(hunter3.passive_manager.passive_abilities)}")
    
    # Simulate clicking on the hunter
    print(f"\nSimulating unit selection...")
    selected_unit = game3.board.units.get((4, 4))
    print(f"Selected unit: {selected_unit.name if selected_unit else 'None'}")
    
    if selected_unit:
        # Simulate trying to move
        print(f"Getting move targets...")
        move_targets = selected_unit.get_ability_targets(0, game3.board)
        print(f"Move targets available: {len(move_targets)}")
        
        if move_targets:
            print(f"Simulating move to {move_targets[0]}...")
            result = selected_unit.use_ability(0, move_targets[0], game3)
            print(f"Move result: {result}")
            print(f"New position: {selected_unit.position}")
            print(f"AP after move: {selected_unit.current_ap}/{selected_unit.max_ap}")
    
    print("\n" + "=" * 30)
    print("🔍 GAME SETUP DIAGNOSIS COMPLETE")
    print("\nKey findings:")
    print("  • Unit logic works correctly")
    print("  • Movement and abilities function")
    print("  • AP system working")
    print("  • Issue likely in UI event handling")
    print("\nSuggestions:")
    print("  • Check mouse click detection")
    print("  • Check unit selection logic")
    print("  • Check turn management")
    print("  • Check player ID matching")

if __name__ == "__main__":
    test_game_setup()

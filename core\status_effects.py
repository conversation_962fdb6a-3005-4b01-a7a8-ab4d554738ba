"""
Unified Status Effect System

This module provides a centralized approach to status effect management,
ensuring consistent behavior across all units and abilities.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from enum import Enum


class StatusEffectType(Enum):
    """Enumeration of status effect types"""
    STUNNED = "Stunned"
    CRIPPLED = "Crippled"
    CHILLED = "Chilled"
    POISONED = "Poisoned"
    BURNING = "Burning"
    BLESSED = "Blessed"
    SHIELDED = "Shielded"
    INVISIBLE = "Invisible"


class StatusEffect:
    """
    Base class for all status effects with duration and effect logic.
    """
    
    def __init__(self, effect_type: StatusEffectType, duration: int, intensity: float = 1.0):
        self.effect_type = effect_type
        self.duration = duration
        self.intensity = intensity
        self.source_unit = None  # Unit that applied this effect
    
    def apply_effect(self, unit) -> bool:
        """
        Apply the status effect to a unit.
        
        Args:
            unit: The unit to apply the effect to
            
        Returns:
            True if effect was applied successfully
        """
        return True
    
    def remove_effect(self, unit) -> bool:
        """
        Remove the status effect from a unit.
        
        Args:
            unit: The unit to remove the effect from
            
        Returns:
            True if effect was removed successfully
        """
        return True
    
    def on_turn_start(self, unit) -> bool:
        """
        Called at the start of the affected unit's turn.
        
        Args:
            unit: The affected unit
            
        Returns:
            True if the unit can act normally, False if prevented
        """
        return True
    
    def on_turn_end(self, unit):
        """
        Called at the end of the affected unit's turn.
        Typically used to reduce duration.
        
        Args:
            unit: The affected unit
        """
        self.duration -= 1
    
    def modify_ap_cost(self, base_cost: int) -> int:
        """
        Modify the AP cost of an ability.
        
        Args:
            base_cost: The base AP cost
            
        Returns:
            Modified AP cost
        """
        return base_cost
    
    def modify_damage_dealt(self, base_damage: float) -> float:
        """
        Modify damage dealt by the affected unit.
        
        Args:
            base_damage: The base damage
            
        Returns:
            Modified damage
        """
        return base_damage
    
    def modify_damage_received(self, base_damage: float) -> float:
        """
        Modify damage received by the affected unit.
        
        Args:
            base_damage: The base damage
            
        Returns:
            Modified damage
        """
        return base_damage
    
    def can_move(self) -> bool:
        """Check if the unit can move while affected by this status"""
        return True
    
    def can_use_abilities(self) -> bool:
        """Check if the unit can use abilities while affected by this status"""
        return True
    
    def __str__(self):
        return f"{self.effect_type.value} {self.duration}"


class StunnedEffect(StatusEffect):
    """Stunned status effect - prevents all actions"""
    
    def __init__(self, duration: int):
        super().__init__(StatusEffectType.STUNNED, duration)
    
    def on_turn_start(self, unit) -> bool:
        return False  # Cannot act while stunned
    
    def can_move(self) -> bool:
        return False
    
    def can_use_abilities(self) -> bool:
        return False


class CrippledEffect(StatusEffect):
    """Crippled status effect - prevents movement"""
    
    def __init__(self, duration: int):
        super().__init__(StatusEffectType.CRIPPLED, duration)
    
    def can_move(self) -> bool:
        return False


class ChilledEffect(StatusEffect):
    """Chilled status effect - increases ability costs"""
    
    def __init__(self, duration: int, cost_increase: int = 1):
        super().__init__(StatusEffectType.CHILLED, duration)
        self.cost_increase = cost_increase
    
    def modify_ap_cost(self, base_cost: int) -> int:
        return base_cost + self.cost_increase


class StatusEffectManager:
    """
    Centralized manager for status effects on units.
    """
    
    def __init__(self):
        self.effect_factories = {
            StatusEffectType.STUNNED: lambda duration: StunnedEffect(duration),
            StatusEffectType.CRIPPLED: lambda duration: CrippledEffect(duration),
            StatusEffectType.CHILLED: lambda duration: ChilledEffect(duration),
        }
    
    def register_effect_factory(self, effect_type: StatusEffectType, factory_func):
        """Register a factory function for creating status effects"""
        self.effect_factories[effect_type] = factory_func
    
    def apply_status_effect(self, unit, effect_type: StatusEffectType, duration: int, **kwargs) -> bool:
        """
        Apply a status effect to a unit.
        
        Args:
            unit: The unit to apply the effect to
            effect_type: Type of status effect
            duration: Duration in turns
            **kwargs: Additional parameters for the effect
            
        Returns:
            True if effect was applied successfully
        """
        if effect_type not in self.effect_factories:
            print(f"Warning: Unknown status effect type: {effect_type}")
            return False
        
        # Create the effect
        effect = self.effect_factories[effect_type](duration)
        
        # Initialize unit status effects if needed
        if not hasattr(unit, 'status_effects'):
            unit.status_effects = {}
        
        # Apply the effect
        unit.status_effects[effect_type] = effect
        effect.apply_effect(unit)
        
        print(f"{unit.name} is now affected by {effect_type.value} for {duration} turns")
        return True
    
    def remove_status_effect(self, unit, effect_type: StatusEffectType) -> bool:
        """
        Remove a status effect from a unit.
        
        Args:
            unit: The unit to remove the effect from
            effect_type: Type of status effect to remove
            
        Returns:
            True if effect was removed successfully
        """
        if not hasattr(unit, 'status_effects'):
            return False
        
        if effect_type in unit.status_effects:
            effect = unit.status_effects[effect_type]
            effect.remove_effect(unit)
            del unit.status_effects[effect_type]
            print(f"{unit.name} is no longer affected by {effect_type.value}")
            return True
        
        return False
    
    def process_turn_start(self, unit) -> bool:
        """
        Process status effects at the start of a unit's turn.
        
        Args:
            unit: The unit whose turn is starting
            
        Returns:
            True if the unit can act normally
        """
        if not hasattr(unit, 'status_effects'):
            return True
        
        can_act = True
        for effect in unit.status_effects.values():
            if not effect.on_turn_start(unit):
                can_act = False
        
        return can_act
    
    def process_turn_end(self, unit):
        """
        Process status effects at the end of a unit's turn.
        
        Args:
            unit: The unit whose turn is ending
        """
        if not hasattr(unit, 'status_effects'):
            return
        
        # Process effects and remove expired ones
        expired_effects = []
        for effect_type, effect in unit.status_effects.items():
            effect.on_turn_end(unit)
            if effect.duration <= 0:
                expired_effects.append(effect_type)
        
        # Remove expired effects
        for effect_type in expired_effects:
            self.remove_status_effect(unit, effect_type)
    
    def get_modified_ap_cost(self, unit, base_cost: int) -> int:
        """
        Get the modified AP cost for a unit considering status effects.
        
        Args:
            unit: The unit using the ability
            base_cost: The base AP cost
            
        Returns:
            Modified AP cost
        """
        if not hasattr(unit, 'status_effects'):
            return base_cost
        
        modified_cost = base_cost
        for effect in unit.status_effects.values():
            modified_cost = effect.modify_ap_cost(modified_cost)
        
        return modified_cost
    
    def can_unit_move(self, unit) -> bool:
        """Check if a unit can move considering status effects"""
        if not hasattr(unit, 'status_effects'):
            return True
        
        for effect in unit.status_effects.values():
            if not effect.can_move():
                return False
        
        return True
    
    def can_unit_use_abilities(self, unit) -> bool:
        """Check if a unit can use abilities considering status effects"""
        if not hasattr(unit, 'status_effects'):
            return True
        
        for effect in unit.status_effects.values():
            if not effect.can_use_abilities():
                return False
        
        return True


# Global status effect manager instance
status_effect_manager = StatusEffectManager()


def get_status_effect_manager() -> StatusEffectManager:
    """Get the global status effect manager instance"""
    return status_effect_manager


def apply_status_effect(unit, effect_type: StatusEffectType, duration: int, **kwargs) -> bool:
    """Convenience function to apply a status effect"""
    return status_effect_manager.apply_status_effect(unit, effect_type, duration, **kwargs)


def remove_status_effect(unit, effect_type: StatusEffectType) -> bool:
    """Convenience function to remove a status effect"""
    return status_effect_manager.remove_status_effect(unit, effect_type)

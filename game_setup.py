import pygame
# from game_board import BOARD_SIZE # Now from const
# We'll need to import unit classes if they are instantiated here directly
from units import <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Druid, Bard
# Import game_settings if SELECTED_ABILITIES is used here, though it's for filtering later
# from game_settings import SELECTED_ABILITIES 
import game_logic # Added import
import game_constants as const # Import constants

# Unused local constants, button rects come from game instance
# WINDOW_WIDTH = 1280 
# WINDOW_HEIGHT = 720 

def process_setup_click(game, pos):
    """Handle clicks during the setup phase.
    'game' is the main Game object.
    """
    # Check if a unit type was selected
    for unit_type, button in game.unit_buttons.items():
        if button.collidepoint(pos):
            if game.available_units[game.setup_phase][unit_type] > 0:
                game.selected_unit_type = unit_type
            return

    # Check if a board position was clicked
    board_pos = game.board.get_cell_from_pos(pos)
    if board_pos:
        row, col = board_pos

        # Check if the position is in the correct setup area
        valid_setup = False
        if game.setup_phase == 1 and row >= const.BOARD_SIZE - 3:  # Player 1 setup at bottom 3 rows
            valid_setup = True
        elif game.setup_phase == 2 and row < 3:  # Player 2 setup at top 3 rows
            valid_setup = True

        if valid_setup and game.selected_unit_type and (row, col) not in game.board.units:
            # Create the appropriate unit
            UnitClass = None
            if game.selected_unit_type == "Warrior":
                UnitClass = Warrior
            elif game.selected_unit_type == "Hunter":
                UnitClass = Hunter
            elif game.selected_unit_type == "Rogue":
                UnitClass = Rogue
            elif game.selected_unit_type == "Pawn":
                UnitClass = Pawn
            elif game.selected_unit_type == "King":
                UnitClass = King
            elif game.selected_unit_type == "Cleric":
                UnitClass = Cleric
            elif game.selected_unit_type == "Mage":
                UnitClass = Mage
            elif game.selected_unit_type == "Warlock":
                UnitClass = Warlock
            elif game.selected_unit_type == "Paladin":
                UnitClass = Paladin
            elif game.selected_unit_type == "Druid":
                UnitClass = Druid
            elif game.selected_unit_type == "Bard":
                UnitClass = Bard
            
            if UnitClass:
                unit = UnitClass(game.setup_phase)
                # Filter unit abilities based on player selections
                game._filter_unit_abilities(unit) # Call as game method

                # Add the unit to the board
                unit.position = (row, col)
                game.board.add_unit(unit, row, col)

                # Reduce the count of available units
                game.available_units[game.setup_phase][game.selected_unit_type] -= 1

                # Reset selected unit type if no more are available
                if game.available_units[game.setup_phase][game.selected_unit_type] <= 0:
                    game.selected_unit_type = None
            
            # Check if all units have been placed (using the new function name)
            if all_units_placed(game, game.setup_phase):
                if game.setup_phase == 1:
                    # Move to player 2 setup
                    game.setup_phase = 2
                    game.selected_unit_type = None
                else:
                    # All setup complete, move to gameplay
                    game.state = const.STATE_PLAYING # Use the constant
                    game_logic.reset_turn(game) # Corrected call

    # Check if done button was clicked
    if game.done_button.collidepoint(pos):
        if all_units_placed(game, game.setup_phase): # Use the new function name
            if game.setup_phase == 1:
                # Move to player 2 setup
                game.setup_phase = 2
                game.selected_unit_type = None
            else:
                # All setup complete, move to gameplay
                game.state = const.STATE_PLAYING # Use the constant
                game_logic.reset_turn(game) # Corrected call

def all_units_placed(game, player):
    """Check if all units for a player have been placed.
    'game' is the main Game object.
    """
    # Make sure the player has placed at least one King
    kings_placed = 0
    
    for unit_in_play in game.board.units.values(): # Renamed unit to unit_in_play to avoid conflict
        if unit_in_play.player_id == player and isinstance(unit_in_play, King):
            kings_placed += 1
    
    # Player must place at least one King to proceed
    return kings_placed > 0 
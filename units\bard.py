import pygame
from units_core import Unit, SimpleAbility, MoveAbility, AttackAbility
from game_config import GAME_CONFIG
import game_constants as const
import math

class Bard(Unit):
    """
    Bard unit class - Support specialist with inspiration, buffs, and crowd control.
    
    Bards are versatile support units who excel at buffing allies, debuffing enemies,
    and providing battlefield control through music and magic. They have unique
    inspiration mechanics that affect multiple units.
    
    Movement:
        - Pattern: All directions (8-directional)
        - Range: 3 tiles in any direction
        - High mobility for positioning
        
    Abilities (8 total):
        0. Move - 8-directional movement (3 tiles)
        1. Basic Attack - Weak ranged sonic attack
        2. Inspire - Grant ally extra AP and damage
        3. Song of Healing - Heal all nearby allies
        4. Discordant Note - Confuse and damage enemy
        5. Bardic Knowledge - Reveal enemy abilities/stats
        6. Mass Inspiration - Buff all allies
        7. Shatter - High damage sonic attack
        
    Default Passive:
        - Inspiring Presence: Allies within 2 tiles get +1 to all rolls
        
    Tactical Role:
        - Primary support unit
        - Force multiplier for team
        - Information gathering specialist
    """
    def __init__(self, player_id):
        super().__init__(player_id, health=GAME_CONFIG.get("bard_config", {}).get("health", 5), max_health=GAME_CONFIG.get("bard_config", {}).get("health", 5))
        self.name = "Bard"
        self.max_ap = GAME_CONFIG.get("bard_config", {}).get("max_ap", 8)
        self.current_ap = GAME_CONFIG.get("bard_config", {}).get("max_ap", 8)
        self.board = None
        
        # Bard-specific state
        self.inspiration_targets = set()  # Track inspired allies
        self.song_active = False
        self.song_type = None  # "healing", "courage", "fear"
        self.bardic_knowledge_used = set()  # Track units analyzed
        
        # Load image (colorful/musical theme)
        self.image = self._create_placeholder_image((255, 165, 0) if player_id == 1 else (255, 20, 147))
        
        # Abilities
        self.abilities = [
            MoveAbility(self),
            AttackAbility(self),
            SimpleAbility("Inspire", GAME_CONFIG.get("bard_config", {}).get("inspire_ap_cost", 2), "Grant ally +2 AP and +1 damage", cooldown=2, owner=self),
            SimpleAbility("Song of Healing", GAME_CONFIG.get("bard_config", {}).get("song_of_healing_ap_cost", 3), "Heal all allies within 2 tiles", cooldown=3, owner=self),
            SimpleAbility("Discordant Note", GAME_CONFIG.get("bard_config", {}).get("discordant_note_ap_cost", 3), "Confuse enemy and deal damage", cooldown=2, owner=self),
            SimpleAbility("Bardic Knowledge", GAME_CONFIG.get("bard_config", {}).get("bardic_knowledge_ap_cost", 1), "Reveal enemy information", cooldown=1, owner=self),
            SimpleAbility("Mass Inspiration", GAME_CONFIG.get("bard_config", {}).get("mass_inspiration_ap_cost", 4), "Buff all allies", cooldown=4, owner=self),
            SimpleAbility("Shatter", GAME_CONFIG.get("bard_config", {}).get("shatter_ap_cost", 4), "High damage sonic attack", cooldown=3, owner=self)
        ]
        
        # Add healing amounts to healing abilities
        self.abilities[3].heal_amount = 2  # Song of Healing
        
        # Apply configuration from balance system
        try:
            from config_loader import apply_config_to_unit
            apply_config_to_unit(self)
        except ImportError:
            pass  # Config loader not available
    
    def _create_placeholder_image(self, color):
        """Create a placeholder image with musical symbols"""
        surf = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA)
        
        # Main circle (colorful theme)
        pygame.draw.circle(surf, color, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2)
        pygame.draw.circle(surf, (255, 255, 255), (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2, 2)
        
        # Musical note symbol
        center_x, center_y = const.CELL_SIZE//2, const.CELL_SIZE//2
        note_color = (0, 0, 0)
        
        # Draw musical note
        pygame.draw.circle(surf, note_color, (center_x - 2, center_y + 4), 4)
        pygame.draw.rect(surf, note_color, (center_x + 2, center_y - 6, 2, 10))
        pygame.draw.arc(surf, note_color, (center_x + 2, center_y - 8, 8, 6), 0, math.pi, 2)
        
        return surf
    
    def get_valid_moves(self, board):
        """Bard moves 3 tiles in any direction (8-directional)"""
        self.board = board
        # Check if unit can move (status effects)
        if hasattr(self, 'status_manager') and not self.status_manager.can_move():
            return []
        if self.immobilized or self.stunned:
            return []
        
        valid_moves = []
        row, col = self.position
        
        # All 8 directions
        all_directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1), (0, 1), (1, -1), (1, 0), (1, 1)]
        
        for dr, dc in all_directions:
            # Move up to 3 tiles in each direction
            for distance in range(1, 4):  # 1, 2, and 3 tiles
                new_r, new_c = row + dr * distance, col + dc * distance
                
                # Check if position is on board
                if 0 <= new_r < const.BOARD_SIZE and 0 <= new_c < const.BOARD_SIZE:
                    # Check if position is empty
                    if (new_r, new_c) not in board.units:
                        valid_moves.append((new_r, new_c))
                    else:
                        # Can't move through units, stop checking further in this direction
                        break
                else:
                    # Off board, stop checking further in this direction
                    break
        
        return valid_moves
    
    def get_valid_attacks(self, board):
        """Bard basic attack: weak ranged sonic attack (4 tile range)"""
        self.board = board
        if self.stunned:
            return []
        
        valid_attacks = []
        row, col = self.position
        
        # Check all positions within 4 tile range
        for r in range(max(0, row - 4), min(const.BOARD_SIZE, row + 5)):
            for c in range(max(0, col - 4), min(const.BOARD_SIZE, col + 5)):
                if (r, c) == self.position:
                    continue
                
                # Calculate distance
                distance = max(abs(r - row), abs(c - col))
                if distance <= 4 and (r, c) in board.units:
                    target_unit = board.units[(r, c)]
                    if not target_unit.sanctuary:  # Can target any unit (friendly fire)
                        valid_attacks.append((r, c))
        
        return valid_attacks
    
    def get_ability_targets(self, ability_idx, board):
        """Get valid targets for Bard abilities"""
        self.board = board
        if ability_idx == 0:
            return self.get_valid_moves(board)
        if ability_idx == 1:
            return self.get_valid_attacks(board)
        
        ability_name = self.abilities[ability_idx].name
        
        if ability_name == "Inspire":
            # Ally-targeting ability (4 tile range)
            return self._get_ally_targets(board, range_limit=4)
        elif ability_name == "Song of Healing":
            # Self-target (affects area around Bard)
            return [self.position]
        elif ability_name in ["Discordant Note", "Bardic Knowledge", "Shatter"]:
            # Enemy-targeting abilities (4 tile range)
            return self._get_ranged_targets(board, range_limit=4)
        elif ability_name == "Mass Inspiration":
            # Self-target (affects all allies)
            return [self.position]
        
        return []
    
    def _get_ally_targets(self, board, range_limit=4):
        """Get allied units within range"""
        allies = []
        row, col = self.position
        
        # Check all positions within range
        for r in range(max(0, row - range_limit), min(const.BOARD_SIZE, row + range_limit + 1)):
            for c in range(max(0, col - range_limit), min(const.BOARD_SIZE, col + range_limit + 1)):
                if (r, c) == self.position:
                    continue
                
                # Calculate distance
                distance = max(abs(r - row), abs(c - col))
                if distance <= range_limit and (r, c) in board.units:
                    unit = board.units[(r, c)]
                    if unit.player_id == self.player_id and not unit.sanctuary:
                        allies.append((r, c))
        
        return allies
    
    def _get_ranged_targets(self, board, range_limit=4):
        """Get targets within range for ranged abilities"""
        targets = []
        row, col = self.position
        
        # Check all positions within range
        for r in range(max(0, row - range_limit), min(const.BOARD_SIZE, row + range_limit + 1)):
            for c in range(max(0, col - range_limit), min(const.BOARD_SIZE, col + range_limit + 1)):
                if (r, c) == self.position:
                    continue
                
                # Calculate distance
                distance = max(abs(r - row), abs(c - col))
                if distance <= range_limit and (r, c) in board.units:
                    target_unit = board.units[(r, c)]
                    if not target_unit.sanctuary:  # Can target any unit (friendly fire)
                        targets.append((r, c))
        
        return targets
    
    def use_ability(self, ability_idx, target_pos, game=None):
        """Use Bard ability with global AP system"""
        if game:
            self.board = game.board
        elif not self.board:
            print(f"ERROR in {self.name}.use_ability: game object not passed and self.board not set.")
            if ability_idx > 1:
                print(f"Cannot use special ability {self.abilities[ability_idx].name} without board context.")
                return False
        
        # Standard move or attack - call super() to use the updated base class logic
        if ability_idx == 0:  # Move
            return super().use_ability(ability_idx, target_pos, game)
        
        if ability_idx == 1:  # Basic Attack
            return super().use_ability(ability_idx, target_pos, game)
        
        # For other Bard-specific abilities - use global AP system
        return super().use_ability(ability_idx, target_pos, game)
    
    def _execute_bard_ability(self, ability, target_pos, game):
        """Execute Bard-specific abilities"""
        ability_name = ability.name
        
        if ability_name == "Inspire":
            return self._use_inspire(ability, target_pos, game)
        elif ability_name == "Song of Healing":
            return self._use_song_of_healing(ability)
        elif ability_name == "Discordant Note":
            return self._use_discordant_note(ability, target_pos)
        elif ability_name == "Bardic Knowledge":
            return self._use_bardic_knowledge(ability, target_pos)
        elif ability_name == "Mass Inspiration":
            return self._use_mass_inspiration(ability, game)
        elif ability_name == "Shatter":
            return self._use_shatter(ability, target_pos)
        else:
            print(f"Unknown Bard ability: {ability_name}")
            return False
    
    def _use_inspire(self, ability, target_pos, game):
        """Grant ally extra AP and damage bonus"""
        target_unit = self.board.units.get(target_pos)
        if target_unit and target_unit.player_id == self.player_id:
            # Grant 2 AP to target
            if game:
                game.current_player_ap += 2
                print(f"{self.name} inspires {target_unit.name} - granting 2 AP to the team")
            
            # Apply inspiration buff (+1 damage for 3 turns)
            if hasattr(target_unit, 'status_manager'):
                target_unit.status_manager.apply_effect("inspired", 3, self.player_id)
            else:
                target_unit.inspired = True
                target_unit.inspired_turns = 3
                target_unit.inspired_damage_bonus = 1
            
            self.inspiration_targets.add(target_unit)
            print(f"{target_unit.name} is inspired - +1 damage for 3 turns")
            return True
        return False
    
    def _use_song_of_healing(self, ability):
        """Heal all allies within 2 tiles"""
        heal_amount = getattr(ability, 'heal_amount', 2)
        healed_count = 0
        row, col = self.position
        
        # Check all positions within 2 tiles
        for r in range(max(0, row - 2), min(const.BOARD_SIZE, row + 3)):
            for c in range(max(0, col - 2), min(const.BOARD_SIZE, col + 3)):
                # Calculate distance
                distance = max(abs(r - row), abs(c - col))
                if distance <= 2 and (r, c) in self.board.units:
                    unit = self.board.units[(r, c)]
                    if unit.player_id == self.player_id:
                        old_health = unit.health
                        unit.health = min(unit.max_health, unit.health + heal_amount)
                        actual_heal = unit.health - old_health
                        if actual_heal > 0:
                            print(f"  {unit.name} heals {actual_heal} HP from Song of Healing")
                            healed_count += 1
        
        print(f"{self.name} plays Song of Healing, affecting {healed_count} allies")
        return healed_count > 0
    
    def _use_discordant_note(self, ability, target_pos):
        """Confuse enemy and deal damage"""
        target_unit = self.board.units.get(target_pos)
        if target_unit:
            damage = ability.damage if ability.damage > 0 else 2
            target_unit.take_damage(damage, self)
            
            # Apply confusion effect (random movement/targeting for 2 turns)
            if hasattr(target_unit, 'status_manager'):
                target_unit.status_manager.apply_effect("confused", 2, self.player_id)
            else:
                target_unit.confused = True
                target_unit.confused_turns = 2
            
            print(f"{target_unit.name} takes {damage} damage and is confused for 2 turns")
            return True
        return False
    
    def _use_bardic_knowledge(self, ability, target_pos):
        """Reveal enemy information"""
        target_unit = self.board.units.get(target_pos)
        if target_unit and target_unit not in self.bardic_knowledge_used:
            self.bardic_knowledge_used.add(target_unit)
            
            # Reveal information about the target
            print(f"{self.name} analyzes {target_unit.name}:")
            print(f"  Health: {target_unit.health}/{target_unit.max_health}")
            print(f"  AP: {getattr(target_unit, 'current_ap', 'Unknown')}/{getattr(target_unit, 'max_ap', 'Unknown')}")
            print(f"  Abilities: {len(target_unit.abilities)} total")
            
            # Show status effects
            if hasattr(target_unit, 'status_manager'):
                effects = target_unit.status_manager.get_active_effects()
                if effects:
                    print(f"  Status Effects: {', '.join(effects)}")
                else:
                    print(f"  Status Effects: None")
            
            # Grant tactical advantage (next attack on this target gets +2 damage)
            if hasattr(target_unit, 'analyzed_by_bard'):
                target_unit.analyzed_by_bard = True
            
            return True
        return False
    
    def _use_mass_inspiration(self, ability, game):
        """Buff all allies on the battlefield"""
        inspired_count = 0
        
        # Affect all allied units on the board
        for pos, unit in self.board.units.items():
            if unit.player_id == self.player_id and unit != self:
                # Apply inspiration buff
                if hasattr(unit, 'status_manager'):
                    unit.status_manager.apply_effect("mass_inspired", 4, self.player_id)
                else:
                    unit.mass_inspired = True
                    unit.mass_inspired_turns = 4
                    unit.mass_inspired_bonus = 1
                
                inspired_count += 1
                print(f"  {unit.name} gains Mass Inspiration (+1 to all actions for 4 turns)")
        
        # Grant team AP
        if game:
            game.current_player_ap += 3
            print(f"Mass Inspiration grants 3 AP to the team")
        
        print(f"{self.name} inspires {inspired_count} allies with Mass Inspiration")
        return inspired_count > 0
    
    def _use_shatter(self, ability, target_pos):
        """High damage sonic attack"""
        target_unit = self.board.units.get(target_pos)
        if target_unit:
            damage = ability.damage if ability.damage > 0 else 5
            
            # Extra damage if target is confused or stunned
            if (hasattr(target_unit, 'confused') and target_unit.confused) or target_unit.stunned:
                damage += 2
                print(f"Shatter deals extra damage to disoriented target!")
            
            target_unit.take_damage(damage, self)
            print(f"{self.name} shatters {target_unit.name} with sonic force for {damage} damage")
            return True
        return False
    
    def reset_turn(self, game=None):
        """Reset turn-specific effects"""
        super().reset_turn(game=game)

        # Clear inspiration targets that are no longer inspired
        expired_targets = set()
        for target in self.inspiration_targets:
            if not (hasattr(target, 'inspired') and target.inspired):
                expired_targets.add(target)

        self.inspiration_targets -= expired_targets

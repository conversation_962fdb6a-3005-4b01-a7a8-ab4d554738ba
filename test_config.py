import unittest
import pygame
import sys
from unittest.mock import patch, MagicMock

# Import the Game class and the new config module
from game_state import Game
import game_config as config

class TestConfig(unittest.TestCase):
    """Tests for the consolidated game configuration."""

    def setUp(self):
        # Initialize pygame for tests that might need it
        pygame.init()
        # Mock pygame.display.set_mode to prevent actual window creation
        self.mock_set_mode = MagicMock(return_value=pygame.Surface((1, 1)))
        self.patcher = patch('pygame.display.set_mode', new=self.mock_set_mode)
        self.patcher.start()

    def tearDown(self):
        self.patcher.stop()
        pygame.quit()

    def test_config_values_accessible(self):
        """Verify that basic config values are accessible."""
        self.assertEqual(config.WINDOW_WIDTH, 1280)
        self.assertEqual(config.FPS, 60)
        self.assertEqual(config.BOARD_SIZE, 9)
        self.assertEqual(config.PLAYER1_COLOR, (0, 100, 200))
        self.assertEqual(config.GAME_SETTINGS["global"]["move_ap_cost"], 1)
        self.assertEqual(config.SELECTED_ABILITIES["Warrior"], [0, 1, 2])
        self.assertEqual(config.RESOLUTION, (1920, 1080))
        self.assertTrue(config.FULLSCREEN)

    def test_game_initialization_with_config(self):
        """Verify that the Game class initializes using config values."""
        game_instance = Game(fullscreen=False)

        # Check if screen was set with correct dimensions from config
        self.mock_set_mode.assert_called_with((config.WINDOW_WIDTH, config.WINDOW_HEIGHT), 0)

        # Check if fonts are initialized using config values
        self.assertIsNotNone(game_instance.title_font)
        self.assertIsNotNone(game_instance.ui_font)
        self.assertIsNotNone(game_instance.small_font)
        self.assertIsNotNone(game_instance.tiny_font)

        # Check initial game state from config
        self.assertEqual(game_instance.state, config.STATE_SETUP)
        self.assertEqual(game_instance.available_units, config.available_units)

if __name__ == '__main__':
    unittest.main()

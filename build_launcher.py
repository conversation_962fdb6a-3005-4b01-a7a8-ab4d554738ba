import os
import subprocess
import sys

def build_launcher():
    """Build the executable launcher using PyInstaller"""
    print("Building launcher executable...")
    
    # Create spec file command
    spec_cmd = [
        'pyinstaller',
        '--name=TacticalPvPStrategy',
        '--onefile',
        '--windowed',
        '--clean',
        'launcher.py'
    ]
    
    # Add icon if it exists
    if os.path.exists('icon.ico'):
        spec_cmd.append('--icon=icon.ico')
    
    # Run PyInstaller
    try:
        subprocess.check_call(spec_cmd)
        print("Launcher built successfully!")
        print(f"Executable created at: {os.path.abspath(os.path.join('dist', 'TacticalPvPStrategy.exe'))}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error building launcher: {e}")
        return False
    except FileNotFoundError:
        print("PyInstaller not found. Make sure it's installed and in your PATH.")
        print("You can install it with: pip install pyinstaller")
        return False

if __name__ == "__main__":
    build_launcher()

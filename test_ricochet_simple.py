#!/usr/bin/env python3
"""
Simple test for Ricochet Shot debugging
"""

import pygame
from game_state import Game
from units.hunter import Hunter
from units.warrior import Warrior

def test_simple_ricochet():
    """Test a simple ricochet scenario"""
    pygame.init()
    
    print("🏹 SIMPLE RICOCHET SHOT TEST 🏹")
    print("=" * 40)
    
    game = Game()
    hunter = Hunter(1)
    warrior = Warrior(2)
    
    # Simple setup
    hunter.position = (4, 4)
    warrior.position = (2, 6)
    
    hunter.board = game.board
    game.board.units[(4, 4)] = hunter
    game.board.units[(2, 6)] = warrior
    
    print(f"Hunter at: {hunter.position}")
    print(f"Warrior at: {warrior.position}")
    
    # Test targeting
    targets = hunter._get_ricochet_shot_targets(game.board)
    print(f"Available targets: {len(targets)}")
    print(f"Sample targets: {targets[:3]}")
    
    # Test simple execution
    print(f"\n🎯 Executing Ricochet Shot towards (1, 7)...")
    try:
        result = hunter.use_ability(2, (1, 7), game)
        print(f"✅ Result: {result}")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_ricochet()

#!/usr/bin/env python3
"""
Test script for the passive system
"""

import pygame
from game_state import Game
from units.warrior import Warrior
from units.hunter import Hunter
from units.rogue import Rogue
from units.mage import Mage
from passive_system import PASSIVE_ABILITIES, PassiveType, get_available_passives_for_class
from game_settings import SELECTED_PASSIVES

def test_passive_system():
    """Test the passive ability system"""
    pygame.init()
    
    print("🛡️ PASSIVE SYSTEM TEST 🛡️")
    print("=" * 40)
    
    # Test 1: Check available passives for each class
    print("\n📋 TEST 1: Available Passives by Class")
    print("-" * 40)
    
    classes = ["Warrior", "<PERSON>", "<PERSON>", "Mage", "Cleric", "King", "Pawn"]
    for class_name in classes:
        available = get_available_passives_for_class(class_name)
        print(f"{class_name}:")
        for passive in available:
            restriction = f" ({passive.class_restriction})" if passive.class_restriction else " (Universal)"
            print(f"  • {passive.name}{restriction}: {passive.description}")
        print()
    
    # Test 2: Test Warrior knockback immunity
    print("\n📋 TEST 2: Warrior Knockback Immunity")
    print("-" * 35)
    
    game = Game()
    warrior = Warrior(1)
    hunter = Hunter(2)
    
    # Set up positions
    warrior.position = (4, 4)
    hunter.position = (3, 3)
    
    warrior.board = game.board
    hunter.board = game.board
    game.board.units = {(4, 4): warrior, (3, 3): hunter}
    
    # Add knockback immunity passive to warrior
    knockback_immunity = PASSIVE_ABILITIES[PassiveType.KNOCKBACK_IMMUNITY]
    warrior.passive_manager.add_passive(knockback_immunity)
    
    print(f"Warrior at {warrior.position}")
    print(f"Hunter at {hunter.position}")
    print(f"Warrior has knockback immunity: {warrior.passive_manager.has_passive(PassiveType.KNOCKBACK_IMMUNITY)}")
    
    # Test knockback shot
    print(f"\n🎯 Hunter uses Knockback Shot on Warrior...")
    original_pos = warrior.position
    result = hunter.use_ability(4, warrior.position, game)  # Knockback Shot is index 4
    
    print(f"Knockback Shot result: {result}")
    print(f"Warrior position after knockback: {warrior.position}")
    print(f"✅ Knockback immunity works: {warrior.position == original_pos}")
    
    # Test 3: Test damage reduction passive
    print("\n\n📋 TEST 3: Damage Reduction Passive")
    print("-" * 35)
    
    warrior2 = Warrior(1)
    warrior2.position = (5, 5)
    warrior2.board = game.board
    game.board.units[(5, 5)] = warrior2
    
    # Add damage reduction passive
    damage_reduction = PASSIVE_ABILITIES[PassiveType.DAMAGE_REDUCTION]
    warrior2.passive_manager.add_passive(damage_reduction)
    
    print(f"Warrior2 health before damage: {warrior2.health}")
    print(f"Warrior2 has damage reduction: {warrior2.passive_manager.has_passive(PassiveType.DAMAGE_REDUCTION)}")
    
    # Deal 3 damage (should be reduced to 2)
    warrior2.take_damage(3)
    
    print(f"Warrior2 health after 3 damage: {warrior2.health}")
    expected_health = 7 - 2  # 7 base health - (3 damage - 1 reduction)
    print(f"✅ Damage reduction works: {warrior2.health == expected_health}")
    
    # Test 4: Test regeneration passive
    print("\n\n📋 TEST 4: Regeneration Passive")
    print("-" * 30)
    
    mage = Mage(1)
    mage.position = (6, 6)
    mage.board = game.board
    game.board.units[(6, 6)] = mage
    
    # Damage the mage first
    mage.take_damage(2)
    print(f"Mage health after damage: {mage.health}")
    
    # Add regeneration passive
    regeneration = PASSIVE_ABILITIES[PassiveType.REGENERATION]
    mage.passive_manager.add_passive(regeneration)
    
    print(f"Mage has regeneration: {mage.passive_manager.has_passive(PassiveType.REGENERATION)}")
    
    # Trigger turn start (should heal 1 HP)
    old_health = mage.health
    mage.reset_ap(game)
    
    print(f"Mage health after turn start: {mage.health}")
    print(f"✅ Regeneration works: {mage.health == old_health + 1}")
    
    # Test 5: Test extra AP passive
    print("\n\n📋 TEST 5: Extra AP Passive")
    print("-" * 25)
    
    mage2 = Mage(1)
    mage2.position = (7, 7)
    
    original_max_ap = mage2.max_ap
    print(f"Mage original max AP: {original_max_ap}")
    
    # Add extra AP passive
    extra_ap = PASSIVE_ABILITIES[PassiveType.EXTRA_AP]
    mage2.passive_manager.add_passive(extra_ap)
    
    print(f"Mage has extra AP: {mage2.passive_manager.has_passive(PassiveType.EXTRA_AP)}")
    
    # Reset AP (should apply passive bonus)
    mage2.reset_ap(game)
    
    print(f"Mage max AP after passive: {mage2.max_ap}")
    print(f"Mage current AP: {mage2.current_ap}")
    print(f"✅ Extra AP works: {mage2.max_ap == original_max_ap + 1}")
    
    # Test 6: Test default passive selections
    print("\n\n📋 TEST 6: Default Passive Selections")
    print("-" * 35)
    
    print("Default passive selections:")
    for class_name, passives in SELECTED_PASSIVES.items():
        print(f"  {class_name}: {passives}")
    
    # Test 7: Test multiple passives
    print("\n\n📋 TEST 7: Multiple Passives")
    print("-" * 25)
    
    warrior3 = Warrior(1)
    warrior3.position = (8, 8)
    
    # Add multiple passives
    warrior3.passive_manager.add_passive(PASSIVE_ABILITIES[PassiveType.KNOCKBACK_IMMUNITY])
    warrior3.passive_manager.add_passive(PASSIVE_ABILITIES[PassiveType.DAMAGE_REDUCTION])
    warrior3.passive_manager.add_passive(PASSIVE_ABILITIES[PassiveType.REGENERATION])
    
    print(f"Warrior3 passive descriptions:")
    for desc in warrior3.passive_manager.get_passive_descriptions():
        print(f"  • {desc}")
    
    print(f"Total passives: {len(warrior3.passive_manager.passive_abilities)}")
    print(f"✅ Multiple passives work: {len(warrior3.passive_manager.passive_abilities) == 3}")
    
    print("\n" + "=" * 40)
    print("🎉 PASSIVE SYSTEM TESTS COMPLETED!")
    print("\n✅ Features Verified:")
    print("  • Passive abilities can be added to units")
    print("  • Knockback immunity prevents knockback")
    print("  • Damage reduction reduces incoming damage")
    print("  • Regeneration heals at turn start")
    print("  • Extra AP increases maximum AP")
    print("  • Multiple passives can be active")
    print("  • Class-specific and universal passives")
    print("  • Default passive selections loaded")
    
    print("\n🛡️ Passive system ready for integration!")

if __name__ == "__main__":
    test_passive_system()

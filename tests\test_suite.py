#!/usr/bin/env python3
"""
Comprehensive Test Suite for the Tactical PvP Strategy Game
This file contains systematic unit tests, integration tests, and regression tests.
"""

import sys
import os
import unittest
import pygame

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from game_state import Game
from units.hunter import Hunter
from units.warrior import Warrior
from units.rogue import Rogue
from units.mage import Mage
from units.cleric import Cleric
from units.king import King
from units.pawn import Pawn
from passive_system import PassiveType, PASSIVE_ABILITIES
from game_config import GAME_CONFIG

class TestUnitCore(unittest.TestCase):
    """Test core unit functionality"""
    
    def setUp(self):
        """Set up test environment"""
        pygame.init()
        self.game = Game()
        
    def test_unit_creation(self):
        """Test that all unit types can be created"""
        units = [
            <PERSON>(1), <PERSON>(1), <PERSON>(1), <PERSON><PERSON>(1),
            <PERSON><PERSON><PERSON>(1), <PERSON>(1), Pawn(1)
        ]
        
        for unit in units:
            self.assertIsNotNone(unit)
            self.assertEqual(unit.player_id, 1)
            self.assertGreater(unit.health, 0)
            self.assertGreater(unit.max_health, 0)
            self.assertGreaterEqual(unit.current_ap, 0)
            self.assertGreater(unit.max_ap, 0)
            self.assertIsNotNone(unit.abilities)
            self.assertGreaterEqual(len(unit.abilities), 2)  # At least Move and Attack
    
    def test_unit_health_system(self):
        """Test health and damage system"""
        warrior = Warrior(1)
        initial_health = warrior.health
        
        # Test taking damage
        warrior.take_damage(2)
        self.assertEqual(warrior.health, initial_health - 2)
        
        # Test death
        warrior.take_damage(100)
        self.assertFalse(warrior.is_alive())
        
    def test_unit_ap_system(self):
        """Test AP (Action Point) system"""
        hunter = Hunter(1)
        initial_ap = hunter.current_ap
        
        # Test AP consumption
        hunter.current_ap -= 2
        self.assertEqual(hunter.current_ap, initial_ap - 2)
        
        # Test AP reset
        hunter.reset_ap(self.game)
        self.assertEqual(hunter.current_ap, hunter.max_ap)

class TestMovementSystem(unittest.TestCase):
    """Test movement mechanics for all unit types"""
    
    def setUp(self):
        pygame.init()
        self.game = Game()
        
    def test_hunter_movement(self):
        """Test Hunter diagonal movement"""
        hunter = Hunter(1)
        hunter.position = (4, 4)
        hunter.board = self.game.board
        self.game.board.units = {(4, 4): hunter}
        
        moves = hunter.get_valid_moves(self.game.board)
        self.assertGreater(len(moves), 0)
        
        # All moves should be diagonal
        for move in moves:
            dr, dc = move[0] - 4, move[1] - 4
            self.assertEqual(abs(dr), abs(dc))  # Diagonal movement
            self.assertNotEqual(dr, 0)  # Not staying in place
    
    def test_warrior_movement(self):
        """Test Warrior orthogonal movement"""
        warrior = Warrior(1)
        warrior.position = (4, 4)
        warrior.board = self.game.board
        self.game.board.units = {(4, 4): warrior}
        
        moves = warrior.get_valid_moves(self.game.board)
        self.assertGreater(len(moves), 0)
        
        # All moves should be orthogonal
        for move in moves:
            dr, dc = move[0] - 4, move[1] - 4
            self.assertTrue((dr == 0 and dc != 0) or (dr != 0 and dc == 0))
    
    def test_rogue_knight_movement(self):
        """Test Rogue L-shaped knight movement"""
        rogue = Rogue(1)
        rogue.position = (4, 4)
        rogue.board = self.game.board
        self.game.board.units = {(4, 4): rogue}
        
        moves = rogue.get_valid_moves(self.game.board)
        self.assertGreater(len(moves), 0)
        
        # Check for L-shaped moves
        l_shaped_moves = []
        for move in moves:
            dr, dc = abs(move[0] - 4), abs(move[1] - 4)
            if (dr == 2 and dc == 1) or (dr == 1 and dc == 2):
                l_shaped_moves.append(move)
        
        self.assertGreater(len(l_shaped_moves), 0, "Rogue should have L-shaped moves")

class TestAbilitySystem(unittest.TestCase):
    """Test ability mechanics"""
    
    def setUp(self):
        pygame.init()
        self.game = Game()
        
    def test_hunter_abilities(self):
        """Test all Hunter abilities"""
        hunter = Hunter(1)
        hunter.position = (4, 4)
        hunter.board = self.game.board
        self.game.board.units = {(4, 4): hunter}
        
        # Test that all abilities exist
        expected_abilities = [
            "Move", "Basic Attack", "Ricochet Shot", "Triple Shot",
            "Knockback Shot", "Multishot", "Crippling Shot", "Piercing Shot"
        ]
        
        self.assertEqual(len(hunter.abilities), len(expected_abilities))
        
        for i, expected_name in enumerate(expected_abilities):
            self.assertEqual(hunter.abilities[i].name, expected_name)
            
        # Test ability usage
        self.assertTrue(hunter.can_use_ability(0))  # Move
        self.assertTrue(hunter.can_use_ability(1))  # Basic Attack
    
    def test_ability_ap_costs(self):
        """Test that abilities have reasonable AP costs"""
        hunter = Hunter(1)
        
        for ability in hunter.abilities:
            self.assertGreater(ability.ap_cost, 0)
            self.assertLessEqual(ability.ap_cost, 10)  # Reasonable upper bound

class TestPassiveSystem(unittest.TestCase):
    """Test passive ability system"""
    
    def setUp(self):
        pygame.init()
        self.game = Game()
        
    def test_passive_manager_creation(self):
        """Test that all units have passive managers"""
        units = [Hunter(1), Warrior(1), Rogue(1), Mage(1)]
        
        for unit in units:
            self.assertTrue(hasattr(unit, 'passive_manager'))
            self.assertIsNotNone(unit.passive_manager)
    
    def test_knockback_immunity(self):
        """Test Warrior knockback immunity passive"""
        warrior = Warrior(1)
        hunter = Hunter(1)
        
        # Set up positions
        warrior.position = (4, 4)
        hunter.position = (3, 3)
        warrior.board = self.game.board
        hunter.board = self.game.board
        self.game.board.units = {(4, 4): warrior, (3, 3): hunter}
        
        # Add knockback immunity
        knockback_immunity = PASSIVE_ABILITIES[PassiveType.KNOCKBACK_IMMUNITY]
        warrior.passive_manager.add_passive(knockback_immunity)
        
        # Test knockback
        original_pos = warrior.position
        hunter.use_ability(4, warrior.position, self.game)  # Knockback Shot
        
        self.assertEqual(warrior.position, original_pos, "Warrior should be immune to knockback")
    
    def test_damage_reduction_passive(self):
        """Test damage reduction passive"""
        warrior = Warrior(1)
        damage_reduction = PASSIVE_ABILITIES[PassiveType.DAMAGE_REDUCTION]
        warrior.passive_manager.add_passive(damage_reduction)
        
        initial_health = warrior.health
        warrior.take_damage(3)
        
        # Should take 2 damage instead of 3 (3 - 1 reduction)
        expected_health = initial_health - 2
        self.assertEqual(warrior.health, expected_health)

class TestGameIntegration(unittest.TestCase):
    """Test integration between different systems"""
    
    def setUp(self):
        pygame.init()
        self.game = Game()
        
    def test_unit_filtering_applies_passives(self):
        """Test that unit filtering applies selected passives"""
        warrior = Warrior(1)
        
        # Apply filtering (which should add passives)
        self.game._filter_unit_abilities(warrior)
        
        # Check that warrior got knockback immunity (default passive)
        self.assertTrue(warrior.passive_manager.has_passive(PassiveType.KNOCKBACK_IMMUNITY))
    
    def test_movement_range_configuration(self):
        """Test that movement range configuration works"""
        hunter = Hunter(1)
        hunter.position = (4, 4)
        hunter.board = self.game.board
        self.game.board.units = {(4, 4): hunter}
        
        # Get movement range from config
        config_range = GAME_CONFIG.get("hunter_config", {}).get("movement_range", 1)
        
        # Get actual moves
        moves = hunter.get_valid_moves(self.game.board)
        
        if moves:
            max_distance = max([max(abs(move[0] - 4), abs(move[1] - 4)) for move in moves])
            self.assertLessEqual(max_distance, config_range)

class TestRegressionSuite(unittest.TestCase):
    """Regression tests to prevent breaking existing functionality"""
    
    def setUp(self):
        pygame.init()
        self.game = Game()
        
    def test_all_units_can_move(self):
        """Regression test: All units should be able to move"""
        unit_classes = [Hunter, Warrior, Rogue, Mage, Cleric, King, Pawn]
        
        for UnitClass in unit_classes:
            with self.subTest(unit=UnitClass.__name__):
                unit = UnitClass(1)
                unit.position = (4, 4)
                unit.board = self.game.board
                self.game.board.units = {(4, 4): unit}
                
                moves = unit.get_valid_moves(self.game.board)
                self.assertGreater(len(moves), 0, f"{UnitClass.__name__} should have valid moves")
    
    def test_all_units_have_basic_abilities(self):
        """Regression test: All units should have Move and Basic Attack"""
        unit_classes = [Hunter, Warrior, Rogue, Mage, Cleric, King, Pawn]
        
        for UnitClass in unit_classes:
            with self.subTest(unit=UnitClass.__name__):
                unit = UnitClass(1)
                
                self.assertGreaterEqual(len(unit.abilities), 2)
                self.assertEqual(unit.abilities[0].name, "Move")
                self.assertEqual(unit.abilities[1].name, "Basic Attack")
    
    def test_config_system_integrity(self):
        """Regression test: Config system should be intact"""
        # Test that all expected config sections exist
        expected_sections = [
            "hunter_config", "warrior_config", "rogue_config",
            "mage_config", "cleric_config", "king_config", "pawn_config"
        ]
        
        for section in expected_sections:
            self.assertIn(section, GAME_CONFIG)
            self.assertIn("movement_range", GAME_CONFIG[section])

def run_all_tests():
    """Run all tests and return results"""
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add all test classes
    test_classes = [
        TestUnitCore, TestMovementSystem, TestAbilitySystem,
        TestPassiveSystem, TestGameIntegration, TestRegressionSuite
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    return result

if __name__ == "__main__":
    print("RUNNING COMPREHENSIVE TEST SUITE")
    print("=" * 50)
    
    result = run_all_tests()
    
    print("\n" + "=" * 50)
    print("TEST RESULTS SUMMARY")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")

    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0]}")

    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('\\n')[-2]}")

    if result.wasSuccessful():
        print("\nALL TESTS PASSED!")
    else:
        print(f"\n{len(result.failures + result.errors)} TESTS FAILED")

    print("\nTest suite complete!")

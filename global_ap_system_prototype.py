#!/usr/bin/env python3
"""
PROTOTYPE: Global AP System Implementation
This shows how the new AP system would work before implementing it in the main game.
"""

import pygame
from game_state import Game
from units.warrior import Warrior
from units.mage import Mage
from units.cleric import Cleric

class GlobalAPGame(Game):
    """Extended Game class with global AP system"""
    
    def __init__(self, fullscreen=False):
        super().__init__(fullscreen)
        
        # Global AP system
        self.max_ap_per_turn = 12  # Configurable
        self.current_player_ap = 12
        self.units_acted_this_turn = set()
        
        # Track unit actions
        self.unit_action_log = []  # For debugging/analysis
    
    def get_current_player_ap(self):
        """Get remaining AP for current player"""
        return self.current_player_ap
    
    def spend_ap(self, amount, unit=None):
        """Spend AP from global pool"""
        if self.current_player_ap >= amount:
            self.current_player_ap -= amount
            if unit:
                self.units_acted_this_turn.add(unit)
                self.unit_action_log.append({
                    'unit': unit.name,
                    'position': unit.position,
                    'ap_cost': amount,
                    'remaining_ap': self.current_player_ap
                })
            return True
        return False
    
    def can_unit_act(self, unit):
        """Check if unit can perform an action"""
        # Basic check: unit hasn't acted this turn
        if unit in self.units_acted_this_turn:
            return False
        
        # Check if unit has any affordable actions
        min_cost = min(ability.ap_cost for ability in unit.abilities)
        return self.current_player_ap >= min_cost
    
    def reset_turn_ap(self):
        """Reset AP and action tracking for new turn"""
        self.current_player_ap = self.max_ap_per_turn
        self.units_acted_this_turn.clear()
        self.unit_action_log.clear()
        
        # Reset unit action flags
        for unit in self.board.units.values():
            if unit.player_id == self.current_player:
                unit.has_acted_this_turn = False

class GlobalAPUnit:
    """Mixin class to add global AP support to units"""
    
    def __init__(self):
        self.has_acted_this_turn = False
        self.can_act_multiple_times = False  # For special abilities
    
    def use_ability_global_ap(self, ability_idx, target_pos, game):
        """Use ability with global AP system"""
        if not isinstance(game, GlobalAPGame):
            # Fallback to old system
            return self.use_ability(ability_idx, target_pos, game)
        
        # Check if unit can act
        if self.has_acted_this_turn and not self.can_act_multiple_times:
            print(f"{self.name} has already acted this turn!")
            return False
        
        # Get ability and check AP cost
        if ability_idx >= len(self.abilities):
            return False
        
        ability = self.abilities[ability_idx]
        ap_cost = ability.ap_cost
        
        # Check global AP
        if game.current_player_ap < ap_cost:
            print(f"Not enough AP! Need {ap_cost}, have {game.current_player_ap}")
            return False
        
        # Execute ability using original method
        success = self.use_ability(ability_idx, target_pos, game)
        
        if success:
            # Deduct from global AP instead of unit AP
            game.spend_ap(ap_cost, self)
            self.has_acted_this_turn = True
            print(f"{self.name} used {ability.name} for {ap_cost} AP. Remaining: {game.current_player_ap}")
        
        return success

def test_global_ap_system():
    """Test the global AP system prototype"""
    pygame.init()
    
    print("🎮 TESTING GLOBAL AP SYSTEM PROTOTYPE 🎮")
    print("=" * 48)
    
    # Test 1: Basic AP Management
    print("📋 TEST 1: Basic AP Management")
    print("-" * 30)
    
    game = GlobalAPGame()
    
    print(f"Initial AP: {game.get_current_player_ap()}")
    
    # Test spending AP
    success = game.spend_ap(3)
    print(f"Spend 3 AP: {success}, Remaining: {game.get_current_player_ap()}")
    
    success = game.spend_ap(15)  # Should fail
    print(f"Spend 15 AP: {success}, Remaining: {game.get_current_player_ap()}")
    
    # Test 2: Unit Action Tracking
    print(f"\n📋 TEST 2: Unit Action Tracking")
    print("-" * 33)
    
    warrior = Warrior(1)
    mage = Mage(1)
    
    # Add global AP methods to units
    warrior.__class__ = type('GlobalAPWarrior', (GlobalAPUnit, Warrior), {})
    mage.__class__ = type('GlobalAPMage', (GlobalAPUnit, Mage), {})
    
    # Initialize global AP attributes
    warrior.__init__()
    mage.__init__()
    
    # Position units
    warrior.position = (4, 4)
    mage.position = (4, 5)
    
    # Set up board
    game.board.units = {
        (4, 4): warrior,
        (4, 5): mage
    }
    warrior.board = game.board
    mage.board = game.board
    
    print(f"Can warrior act: {game.can_unit_act(warrior)}")
    print(f"Can mage act: {game.can_unit_act(mage)}")
    
    # Test 3: Ability Usage with Global AP
    print(f"\n📋 TEST 3: Ability Usage with Global AP")
    print("-" * 38)
    
    # Reset AP for clean test
    game.reset_turn_ap()
    print(f"Turn start AP: {game.get_current_player_ap()}")
    
    # Warrior uses movement (1 AP)
    print(f"\n🚶 Warrior attempts to move...")
    move_success = warrior.use_ability_global_ap(0, (4, 3), game)
    print(f"Move result: {move_success}")
    print(f"AP after move: {game.get_current_player_ap()}")
    print(f"Warrior acted: {warrior.has_acted_this_turn}")
    
    # Warrior tries to act again (should fail)
    print(f"\n⚔️ Warrior attempts second action...")
    attack_success = warrior.use_ability_global_ap(1, (4, 2), game)
    print(f"Second action result: {attack_success}")
    
    # Mage uses ability (3 AP)
    print(f"\n🔮 Mage attempts to use Fireball...")
    fireball_success = mage.use_ability_global_ap(2, (3, 5), game)
    print(f"Fireball result: {fireball_success}")
    print(f"AP after Fireball: {game.get_current_player_ap()}")
    
    # Test 4: Turn Transition
    print(f"\n📋 TEST 4: Turn Transition")
    print("-" * 25)
    
    print(f"Before turn end:")
    print(f"  AP: {game.get_current_player_ap()}")
    print(f"  Units acted: {len(game.units_acted_this_turn)}")
    print(f"  Action log: {len(game.unit_action_log)} actions")
    
    # Simulate turn end
    game.reset_turn_ap()
    
    print(f"\nAfter turn reset:")
    print(f"  AP: {game.get_current_player_ap()}")
    print(f"  Units acted: {len(game.units_acted_this_turn)}")
    print(f"  Warrior can act: {game.can_unit_act(warrior)}")
    print(f"  Mage can act: {game.can_unit_act(mage)}")
    
    # Test 5: AP Exhaustion
    print(f"\n📋 TEST 5: AP Exhaustion")
    print("-" * 24)
    
    # Use up all AP
    remaining_ap = game.get_current_player_ap()
    print(f"Starting AP: {remaining_ap}")
    
    # Spend AP in chunks
    while game.get_current_player_ap() > 0:
        spent = min(3, game.get_current_player_ap())
        game.spend_ap(spent)
        print(f"Spent {spent} AP, remaining: {game.get_current_player_ap()}")
    
    # Try to spend more (should fail)
    final_attempt = game.spend_ap(1)
    print(f"Attempt to spend 1 more AP: {final_attempt}")
    
    print(f"\n" + "=" * 48)
    print("🎯 GLOBAL AP SYSTEM PROTOTYPE SUMMARY")
    print("-" * 35)
    print("✅ Global AP pool management")
    print("✅ One action per unit enforcement")
    print("✅ AP cost validation")
    print("✅ Turn transition handling")
    print("✅ AP exhaustion protection")
    
    print("\n🚀 Ready for full implementation!")
    print("   This prototype shows the system works correctly.")

if __name__ == "__main__":
    test_global_ap_system()

#!/usr/bin/env python3
"""
Test the implemented global AP system
Verify that the new AP scaling and one-action-per-unit system works correctly
"""

import pygame
from game_state import Game
from units.warrior import Warrior
from units.mage import Mage
from units.cleric import <PERSON><PERSON><PERSON>

def test_global_ap_implementation():
    """Test the implemented global AP system"""
    pygame.init()
    
    print("🎮 TESTING IMPLEMENTED GLOBAL AP SYSTEM 🎮")
    print("=" * 48)
    
    # Test 1: AP Progression Calculation
    print("📋 TEST 1: AP Progression Calculation")
    print("-" * 36)
    
    game = Game()
    
    # Test AP calculation for different turns
    print("Turn | P1 AP | P2 AP | Difference")
    print("-" * 35)
    for turn in range(1, 11):
        p1_ap = game.calculate_turn_ap(turn, 1)
        p2_ap = game.calculate_turn_ap(turn, 2)
        diff = p2_ap - p1_ap
        print(f"{turn:4d} | {p1_ap:5d} | {p2_ap:5d} | {diff:+10d}")
    
    # Verify expected values
    assert game.calculate_turn_ap(1, 1) == 1, "Turn 1 P1 should have 1 AP"
    assert game.calculate_turn_ap(1, 2) == 2, "Turn 1 P2 should have 2 AP (bonus)"
    assert game.calculate_turn_ap(5, 1) == 5, "Turn 5 P1 should have 5 AP"
    assert game.calculate_turn_ap(5, 2) == 5, "Turn 5 P2 should have 5 AP (no bonus)"
    assert game.calculate_turn_ap(10, 1) == 10, "Turn 10 P1 should have 10 AP (max)"
    assert game.calculate_turn_ap(15, 1) == 10, "Turn 15 P1 should be capped at 10 AP"
    
    print("✅ AP progression calculation working correctly")
    
    # Test 2: Turn Initialization
    print(f"\n📋 TEST 2: Turn Initialization")
    print("-" * 29)
    
    # Start Player 1's turn
    game.turn_count = 3
    ap_returned = game.start_player_turn(1)
    
    print(f"Turn {game.turn_count}, Player 1:")
    print(f"  Expected AP: {game.calculate_turn_ap(3, 1)}")
    print(f"  Actual AP: {game.current_player_ap}")
    print(f"  Returned AP: {ap_returned}")
    
    assert game.current_player_ap == 3, "Player 1 turn 3 should have 3 AP"
    assert ap_returned == 3, "start_player_turn should return correct AP"
    
    # Start Player 2's turn (same turn number)
    ap_returned = game.start_player_turn(2)
    
    print(f"\nTurn {game.turn_count}, Player 2:")
    print(f"  Expected AP: {game.calculate_turn_ap(3, 2)}")
    print(f"  Actual AP: {game.current_player_ap}")
    print(f"  Returned AP: {ap_returned}")
    
    assert game.current_player_ap == 4, "Player 2 turn 3 should have 4 AP (bonus)"
    assert ap_returned == 4, "start_player_turn should return correct AP"
    
    print("✅ Turn initialization working correctly")
    
    # Test 3: Unit Action Tracking
    print(f"\n📋 TEST 3: Unit Action Tracking")
    print("-" * 31)
    
    # Create test units
    warrior = Warrior(1)
    mage = Mage(1)
    
    # Position units
    warrior.position = (4, 4)
    mage.position = (4, 5)
    
    # Set up board
    game.board.units = {
        (4, 4): warrior,
        (4, 5): mage
    }
    
    # Start a turn with enough AP
    game.start_player_turn(1)
    game.current_player_ap = 6  # Set enough AP for testing
    
    print(f"Initial state:")
    print(f"  Game AP: {game.current_player_ap}")
    print(f"  Warrior acted: {warrior.has_acted_this_turn}")
    print(f"  Mage acted: {mage.has_acted_this_turn}")
    print(f"  Units acted: {len(game.units_acted_this_turn)}")
    
    # Check if units can act
    warrior_can_act = game.can_unit_act(warrior)
    mage_can_act = game.can_unit_act(mage)
    
    print(f"\nCan act check:")
    print(f"  Warrior can act: {warrior_can_act}")
    print(f"  Mage can act: {mage_can_act}")
    
    assert warrior_can_act, "Warrior should be able to act initially"
    assert mage_can_act, "Mage should be able to act initially"
    
    print("✅ Unit action tracking working correctly")
    
    # Test 4: Ability Usage with Global AP
    print(f"\n📋 TEST 4: Ability Usage with Global AP")
    print("-" * 37)
    
    # Set board references
    warrior.board = game.board
    mage.board = game.board
    
    # Test warrior movement (should cost 1 AP)
    print(f"Before warrior move:")
    print(f"  Game AP: {game.current_player_ap}")
    print(f"  Warrior acted: {warrior.has_acted_this_turn}")
    
    # Attempt to move warrior
    move_success = warrior.use_ability(0, (4, 3), game)
    
    print(f"\nAfter warrior move:")
    print(f"  Move success: {move_success}")
    print(f"  Game AP: {game.current_player_ap}")
    print(f"  Warrior acted: {warrior.has_acted_this_turn}")
    print(f"  Units acted: {len(game.units_acted_this_turn)}")
    
    if move_success:
        assert game.current_player_ap == 5, "AP should be reduced by 1"
        assert warrior.has_acted_this_turn, "Warrior should be marked as acted"
        assert warrior in game.units_acted_this_turn, "Warrior should be in acted set"
        print("✅ Warrior movement with global AP working")
    else:
        print("❌ Warrior movement failed")
    
    # Test warrior trying to act again (should fail)
    print(f"\nTesting second action by warrior:")
    second_action = warrior.use_ability(1, (3, 3), game)
    
    print(f"  Second action success: {second_action}")
    print(f"  Game AP: {game.current_player_ap}")
    
    assert not second_action, "Warrior should not be able to act twice"
    print("✅ One action per unit enforcement working")
    
    # Test mage action (should still work)
    print(f"\nTesting mage action:")
    mage_action = mage.use_ability(0, (4, 6), game)
    
    print(f"  Mage action success: {mage_action}")
    print(f"  Game AP: {game.current_player_ap}")
    print(f"  Mage acted: {mage.has_acted_this_turn}")
    
    if mage_action:
        print("✅ Different units can act in same turn")
    else:
        print("❌ Mage action failed")
    
    # Test 5: AP Exhaustion
    print(f"\n📋 TEST 5: AP Exhaustion")
    print("-" * 23)
    
    # Create new game with low AP
    game2 = Game()
    cleric = Cleric(1)
    cleric.position = (4, 4)
    cleric.board = game2.board
    game2.board.units = {(4, 4): cleric}
    
    # Set very low AP
    game2.start_player_turn(1)
    game2.current_player_ap = 1
    
    print(f"Low AP test:")
    print(f"  Game AP: {game2.current_player_ap}")
    print(f"  Cleric can act: {game2.can_unit_act(cleric)}")
    
    # Try to use expensive ability (should fail)
    expensive_action = cleric.use_ability(2, (4, 5), game2)  # Heal costs 2 AP
    
    print(f"  Expensive action (2 AP): {expensive_action}")
    print(f"  Game AP after: {game2.current_player_ap}")
    
    assert not expensive_action, "Expensive action should fail with insufficient AP"
    
    # Try cheap action (should work)
    cheap_action = cleric.use_ability(0, (4, 5), game2)  # Move costs 1 AP
    
    print(f"  Cheap action (1 AP): {cheap_action}")
    print(f"  Game AP after: {game2.current_player_ap}")
    
    if cheap_action:
        assert game2.current_player_ap == 0, "AP should be exhausted"
        print("✅ AP exhaustion handling working")
    else:
        print("❌ Cheap action failed unexpectedly")
    
    print(f"\n" + "=" * 48)
    print("🎯 GLOBAL AP IMPLEMENTATION TEST SUMMARY")
    print("-" * 40)
    print("✅ AP progression: 1→10 with P2 early bonus")
    print("✅ Turn initialization: Correct AP calculation")
    print("✅ Action tracking: One action per unit enforced")
    print("✅ Global AP deduction: Working correctly")
    print("✅ AP exhaustion: Prevents invalid actions")
    
    print("\n🚀 GLOBAL AP SYSTEM SUCCESSFULLY IMPLEMENTED!")
    print("   Ready for balance slider integration.")

if __name__ == "__main__":
    test_global_ap_implementation()

﻿import pygame
from units_core import Unit, SimpleAbility, MoveAbility, AttackAbility, SummonAbility
from game_config import GAME_CONFIG
import game_constants as const
import math
import random

# Import unified systems
from core.configuration_manager import get_config_manager
from core.ability_system import DamageCalculator
from core.status_effects import StatusEffectType

class Mage(Unit):
    """Mage unit - Glass cannon with powerful AoE and single-target spells."""
    def __init__(self, player_id):
        # Initialize with unified configuration system
        config_manager = get_config_manager()
        mage_config = config_manager.get_unit_config("Mage")

        super().__init__(
            player_id,
            health=mage_config.get("health", 4),
            max_health=mage_config.get("health", 4)
        )
        self.name = "Mage"
        self.max_ap = mage_config.get("max_ap", 9)
        self.current_ap = mage_config.get("max_ap", 9)
        self.board = None
        self.image = self._create_placeholder_image((138, 43, 226) if player_id == 1 else (148, 0, 211))

        # Abilities with unified configuration
        self.abilities = [
            MoveAbility(self),
            AttackAbility(self),
            SimpleAbility("Fireball", config_manager.get_ability_ap_cost("Mage", "Fireball"), "Deal 2 damage to target and 1 to adjacent units", cooldown=2, owner=self),
            SimpleAbility("Ice Spike", config_manager.get_ability_ap_cost("Mage", "Ice Spike"), "Deal 1 damage and Chill target for 1 turn", cooldown=1, owner=self),
            SimpleAbility("Teleport", config_manager.get_ability_ap_cost("Mage", "Teleport"), "Move to any empty tile within 3 squares", cooldown=2, owner=self),
            SimpleAbility("Frost Nova", config_manager.get_ability_ap_cost("Mage", "Frost Nova"), "Immobilize all adjacent enemy units for 1 turn", cooldown=3, owner=self),
            SimpleAbility("Arcane Missile", config_manager.get_ability_ap_cost("Mage", "Arcane Missile"), "Fire 3 missiles, each dealing 1 damage to random enemies in range", cooldown=2, owner=self),
            SimpleAbility("Cone of Cold", config_manager.get_ability_ap_cost("Mage", "Cone of Cold"), "Deal 1 damage and Chill units in a 3-tile cone", cooldown=3, owner=self),
            SummonAbility(self, config_manager.get_ability_ap_cost("Mage", "Summon"))
        ]

        # Configuration is automatically applied by the base Unit class

    def _create_placeholder_image(self, color):
        surf = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA) # Use const.CELL_SIZE
        pygame.draw.circle(surf, color, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2) # Use const.CELL_SIZE
        pygame.draw.circle(surf, (255, 255, 255), (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2, 2) # Use const.CELL_SIZE
        # Staff or orb symbol
        staff_color = (139, 69, 19) # Brown for staff
        orb_color = (0, 255, 255)   # Cyan for orb
        pygame.draw.line(surf, staff_color, (const.CELL_SIZE//2, const.CELL_SIZE//2 - 15), (const.CELL_SIZE//2, const.CELL_SIZE//2 + 15), 4) # Use const.CELL_SIZE
        pygame.draw.circle(surf, orb_color, (const.CELL_SIZE//2, const.CELL_SIZE//2 - 15), 6) # Use const.CELL_SIZE
        return surf

    def get_valid_moves(self, board):
        """
        Mage movement: Blink exactly 2 tiles in orthogonal directions.
        Can pass through entities (no line of sight required).
        Only destination needs to be unoccupied.
        """
        self.board = board
        # Check if unit can move (new status system + legacy)
        if hasattr(self, 'status_manager') and not self.status_manager.can_move():
            return []
        if self.immobilized or self.stunned:
            return []

        valid_moves = []
        row, col = self.position

        # Blink exactly 2 tiles in orthogonal directions (N, S, E, W)
        blink_moves = [
            (-2, 0),  # North 2 tiles
            (2, 0),   # South 2 tiles
            (0, -2),  # West 2 tiles
            (0, 2)    # East 2 tiles
        ]

        for dr, dc in blink_moves:
            new_row, new_col = row + dr, col + dc
            # Check bounds and if destination is empty (can pass through entities)
            if (0 <= new_row < const.BOARD_SIZE and 0 <= new_col < const.BOARD_SIZE and
                    (new_row, new_col) not in board.units):
                valid_moves.append((new_row, new_col))

        return valid_moves

    def get_valid_attacks(self, board):
        """Mage basic attack is weak, melee range."""
        self.board = board
        if self.has_status('Stunned'): return []
        valid_attacks = []
        row, col = self.position
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0: continue
                r, c = row + dr, col + dc
                if (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and (r,c) in board.units and # Use const.BOARD_SIZE
                        board.units[(r,c)].player_id != self.player_id and not board.units[(r,c)].sanctuary):
                    valid_attacks.append((r,c))
        return valid_attacks
    
    def get_attack_damage(self, target_pos):
        """Calculate Mage basic attack damage using unified damage calculation"""
        return DamageCalculator.calculate_basic_attack_damage(self, target_pos)

    def get_ability_targets(self, ability_idx, board): # Renamed from get_valid_ability_targets for consistency
        self.board = board
        if ability_idx == 0: return self.get_valid_moves(board)
        if ability_idx == 1: return self.get_valid_attacks(board)

        ability_name = self.abilities[ability_idx].name
        spell_range = 4 # Default range for many Mage spells

        if ability_name == "Fireball" or ability_name == "Ice Spike":
            return self._get_enemy_targets_in_line_of_sight(board, spell_range)
        elif ability_name == "Teleport":
            return self._get_teleport_targets(board, 3) # Range 3 for teleport
        elif ability_name == "Frost Nova":
            return [self.position] # Centered on self
        elif ability_name == "Arcane Missile": 
             # Targets automatically, so indicate self-target for UI
            return [self.position] if self._get_random_enemy_targets_for_arcane_missile(board, spell_range) else []
        elif ability_name == "Cone of Cold":
            # Target a direction (adjacent tile)
            return self._get_adjacent_tiles(board, include_occupied=True, include_allies=True, include_enemies=True)
        elif ability_name == "Summon":
            return self.get_valid_moves(board) # Can summon at any position Mage could move to

        return []

    def _get_adjacent_tiles(self, board, include_occupied=True, include_allies=True, include_enemies=True):
        """Helper to get all valid adjacent tiles, primarily for directional targeting."""
        adj_tiles = []
        row,col = self.position
        for dr in [-1,0,1]:
            for dc in [-1,0,1]:
                if dr==0 and dc==0: continue
                nr, nc = row+dr, col+dc
                if 0 <= nr < const.BOARD_SIZE and 0 <= nc < const.BOARD_SIZE: # Use const.BOARD_SIZE
                    is_occupied = (nr,nc) in board.units
                    if not is_occupied:
                        adj_tiles.append((nr,nc))
                    elif include_occupied:
                        if board.units[(nr,nc)].player_id == self.player_id and include_allies:
                            adj_tiles.append((nr,nc))
                        elif board.units[(nr,nc)].player_id != self.player_id and include_enemies:
                            adj_tiles.append((nr,nc))
        return adj_tiles

    def _get_enemy_targets_in_line_of_sight(self, board, max_range):
        targets = []
        row, col = self.position
        for r_target in range(const.BOARD_SIZE): # Use const.BOARD_SIZE
            for c_target in range(const.BOARD_SIZE): # Use const.BOARD_SIZE
                if (r_target, c_target) == self.position: continue
                dist_sq = (r_target - row)**2 + (c_target - col)**2
                if dist_sq <= max_range**2:
                    # Basic LoS: Check direct line, no obstacles
                    # More advanced LoS would trace the line tile by tile
                    # For simplicity now, if it's an enemy, add it.
                    # A true LoS would be needed for tactical depth.
                    target_unit = board.units.get((r_target, c_target))
                    if target_unit and target_unit.player_id != self.player_id and not target_unit.sanctuary:
                        # Simplified LoS: can we draw a straight line without hitting another unit?
                        blocked = False
                        # Skip LoS check for now, just range check
                        targets.append((r_target, c_target))
        return targets
        
    def _get_teleport_targets(self, board, max_range):
        targets = []
        row, col = self.position
        for r_offset in range(-max_range, max_range + 1):
            for c_offset in range(-max_range, max_range + 1):
                if r_offset == 0 and c_offset == 0: continue
                # Check if within circular range
                if r_offset**2 + c_offset**2 > max_range**2: continue
                
                check_r, check_c = row + r_offset, col + c_offset
                if (0 <= check_r < const.BOARD_SIZE and 0 <= check_c < const.BOARD_SIZE and # Use const.BOARD_SIZE
                        (check_r, check_c) not in board.units):
                    targets.append((check_r, check_c))
        return targets

    def _get_random_enemy_targets_for_arcane_missile(self, board, max_range):
        possible_targets = []
        for r_offset in range(-max_range, max_range + 1):
            for c_offset in range(-max_range, max_range + 1):
                if r_offset**2 + c_offset**2 > max_range**2 : continue
                check_r, check_c = self.position[0] + r_offset, self.position[1] + c_offset
                if (0 <= check_r < const.BOARD_SIZE and 0 <= check_c < const.BOARD_SIZE and # Use const.BOARD_SIZE
                        (check_r, check_c) in board.units and
                        board.units[(check_r, check_c)].player_id != self.player_id and
                        not board.units[(check_r, check_c)].sanctuary):
                    possible_targets.append(board.units[(check_r, check_c)])
        return possible_targets

    def use_ability(self, ability_idx, target_pos, game=None):
        """
        Use an ability with unified ability execution system.
        Mage-specific abilities are handled by registered methods.
        """
        # Ensure self.board is set if game object is provided
        if game:
            self.board = game.board

        # Register Mage-specific ability handlers with the unified system
        self._register_ability_handlers()

        # Use unified ability executor (this handles all validation, AP spending, etc.)
        return super().use_ability(ability_idx, target_pos, game)

    def _register_ability_handlers(self):
        """Register Mage-specific ability handlers with the unified ability system"""
        ability_executor = self.ability_executor

        # Register handlers for each Mage ability with proper lambda wrappers
        ability_executor.register_ability_handler("Fireball", lambda unit, target_pos, game: self._use_fireball(target_pos, game))
        ability_executor.register_ability_handler("Ice Spike", lambda unit, target_pos, game: self._use_ice_spike(target_pos, game))
        ability_executor.register_ability_handler("Teleport", lambda unit, target_pos, game: self._use_teleport(target_pos, game))
        ability_executor.register_ability_handler("Frost Nova", lambda unit, target_pos, game: self._use_frost_nova(target_pos, game))
        ability_executor.register_ability_handler("Arcane Missile", lambda unit, target_pos, game: self._use_arcane_missile(target_pos, game))
        ability_executor.register_ability_handler("Cone of Cold", lambda unit, target_pos, game: self._use_cone_of_cold(target_pos, game))

    def _use_fireball(self, target_pos, game=None):
        """Fireball: Deal damage to target and splash damage to adjacent units using unified damage calculation"""
        main_target_unit = self.board.units.get(target_pos)
        if not (main_target_unit and main_target_unit.player_id != self.player_id):
            return False

        # Get configured damage using unified damage calculation
        fireball_damage = DamageCalculator.calculate_ability_damage(self, "Fireball", target_pos)
        splash_damage = fireball_damage // 2  # Splash damage is half of main damage

        # Hit primary target
        print(f"Fireball hits {main_target_unit.name} at {target_pos} for {fireball_damage} damage")
        main_target_unit.take_damage(fireball_damage, self, game=game)

        # Splash damage to adjacent units (orthogonal only, not diagonal)
        target_row, target_col = target_pos
        adjacent_positions = [
            (target_row - 1, target_col), (target_row + 1, target_col),
            (target_row, target_col - 1), (target_row, target_col + 1)
        ]

        for adj_pos in adjacent_positions:
            if (0 <= adj_pos[0] < const.BOARD_SIZE and 0 <= adj_pos[1] < const.BOARD_SIZE):
                adj_unit = self.board.units.get(adj_pos)
                if adj_unit and adj_unit.player_id != self.player_id and not adj_unit.sanctuary:
                    print(f"Fireball splash hits {adj_unit.name} at {adj_pos} for {splash_damage} damage")
                    adj_unit.take_damage(splash_damage, self, game=game)

        return True

    def _use_ice_spike(self, target_pos, game=None):
        """Ice Spike: Deal damage and apply Chilled status using unified systems"""
        target_unit = self.board.units.get(target_pos)
        if not (target_unit and target_unit.player_id != self.player_id):
            return False

        # Get configured damage using unified damage calculation
        ice_damage = DamageCalculator.calculate_ability_damage(self, "Ice Spike", target_pos)

        print(f"Ice Spike hits {target_unit.name} at {target_pos} for {ice_damage} damage")
        target_unit.take_damage(ice_damage, self, game=game)

        # Apply Chilled status effect using unified status system
        target_unit.apply_status('Chilled', 1)
        print(f"{target_unit.name} is chilled for 1 turn")
        return True

    def _use_teleport(self, target_pos, game=None):
        """Teleport: Move to any empty tile within 3 squares"""
        if target_pos in self.board.units:
            return False  # Can't teleport to occupied tile

        # Check if target is within teleport range (3 tiles)
        distance = max(abs(target_pos[0] - self.position[0]), abs(target_pos[1] - self.position[1]))
        if distance > 3:
            return False

        # Move to the target position
        old_pos = self.position
        self.board.units[target_pos] = self
        del self.board.units[old_pos]
        self.position = target_pos
        print(f"{self.name} teleports from {old_pos} to {target_pos}")
        return True

    def _use_frost_nova(self, target_pos, game=None):
        """Frost Nova: Immobilize all adjacent enemy units"""
        affected_units = 0
        row, col = self.position

        # Check all 8 adjacent positions
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:
                    continue  # Skip self position

                adj_pos = (row + dr, col + dc)
                if (0 <= adj_pos[0] < const.BOARD_SIZE and 0 <= adj_pos[1] < const.BOARD_SIZE):
                    adj_unit = self.board.units.get(adj_pos)
                    if adj_unit and adj_unit.player_id != self.player_id and not adj_unit.sanctuary:
                        # Apply Crippled status effect using unified status system
                        adj_unit.apply_status('Crippled', 1)
                        print(f"Frost Nova immobilizes {adj_unit.name} at {adj_pos}")
                        affected_units += 1

        print(f"Frost Nova affected {affected_units} enemy units")
        return True

    def _use_arcane_missile(self, target_pos, game=None):
        """Arcane Missile: Fire 3 missiles at random enemies in range"""
        # Get all enemy units in range (orthogonal directions, up to 4 tiles)
        possible_targets = []
        row, col = self.position

        # Check all four orthogonal directions
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]

        for dr, dc in directions:
            for distance in range(1, 5):  # Range of 4
                check_r, check_c = row + dr * distance, col + dc * distance
                if not (0 <= check_r < const.BOARD_SIZE and 0 <= check_c < const.BOARD_SIZE):
                    break

                if (check_r, check_c) in self.board.units:
                    unit = self.board.units[(check_r, check_c)]
                    if unit.player_id != self.player_id and not unit.sanctuary:
                        possible_targets.append(unit)
                    break  # Line of sight blocked

        if not possible_targets:
            print("Arcane Missile: No valid targets in range")
            return True  # Ability still succeeds even if no targets

        # Get configured damage using unified damage calculation
        missile_damage = DamageCalculator.calculate_ability_damage(self, "Arcane Missile", target_pos)

        # Fire 3 missiles at random targets
        for i in range(3):
            if possible_targets:
                target = random.choice(possible_targets)
                print(f"Arcane Missile {i+1} hits {target.name} for {missile_damage} damage")
                target.take_damage(missile_damage, self, game=game)

                # If target dies, remove from possible targets
                if not target.is_alive():
                    possible_targets.remove(target)

        return True

    def _use_cone_of_cold(self, target_pos, game=None):
        """Cone of Cold: Deal damage and Chill units in a T pattern"""
        # T pattern: 1 tile in chosen orthogonal direction, then 3 tiles at distance 2
        direction_row = target_pos[0] - self.position[0]
        direction_col = target_pos[1] - self.position[1]

        # Normalize direction
        if direction_row != 0: direction_row //= abs(direction_row)
        if direction_col != 0: direction_col //= abs(direction_col)

        # Get configured damage using unified damage calculation
        cone_damage = DamageCalculator.calculate_ability_damage(self, "Cone of Cold", target_pos)

        affected_units = 0

        # First tile in chosen direction
        first_tile = (self.position[0] + direction_row, self.position[1] + direction_col)
        if (0 <= first_tile[0] < const.BOARD_SIZE and 0 <= first_tile[1] < const.BOARD_SIZE):
            unit = self.board.units.get(first_tile)
            if unit and unit.player_id != self.player_id and not unit.sanctuary:
                print(f"Cone of Cold hits {unit.name} at {first_tile} for {cone_damage} damage")
                unit.take_damage(cone_damage, self, game=game)
                unit.apply_status('Chilled', 1)
                affected_units += 1

        # Three tiles at distance 2 (center + left/right)
        second_row_center = (self.position[0] + direction_row * 2, self.position[1] + direction_col * 2)

        # Perpendicular directions for the spread
        if direction_row == 0:  # Horizontal direction
            spread_positions = [
                second_row_center,
                (second_row_center[0] - 1, second_row_center[1]),
                (second_row_center[0] + 1, second_row_center[1])
            ]
        else:  # Vertical direction
            spread_positions = [
                second_row_center,
                (second_row_center[0], second_row_center[1] - 1),
                (second_row_center[0], second_row_center[1] + 1)
            ]

        for pos in spread_positions:
            if (0 <= pos[0] < const.BOARD_SIZE and 0 <= pos[1] < const.BOARD_SIZE):
                unit = self.board.units.get(pos)
                if unit and unit.player_id != self.player_id and not unit.sanctuary:
                    print(f"Cone of Cold hits {unit.name} at {pos} for {cone_damage} damage")
                    unit.take_damage(cone_damage, self, game=game)
                    unit.apply_status('Chilled', 1)
                    affected_units += 1

        print(f"Cone of Cold affected {affected_units} enemy units")
        return True

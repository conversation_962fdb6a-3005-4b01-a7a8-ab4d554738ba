#!/usr/bin/env python3
"""
Test King and Cleric specific fixes
"""

import pygame
import sys
from game_state import Game
from units.king import King
from units.cleric import Cleric

def test_king_cleric():
    """Test King and Cleric fixes"""
    print("🎮 TESTING KING & CLERIC FIXES")
    print("=" * 30)
    
    pygame.init()
    
    # Test 1: King AP System
    print("📋 TEST 1: King AP System")
    print("-" * 25)
    
    game = Game()
    
    # Create King
    king = King(1)
    king.position = (4, 4)
    game.board.units[(4, 4)] = king
    king.board = game.board
    
    # Start turn
    game.start_player_turn(1)
    game.current_player_ap = 10
    
    # Reset acted flag
    king.has_acted_this_turn = False
    
    # Try first action
    first_action = king.use_ability(0, (king.position[0], king.position[1] + 1), game)
    print(f"King first action (Move): {first_action}")
    
    # Try second action
    second_action = king.use_ability(1, (king.position[0], king.position[1] + 2), game)
    print(f"King second action (Attack): {second_action}")
    
    king_ap_working = first_action and not second_action
    print(f"King AP System: {'✅ WORKING' if king_ap_working else '❌ BROKEN'}")
    
    # Test 2: King Damage Attributes
    print(f"\n📋 TEST 2: King Damage Attributes")
    print("-" * 33)
    
    king_damage_working = True
    for ability in king.abilities:
        has_damage = hasattr(ability, 'damage')
        print(f"  {ability.name}: {'✅' if has_damage else '❌'} Damage = {getattr(ability, 'damage', 'MISSING')}")
        if not has_damage:
            king_damage_working = False
    
    print(f"King Damage Attributes: {'✅ WORKING' if king_damage_working else '❌ BROKEN'}")
    
    # Test 3: Cleric AP System
    print(f"\n📋 TEST 3: Cleric AP System")
    print("-" * 27)
    
    # Create Cleric
    cleric = Cleric(1)
    cleric.position = (2, 2)
    game.board.units[(2, 2)] = cleric
    cleric.board = game.board
    
    # Reset for new test
    game.start_player_turn(1)
    game.current_player_ap = 10
    cleric.has_acted_this_turn = False
    
    # Try first action
    first_action = cleric.use_ability(0, (cleric.position[0], cleric.position[1] + 1), game)
    print(f"Cleric first action (Move): {first_action}")
    
    # Try second action
    second_action = cleric.use_ability(1, (cleric.position[0], cleric.position[1] + 2), game)
    print(f"Cleric second action (Attack): {second_action}")
    
    cleric_ap_working = first_action and not second_action
    print(f"Cleric AP System: {'✅ WORKING' if cleric_ap_working else '❌ BROKEN'}")
    
    # Test 4: Cleric Healing Configuration
    print(f"\n📋 TEST 4: Cleric Healing Configuration")
    print("-" * 38)
    
    cleric_config_working = True
    heal_abilities = []
    
    for ability in cleric.abilities:
        has_damage = hasattr(ability, 'damage')
        has_heal = hasattr(ability, 'heal_amount')
        
        print(f"  {ability.name}:")
        print(f"    Damage: {'✅' if has_damage else '❌'} {getattr(ability, 'damage', 'MISSING')}")
        
        if ability.name in ["Heal", "Mass Heal"]:
            print(f"    Heal Amount: {'✅' if has_heal else '❌'} {getattr(ability, 'heal_amount', 'MISSING')}")
            heal_abilities.append(has_heal)
        
        if not has_damage:
            cleric_config_working = False
    
    healing_working = all(heal_abilities)
    print(f"Cleric Configuration: {'✅ WORKING' if cleric_config_working else '❌ BROKEN'}")
    print(f"Healing Attributes: {'✅ WORKING' if healing_working else '❌ BROKEN'}")
    
    # Test 5: Configuration Menu Healing Sliders
    print(f"\n📋 TEST 5: Healing Sliders in Config")
    print("-" * 35)
    
    try:
        from menu_screens.new_config_menu import NewConfigMenu
        screen = pygame.display.set_mode((800, 600))
        clock = pygame.time.Clock()
        
        config_menu = NewConfigMenu(screen, clock)
        
        heal_config = config_menu.ability_data["Cleric"]["Heal"]
        mass_heal_config = config_menu.ability_data["Cleric"]["Mass Heal"]
        
        has_heal_in_config = "heal_amount" in heal_config and "heal_amount" in mass_heal_config
        
        print(f"  Heal config: {heal_config}")
        print(f"  Mass Heal config: {mass_heal_config}")
        print(f"Healing Sliders: {'✅ WORKING' if has_heal_in_config else '❌ BROKEN'}")
        
    except Exception as e:
        print(f"❌ Config menu test failed: {e}")
        has_heal_in_config = False
    
    pygame.quit()
    
    # Summary
    print(f"\n" + "=" * 30)
    print("🎯 KING & CLERIC TEST SUMMARY")
    print("-" * 30)
    print(f"King AP System: {'✅ WORKING' if king_ap_working else '❌ BROKEN'}")
    print(f"King Damage Attributes: {'✅ WORKING' if king_damage_working else '❌ BROKEN'}")
    print(f"Cleric AP System: {'✅ WORKING' if cleric_ap_working else '❌ BROKEN'}")
    print(f"Cleric Configuration: {'✅ WORKING' if cleric_config_working else '❌ BROKEN'}")
    print(f"Healing Attributes: {'✅ WORKING' if healing_working else '❌ BROKEN'}")
    print(f"Healing Sliders: {'✅ WORKING' if has_heal_in_config else '❌ BROKEN'}")
    
    all_working = (king_ap_working and king_damage_working and 
                   cleric_ap_working and cleric_config_working and 
                   healing_working and has_heal_in_config)
    
    if all_working:
        print(f"\n🎉 ALL KING & CLERIC FIXES SUCCESSFUL!")
        print("✅ King follows one action per turn rule")
        print("✅ King abilities have damage attributes")
        print("✅ Cleric follows one action per turn rule")
        print("✅ Cleric works with configuration system")
        print("✅ Healing abilities have configurable amounts")
        print("✅ Healing sliders available in config menu")
        return True
    else:
        print(f"\n❌ SOME ISSUES REMAIN!")
        return False

if __name__ == "__main__":
    success = test_king_cleric()
    if success:
        print(f"\n🚀 KING & CLERIC PERFECT!")
        print("All specific issues resolved!")
    else:
        print(f"\n🔧 NEEDS MORE WORK!")
    
    sys.exit(0 if success else 1)

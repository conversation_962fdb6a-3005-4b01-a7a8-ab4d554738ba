import unittest
import pygame
from game_units import <PERSON>, <PERSON>, Cleric, config.GAME_SETTINGS
from game_board import GameBoard

class TestHunterSpreadShot(unittest.TestCase):
    """Tests specifically for the Hunter's Spread Shot ability"""
    
    def setUp(self):
        """Set up a fresh board and units for each test"""
        # Initialize pygame and create a dummy screen
        pygame.init()
        self.screen = pygame.Surface((800, 600))  # Create an off-screen surface
        
        # Create the game board with the dummy screen
        self.board = GameBoard(self.screen)
        
        # Create the hunter
        self.hunter = Hunter(player_id=1)
        self.hunter.current_ap = 10  # Ensure plenty of AP for testing
        
        # Place the hunter at a central position
        self.board.add_unit(self.hunter, 4, 4)
        
        # Set up the hunter's board reference
        self.hunter.board = self.board
        
        # Get the Spread Shot ability details
        self.spread_shot_ability = self.hunter.abilities[5]
        self.ability_idx = 5  # Index of Spread Shot ability
        
    def tearDown(self):
        """Clean up pygame resources"""
        pygame.quit()
    
    def test_diagonal_spread_shot(self):
        """Test Spread Shot ability when fired diagonally (current implementation)"""
        # Place an enemy diagonally from the hunter
        enemy = Warrior(player_id=2)
        self.board.add_unit(enemy, 6, 6)  # Diagonally down-right
        
        # Initial health
        initial_health = enemy.health
        
        # Use the Spread Shot ability targeting diagonally
        success = self.hunter.use_ability(self.ability_idx, (6, 6))
        
        # Verify the ability was used successfully
        self.assertTrue(success, "Spread Shot ability should execute successfully")
        
        # Verify AP was consumed
        self.assertEqual(self.hunter.current_ap, 10 - self.spread_shot_ability.ap_cost, 
                        "AP should be consumed when using Spread Shot")
        
        # Verify damage was done to the enemy
        self.assertEqual(enemy.health, initial_health - config.GAME_SETTINGS["ability_damage"], 
                        "Diagonal target should take damage")
        
        # Verify cooldown was applied
        self.assertEqual(self.spread_shot_ability.cooldown_remaining, 
                        self.spread_shot_ability.cooldown, 
                        "Cooldown should be applied after using Spread Shot")
    
    def test_shot_blocked_by_friendly_units(self):
        """Test that Spread Shot is blocked by friendly units"""
        # Place an enemy and a friendly unit in a line
        enemy = Warrior(player_id=2)
        friendly = Warrior(player_id=1)
        
        # Place units
        self.board.add_unit(friendly, 5, 5)  # One step diagonally from hunter
        self.board.add_unit(enemy, 6, 6)     # Two steps diagonally from hunter (behind friendly)
        
        # Initial health values
        initial_health_enemy = enemy.health
        
        # Use the Spread Shot ability
        self.hunter.use_ability(self.ability_idx, (7, 7))  # Target far diagonal
        
        # Verify enemy was not damaged since friendly unit blocks the shot
        self.assertEqual(enemy.health, initial_health_enemy, 
                        "Enemy behind friendly unit should not take damage")

if __name__ == "__main__":
    unittest.main()

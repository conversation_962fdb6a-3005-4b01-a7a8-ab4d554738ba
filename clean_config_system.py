#!/usr/bin/env python3
"""
Clean, bug-free ability configuration system
Integrated with the main game
"""

import pygame
import json
import sys
from typing import Dict, List, Optional, Tuple
from menu_screens.button import Button
import game_constants as const

class ConfigSlider:
    """A simple, reliable slider widget"""
    
    def __init__(self, x: int, y: int, width: int, label: str, 
                 value: float, min_val: float, max_val: float):
        self.rect = pygame.Rect(x, y + 20, width, 10)
        self.label = label
        self.value = value
        self.min_val = min_val
        self.max_val = max_val
        self.dragging = False
        self.label_pos = (x, y)
    
    def handle_event(self, event) -> bool:
        """Handle mouse events, return True if value changed"""
        if event.type == pygame.MOUSEBUTTONDOWN:
            if self.rect.collidepoint(event.pos):
                self.dragging = True
                self._update_value_from_mouse(event.pos[0])
                return True
        
        elif event.type == pygame.MOUSEBUTTONUP:
            self.dragging = False
        
        elif event.type == pygame.MOUSEMOTION and self.dragging:
            self._update_value_from_mouse(event.pos[0])
            return True
        
        return False
    
    def _update_value_from_mouse(self, mouse_x: int):
        """Update value based on mouse position"""
        relative_x = mouse_x - self.rect.x
        ratio = max(0, min(1, relative_x / self.rect.width))
        self.value = self.min_val + ratio * (self.max_val - self.min_val)
    
    def render(self, screen: pygame.Surface, font: pygame.font.Font):
        """Render the slider"""
        # Label
        label_text = f"{self.label}: {self.value:.1f}" if isinstance(self.value, float) else f"{self.label}: {int(self.value)}"
        label_surf = font.render(label_text, True, (255, 255, 255))
        screen.blit(label_surf, self.label_pos)
        
        # Track
        pygame.draw.rect(screen, (100, 100, 100), self.rect)
        pygame.draw.rect(screen, (255, 255, 255), self.rect, 1)
        
        # Handle
        if self.max_val > self.min_val:
            ratio = (self.value - self.min_val) / (self.max_val - self.min_val)
            handle_x = self.rect.x + ratio * self.rect.width
            handle_rect = pygame.Rect(handle_x - 5, self.rect.y - 2, 10, 14)
            pygame.draw.rect(screen, (200, 200, 200), handle_rect)

class CleanConfigSystem:
    """Clean, simple configuration system"""
    
    def __init__(self):
        self.font = None
        self.small_font = None
        
        # Current state
        self.selected_class = "Warrior"
        self.mode = "class"  # "class" or "abilities"
        self.selected_ability = None
        
        # Data
        self.class_data = {
            "Warrior": {"hp": 12, "movement": 2},
            "Mage": {"hp": 8, "movement": 2},
            "Cleric": {"hp": 10, "movement": 1},
            "Rogue": {"hp": 9, "movement": 3},
            "Hunter": {"hp": 10, "movement": 3}
        }
        
        self.ability_data = {
            "Warrior": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 2, "ap_cost": 2, "cooldown": 0},
                "Charge": {"damage": 3, "ap_cost": 3, "cooldown": 2},
                "Shield Bash": {"damage": 2, "ap_cost": 2, "cooldown": 1},
                "Whirlwind": {"damage": 4, "ap_cost": 4, "cooldown": 3},
                "Taunt": {"damage": 0, "ap_cost": 1, "cooldown": 2},
                "Berserker Rage": {"damage": 0, "ap_cost": 3, "cooldown": 4},
                "Shield Wall": {"damage": 0, "ap_cost": 2, "cooldown": 3}
            },
            "Mage": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 1, "ap_cost": 1, "cooldown": 0},
                "Fireball": {"damage": 3, "ap_cost": 4, "cooldown": 2},
                "Ice Spike": {"damage": 2, "ap_cost": 3, "cooldown": 1},
                "Teleport": {"damage": 0, "ap_cost": 2, "cooldown": 3},
                "Arcane Missiles": {"damage": 3, "ap_cost": 5, "cooldown": 2},
                "Frost Nova": {"damage": 1, "ap_cost": 3, "cooldown": 4},
                "Lightning Bolt": {"damage": 4, "ap_cost": 4, "cooldown": 2},
                "Cone of Cold": {"damage": 2, "ap_cost": 4, "cooldown": 3},
                "Meteor": {"damage": 6, "ap_cost": 6, "cooldown": 5}
            },
            "Cleric": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 1, "ap_cost": 1, "cooldown": 0},
                "Heal": {"damage": 0, "ap_cost": 2, "cooldown": 1},
                "Mass Heal": {"damage": 0, "ap_cost": 5, "cooldown": 3},
                "Cleanse": {"damage": 0, "ap_cost": 2, "cooldown": 2},
                "Holy Smite": {"damage": 3, "ap_cost": 3, "cooldown": 2},
                "Divine Protection": {"damage": 0, "ap_cost": 2, "cooldown": 3},
                "Sanctuary": {"damage": 0, "ap_cost": 3, "cooldown": 4},
                "Turn Undead": {"damage": 4, "ap_cost": 4, "cooldown": 3},
                "Blessing": {"damage": 0, "ap_cost": 2, "cooldown": 2}
            },
            "Rogue": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 2, "ap_cost": 1, "cooldown": 0},
                "Backstab": {"damage": 4, "ap_cost": 2, "cooldown": 2},
                "Fan of Knives": {"damage": 2, "ap_cost": 2, "cooldown": 2},
                "Assassination": {"damage": 5, "ap_cost": 3, "cooldown": 3},
                "Stealth": {"damage": 0, "ap_cost": 2, "cooldown": 4},
                "Poison Blade": {"damage": 3, "ap_cost": 2, "cooldown": 2},
                "Shadow Step": {"damage": 0, "ap_cost": 2, "cooldown": 3},
                "Smoke Bomb": {"damage": 0, "ap_cost": 3, "cooldown": 4},
                "Critical Strike": {"damage": 6, "ap_cost": 4, "cooldown": 3}
            },
            "Hunter": {
                "Move": {"damage": 0, "ap_cost": 1, "cooldown": 0},
                "Attack": {"damage": 2, "ap_cost": 2, "cooldown": 0},
                "Multishot": {"damage": 2, "ap_cost": 3, "cooldown": 1},
                "Triple Shot": {"damage": 3, "ap_cost": 4, "cooldown": 2},
                "Piercing Shot": {"damage": 3, "ap_cost": 3, "cooldown": 2},
                "Crippling Shot": {"damage": 2, "ap_cost": 2, "cooldown": 1},
                "Explosive Shot": {"damage": 4, "ap_cost": 4, "cooldown": 3},
                "Hunter's Mark": {"damage": 0, "ap_cost": 1, "cooldown": 2},
                "Trap": {"damage": 3, "ap_cost": 3, "cooldown": 4},
                "Eagle Eye": {"damage": 0, "ap_cost": 2, "cooldown": 3}
            }
        }
        
        # UI elements
        self.sliders: List[ConfigSlider] = []
        self._create_sliders()
    
    def _create_sliders(self):
        """Create sliders based on current mode and selection"""
        self.sliders.clear()
        
        if self.mode == "class":
            # Class sliders
            class_stats = self.class_data[self.selected_class]
            y_start = 200

            self.sliders.append(ConfigSlider(400, y_start, 200, "HP",
                                           class_stats["hp"], 5, 20))
            self.sliders.append(ConfigSlider(400, y_start + 50, 200, "Movement",
                                           class_stats["movement"], 1, 5))
        
        elif self.mode == "abilities" and self.selected_ability:
            # Ability sliders
            ability_stats = self.ability_data[self.selected_class][self.selected_ability]
            y_start = 200
            
            if ability_stats["damage"] > 0:  # Only show damage slider if ability does damage
                self.sliders.append(ConfigSlider(400, y_start, 200, "Damage", 
                                               ability_stats["damage"], 0, 10))
                y_start += 50
            
            self.sliders.append(ConfigSlider(400, y_start, 200, "AP Cost", 
                                           ability_stats["ap_cost"], 0, 8))
            
            if ability_stats["cooldown"] > 0:  # Only show cooldown if ability has one
                self.sliders.append(ConfigSlider(400, y_start + 50, 200, "Cooldown", 
                                               ability_stats["cooldown"], 0, 5))
    
    def handle_event(self, event) -> bool:
        """Handle events, return True if something changed"""
        changed = False
        
        # Handle slider events
        for slider in self.sliders:
            if slider.handle_event(event):
                self._update_data_from_sliders()
                changed = True
        
        # Handle button clicks
        if event.type == pygame.MOUSEBUTTONDOWN:
            mouse_x, mouse_y = event.pos
            
            # Class selection buttons
            class_buttons = ["Warrior", "Mage", "Cleric", "Rogue", "Hunter"]
            for i, class_name in enumerate(class_buttons):
                button_rect = pygame.Rect(50 + i * 120, 50, 100, 30)
                if button_rect.collidepoint(mouse_x, mouse_y):
                    self.selected_class = class_name
                    self.selected_ability = None
                    self._create_sliders()
                    changed = True
            
            # Mode buttons
            class_button = pygame.Rect(50, 100, 100, 30)
            ability_button = pygame.Rect(160, 100, 100, 30)
            
            if class_button.collidepoint(mouse_x, mouse_y):
                self.mode = "class"
                self.selected_ability = None
                self._create_sliders()
                changed = True
            elif ability_button.collidepoint(mouse_x, mouse_y):
                self.mode = "abilities"
                self._create_sliders()
                changed = True
            
            # Ability buttons (only in ability mode)
            if self.mode == "abilities":
                abilities = list(self.ability_data[self.selected_class].keys())
                for i, ability_name in enumerate(abilities):
                    button_rect = pygame.Rect(50, 150 + i * 35, 150, 30)
                    if button_rect.collidepoint(mouse_x, mouse_y):
                        self.selected_ability = ability_name
                        self._create_sliders()
                        changed = True
        
        return changed
    
    def _update_data_from_sliders(self):
        """Update data based on current slider values"""
        if self.mode == "class":
            class_stats = self.class_data[self.selected_class]
            if len(self.sliders) >= 2:
                class_stats["hp"] = int(self.sliders[0].value)
                class_stats["movement"] = int(self.sliders[1].value)
        
        elif self.mode == "abilities" and self.selected_ability:
            ability_stats = self.ability_data[self.selected_class][self.selected_ability]
            slider_idx = 0
            
            # Update damage if slider exists
            if ability_stats["damage"] > 0 and slider_idx < len(self.sliders):
                ability_stats["damage"] = self.sliders[slider_idx].value
                slider_idx += 1
            
            # Update AP cost
            if slider_idx < len(self.sliders):
                ability_stats["ap_cost"] = int(self.sliders[slider_idx].value)
                slider_idx += 1
            
            # Update cooldown if slider exists
            if ability_stats["cooldown"] > 0 and slider_idx < len(self.sliders):
                ability_stats["cooldown"] = int(self.sliders[slider_idx].value)
    
    def render(self, screen: pygame.Surface):
        """Render the configuration UI"""
        if not self.font:
            self.font = pygame.font.Font(None, 24)
            self.small_font = pygame.font.Font(None, 20)
        
        # Clear background
        screen.fill((30, 30, 30))
        
        # Title
        title = self.font.render("Clean Configuration System", True, (255, 255, 255))
        screen.blit(title, (50, 10))
        
        # Class selection buttons
        class_buttons = ["Warrior", "Mage", "Cleric", "Rogue", "Hunter"]
        for i, class_name in enumerate(class_buttons):
            button_rect = pygame.Rect(50 + i * 120, 50, 100, 30)
            color = (100, 150, 200) if class_name == self.selected_class else (70, 70, 70)
            
            pygame.draw.rect(screen, color, button_rect)
            pygame.draw.rect(screen, (255, 255, 255), button_rect, 1)
            
            text = self.small_font.render(class_name, True, (255, 255, 255))
            text_rect = text.get_rect(center=button_rect.center)
            screen.blit(text, text_rect)
        
        # Mode buttons
        class_button = pygame.Rect(50, 100, 100, 30)
        ability_button = pygame.Rect(160, 100, 100, 30)
        
        class_color = (100, 150, 200) if self.mode == "class" else (70, 70, 70)
        ability_color = (100, 150, 200) if self.mode == "abilities" else (70, 70, 70)
        
        pygame.draw.rect(screen, class_color, class_button)
        pygame.draw.rect(screen, (255, 255, 255), class_button, 1)
        pygame.draw.rect(screen, ability_color, ability_button)
        pygame.draw.rect(screen, (255, 255, 255), ability_button, 1)
        
        class_text = self.small_font.render("Class Stats", True, (255, 255, 255))
        ability_text = self.small_font.render("Abilities", True, (255, 255, 255))
        
        screen.blit(class_text, (class_button.x + 10, class_button.y + 8))
        screen.blit(ability_text, (ability_button.x + 20, ability_button.y + 8))
        
        # Ability buttons (if in ability mode)
        if self.mode == "abilities":
            abilities = list(self.ability_data[self.selected_class].keys())
            for i, ability_name in enumerate(abilities):
                button_rect = pygame.Rect(50, 150 + i * 35, 150, 30)
                color = (100, 150, 200) if ability_name == self.selected_ability else (70, 70, 70)
                
                pygame.draw.rect(screen, color, button_rect)
                pygame.draw.rect(screen, (255, 255, 255), button_rect, 1)
                
                text = self.small_font.render(ability_name, True, (255, 255, 255))
                screen.blit(text, (button_rect.x + 5, button_rect.y + 8))
        
        # Render sliders
        for slider in self.sliders:
            slider.render(screen, self.small_font)
        
        # Status info
        status_y = 400
        if self.mode == "class":
            class_stats = self.class_data[self.selected_class]
            status_text = f"{self.selected_class}: HP={class_stats['hp']}, Movement={class_stats['movement']}"
        else:
            if self.selected_ability:
                ability_stats = self.ability_data[self.selected_class][self.selected_ability]
                status_text = f"{self.selected_ability}: Dmg={ability_stats['damage']}, AP={ability_stats['ap_cost']}, CD={ability_stats['cooldown']}"
            else:
                status_text = "Select an ability to configure"
        
        status_surf = self.small_font.render(status_text, True, (100, 255, 100))
        screen.blit(status_surf, (50, status_y))

def run_clean_demo():
    """Run the clean configuration demo"""
    pygame.init()
    screen = pygame.display.set_mode((800, 500))
    pygame.display.set_caption("Clean Configuration System - Bug Free")
    clock = pygame.time.Clock()
    
    config_system = CleanConfigSystem()
    
    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            else:
                config_system.handle_event(event)
        
        config_system.render(screen)
        pygame.display.flip()
        clock.tick(60)
    
    pygame.quit()

if __name__ == "__main__":
    run_clean_demo()

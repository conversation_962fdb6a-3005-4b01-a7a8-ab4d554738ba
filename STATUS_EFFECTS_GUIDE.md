# Status Effects System Guide

## Overview

The game now features a comprehensive status effect system that properly tracks duration based on the unit's owner's turns, not global game turns. This fixes issues like the Hunter's Crippling Shot and provides a foundation for many more status effects.

## Key Features

### ✅ **Turn-Based Duration**
- Status effects count down based on the **unit's owner's turns**
- Example: "Stunned 2" means the unit cannot act for 2 of its owner's turns
- Much more intuitive and balanced than global turn counting

### ✅ **Comprehensive Status Effects**
- **Stunned**: Cannot move or use abilities
- **Immobilized**: Cannot move but can use abilities  
- **Chilled**: Abilities cost +1 AP
- **Poisoned**: Takes damage at start of turn
- **Frozen**: Cannot act, takes extra fire damage
- **Slowed**: Movement range reduced by half
- **Burning**: Takes fire damage at start of turn
- **Blessed**: Heals at start of turn
- **Shielded**: Takes 50% less damage
- **Invisible**: Cannot be targeted by single-target abilities
- **Marked**: Takes +50% damage from all sources
- **Weakened**: Deals 50% less damage
- **Enraged**: Deals +50% damage but takes +50% damage

### ✅ **Smart Integration**
- Automatic UI display in unit info panels
- Proper interaction with movement and ability systems
- Damage modification system
- AP cost modification system

## How to Use

### Adding Status Effects to Abilities

```python
from status_effects import apply_stun, apply_immobilize, apply_chill

# In your ability implementation:
def _use_shield_bash(self, target_pos, game):
    target_unit = self.board.units.get(target_pos)
    if target_unit and target_unit.player_id != self.player_id:
        target_unit.take_damage(1, self)
        # Stun for 1 turn of the target's owner
        apply_stun(target_unit, duration=1, source="Shield Bash")
        return True
```

### Checking Status Effects

```python
# Check if unit has a specific effect
if unit.status_manager.has_effect(StatusType.STUNNED):
    print("Unit is stunned!")

# Check if unit can perform actions
if not unit.status_manager.can_move():
    print("Unit cannot move!")

if not unit.status_manager.can_use_abilities():
    print("Unit cannot use abilities!")

# Get all status effects for display
effects = unit.status_manager.get_status_display()
# Returns: ['Stunned (2t)', 'Chilled (1t)']
```

### Custom Status Effects

```python
# Add custom status effect
unit.status_manager.add_effect(
    StatusType.POISONED, 
    duration=3, 
    intensity=2,  # 2 damage per turn
    source="Poison Arrow"
)
```

## Current Implementations

### Hunter - Crippling Shot ✅
- **Effect**: Immobilizes target for 2 turns
- **Fixed**: Now properly counts target's owner turns
- **Usage**: Target cannot move but can still use abilities

### Warrior - Shield Bash ✅  
- **Effect**: Stuns target for 1 turn
- **New**: Uses proper turn-based duration
- **Usage**: Target cannot move or use abilities

### Mage - Ice Spike ✅
- **Effect**: Chills target for 2 turns  
- **Enhanced**: Now uses new status system
- **Usage**: Target's abilities cost +1 AP

### Mage - Cone of Cold ✅
- **Effect**: Chills all targets in cone for 2 turns
- **Enhanced**: Now uses new status system
- **Usage**: All affected targets have +1 AP cost

## Backward Compatibility

The system maintains backward compatibility with the old status effect attributes:
- `unit.stunned` still works alongside `StatusType.STUNNED`
- `unit.immobilized` still works alongside `StatusType.IMMOBILIZED`  
- `unit.chilled` still works alongside `StatusType.CHILLED`

This ensures existing code continues to work while new implementations can use the enhanced system.

## Adding New Status Effects

1. **Add to StatusType enum** in `status_effects.py`
2. **Implement effect logic** in `StatusEffectManager.tick_turn_start()`
3. **Add modifier functions** if needed (damage, AP cost, etc.)
4. **Create convenience function** for easy application

Example:
```python
# 1. Add to enum
class StatusType(Enum):
    BURNING = "burning"

# 2. Add logic in tick_turn_start
elif status_type == StatusType.BURNING:
    damage = effect.intensity
    print(f"{self.unit.name} takes {damage} fire damage from burning")
    self.unit.take_damage(damage, attacker=None, game=game)

# 3. Create convenience function
def apply_burn(unit, duration: int, damage_per_turn: int = 1, source: str = None):
    unit.status_manager.add_effect(StatusType.BURNING, duration, damage_per_turn, source=source)
```

## Testing

Run `python test_status_effects.py` to see the system in action with comprehensive tests covering:
- All major status effects
- Turn progression
- Status effect interactions
- Damage and AP modifiers
- UI integration

The status effect system is now robust, extensible, and properly balanced for turn-based gameplay!

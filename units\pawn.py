import pygame
from units_core import Unit, Ability, MoveAbility, AttackAbility
from game_config import GAME_CONFIG
import game_constants as const
import math

class Pawn(Unit):
    """Pawn unit - Basic, expendable unit with simple actions"""
    def __init__(self, player_id):
        super().__init__(player_id, health=GAME_CONFIG.get("pawn_config", {}).get("health", 3), max_health=GAME_CONFIG.get("pawn_config", {}).get("health", 3))
        self.name = "Pawn"
        self.max_ap = GAME_CONFIG.get("pawn_config", {}).get("max_ap", 5)
        self.current_ap = GAME_CONFIG.get("pawn_config", {}).get("max_ap", 5)
        self.board = None
        self.image = self._create_placeholder_image((200, 200, 0) if player_id == 1 else (255, 255, 100))

        # Pawns typically only have Move and Basic Attack
        self.abilities = [
            MoveAbility(self),
            AttackAbility(self) 
            # Add a simple ability if desired, e.g., "Fortify" (gain +1 temp HP, 1 AP, CD 2)
            # Ability("Fortify", 1, "Gain +1 temporary HP", cooldown=2) 
        ]

    def _create_placeholder_image(self, color):
        surf = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA)
        pygame.draw.circle(surf, color, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 4)
        pygame.draw.circle(surf, (255, 255, 255), (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 4, 1)
        # Simple dot or small shape
        pygame.draw.circle(surf, (50,50,50), (const.CELL_SIZE//2, const.CELL_SIZE//2), 5)
        return surf

    def get_valid_moves(self, board):
        """Pawns move one square in any direction (like a King)."""
        self.board = board
        if self.has_status('Immobilized') or self.has_status('Stunned'):
            return []

        valid_moves = []
        row, col = self.position

        # King-like movement: 8 directions
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:  # Skip current position
                    continue
                
                r, c = row + dr, col + dc
                if 0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and (r, c) not in board.units:
                    valid_moves.append((r, c))
        return valid_moves

    def get_valid_attacks(self, board):
        """Pawns attack one square diagonally forward."""
        self.board = board
        if self.has_status('Stunned'):
            return []
            
        valid_attacks = []
        row, col = self.position

        if self.player_id == 1:
            attack_dirs = [(-1, -1), (-1, 1)] # Forward-left, Forward-right
        else: # player_id == 2
            attack_dirs = [(1, -1), (1, 1)]   # Forward-left, Forward-right (relative to P2)

        for dr, dc in attack_dirs:
            r, c = row + dr, col + dc
            if (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and
                    (r, c) in board.units and
                    board.units[(r, c)].player_id != self.player_id and
                    not board.units[(r,c)].sanctuary):
                valid_attacks.append((r, c))
        return valid_attacks

    # Pawns usually don't have complex abilities, so get_valid_ability_targets primarily handles move/attack.
    def get_ability_targets(self, ability_idx, board):
        self.board = board
        if ability_idx == 0: return self.get_valid_moves(board)
        if ability_idx == 1: return self.get_valid_attacks(board)
        # Example for a simple self-target ability like "Fortify"
        # if self.abilities[ability_idx].name == "Fortify":
        #    return [self.position] 
        return []

    def use_ability(self, ability_idx, target_pos, game=None):
        # Rely on the base Unit.use_ability for Move (0) and Attack (1)
        if ability_idx == 0 or ability_idx == 1:
            return super().use_ability(ability_idx, target_pos, game)

        # For any other Pawn-specific abilities (if they get added):
        if not self.can_use_ability(ability_idx):
            return False

        ability = self.abilities[ability_idx]
        ap_cost = self.get_ability_ap_cost(ability_idx) # Use the new method that handles status effects

        # Use global AP system if game is provided
        if game and hasattr(game, 'spend_ap'):
            if not game.spend_ap(ap_cost, self):
                print(f"Not enough global AP! Need {ap_cost}, have {game.current_player_ap}")
                return False
        else:
            self.current_ap -= ap_cost
        ability.cooldown_remaining = ability.cooldown
        
        print(f"{self.name} uses {ability.name} on {target_pos if target_pos != self.position else 'self'}")
        
        # Example for Fortify
        # if ability.name == "Fortify":
        #     self.health += 1
        #     self.max_health +=1 # Or make it temp HP not exceeding max_health unless specifically designed for it
        #     print(f"{self.name} used Fortify. HP: {self.health}/{self.max_health}")
        #     return True
            
        return True # Assuming other specific pawn abilities are successful if they reach here

    # Pawns don't typically level up in the same way, but can inherit if needed.
    # def level_up(self):
    #    super().level_up()
    #    # Pawn specific level up bonuses, if any 
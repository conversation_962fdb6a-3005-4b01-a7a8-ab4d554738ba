import unittest
import pygame
from unittest.mock import MagicMock # Added MagicMock
from game_board import GameBoard
from game_units import Cleric, Warrior, Hunter

class TestClericHealAbility(unittest.TestCase):
    """Tests specifically for the Cleric's Heal ability"""
    
    def setUp(self):
        """Set up a fresh board and units for each test"""
        # Initialize pygame and create a dummy screen
        pygame.init()
        self.screen = pygame.Surface((800, 600))  # Create an off-screen surface
        
        # Create the game board with the dummy screen
        self.board = GameBoard(self.screen)
        self.cleric = Cleric(player_id=1)
        self.cleric.current_ap = 10  # Ensure plenty of AP for testing
        
        # Place the cleric at a central position
        self.board.add_unit(self.cleric, 4, 4)
        
        # Set up the cleric's board reference (normally done by the game)
        self.cleric.board = self.board
    
    def tearDown(self):
        """Clean up pygame resources"""
        pygame.quit()
    
    def test_heal_ability_targeting(self):
        """Test that heal ability correctly targets adjacent friendly units that are damaged"""
        # Place a damaged friendly warrior adjacent to the cleric
        warrior = Warrior(player_id=1)
        warrior.health = warrior.max_health - 2  # Damage the warrior
        self.board.add_unit(warrior, 4, 5)  # Adjacent (right)
        
        # Place a damaged friendly hunter diagonal to the cleric (should NOT be targetable)
        hunter = Hunter(player_id=1)
        hunter.health = hunter.max_health - 2
        self.board.add_unit(hunter, 5, 5)  # Diagonal (not adjacent orthogonally)
        
        # Place an undamaged warrior adjacent to the cleric (should NOT be targetable)
        full_warrior = Warrior(player_id=1)
        self.board.add_unit(full_warrior, 3, 4)  # Adjacent (left)
        
        # Place an enemy warrior adjacent to the cleric (should NOT be targetable)
        enemy_warrior = Warrior(player_id=2)
        enemy_warrior.health = enemy_warrior.max_health - 2
        self.board.add_unit(enemy_warrior, 4, 3)  # Adjacent (up)
        
        # Get valid targets for heal ability
        heal_ability = next(a for a in self.cleric.abilities if a.name == "Heal")
        heal_ability_idx = self.cleric.abilities.index(heal_ability)
        heal_targets = self.cleric.get_ability_targets(heal_ability_idx, self.board)
        
        # Verify that only the damaged friendly adjacent warrior is a valid target
        self.assertEqual(len(heal_targets), 1, "There should be exactly one valid target")
        self.assertIn((4, 5), heal_targets, "The damaged friendly warrior should be a valid target")
        self.assertNotIn((5, 5), heal_targets, "Diagonal units should not be targetable")
        self.assertNotIn((3, 4), heal_targets, "Undamaged units should not be targetable")
        self.assertNotIn((4, 3), heal_targets, "Enemy units should not be targetable")
    
    def test_heal_ability_effect(self):
        """Test that heal ability correctly heals the target"""
        # Place a damaged friendly warrior adjacent to the cleric
        warrior = Warrior(player_id=1)
        initial_health = warrior.max_health - 3  # Significantly damaged
        warrior.health = initial_health
        self.board.add_unit(warrior, 4, 5)

        heal_ability = next(a for a in self.cleric.abilities if a.name == "Heal")
        heal_ability_idx = self.cleric.abilities.index(heal_ability)
        
        # Use the heal ability on the warrior
        success = self.cleric.use_ability(heal_ability_idx, (4, 5))
        
        # Verify the ability was used successfully
        self.assertTrue(success, "Heal ability should execute successfully")
        
        # Verify the warrior was healed by the correct amount (heal should add 2 health)
        self.assertEqual(warrior.health, initial_health + 2, 
                        f"Warrior should be healed by 2 (was {initial_health}, now {warrior.health})")
        
        # Verify AP was consumed
        self.assertEqual(self.cleric.current_ap, 10 - self.cleric.abilities[2].ap_cost, 
                        "AP should be consumed when using heal ability")
    
    def test_heal_ability_max_health_cap(self):
        """Test that heal ability doesn't heal beyond max health"""
        # Place a slightly damaged friendly warrior adjacent to the cleric
        warrior = Warrior(player_id=1)
        warrior.health = warrior.max_health - 1  # Only 1 point of damage
        initial_health = warrior.health
        self.board.add_unit(warrior, 4, 5)

        heal_ability = next(a for a in self.cleric.abilities if a.name == "Heal")
        heal_ability_idx = self.cleric.abilities.index(heal_ability)
        
        # Use the heal ability on the warrior
        success = self.cleric.use_ability(heal_ability_idx, (4, 5))
        
        # Verify the ability was used successfully
        self.assertTrue(success, "Heal ability should execute successfully")
        
        # Verify the warrior was only healed up to max health
        self.assertEqual(warrior.health, warrior.max_health, 
                        f"Warrior should be healed only to max health (was {initial_health}, now {warrior.health})")

class TestClericMassHealAbility(unittest.TestCase):
    """Tests specifically for the Cleric's Mass Heal ability"""
    
    def setUp(self):
        """Set up a fresh board and units for each test"""
        # Initialize pygame and create a dummy screen
        pygame.init()
        self.screen = pygame.Surface((800, 600))  # Create an off-screen surface
        
        # Create the game board with the dummy screen
        self.board = GameBoard(self.screen)
        self.cleric = Cleric(player_id=1)
        self.cleric.current_ap = 10  # Ensure plenty of AP for testing
        
        # Place the cleric at a central position
        self.board.add_unit(self.cleric, 4, 4)
        
        # Set up the cleric's board reference (normally done by the game)
        self.cleric.board = self.board
    
    def tearDown(self):
        """Clean up pygame resources"""
        pygame.quit()
    
    def test_mass_heal_ability_targeting(self):
        """Test that Mass Heal ability correctly targets the Cleric itself (as the center of the AoE)"""
        # Mass Heal targets the Cleric itself as the center of the effect
        mass_heal_ability = next(a for a in self.cleric.abilities if a.name == "Mass Heal")
        mass_heal_ability_idx = self.cleric.abilities.index(mass_heal_ability)
        mass_heal_targets = self.cleric.get_ability_targets(mass_heal_ability_idx, self.board)
        
        # Verify only the Cleric's position is targeted (the rest is handled in the ability execution)
        self.assertEqual(len(mass_heal_targets), 1, "There should be exactly one valid target")
        self.assertIn((4, 4), mass_heal_targets, "The Cleric itself should be the target for Mass Heal")
    
    def test_mass_heal_ability_effect(self):
        """Test that Mass Heal ability correctly heals all friendly units within range"""
        # Place several damaged friendly units around the cleric
        units = {}
        
        # Create units in a pattern around the cleric:
        # - Some within 2 tiles (should be healed)
        # - Some outside 2 tiles (should not be healed)
        # - Some at full health (should remain at full health)
        # - Some enemy units (should not be healed)
        
        # Within range - damaged friendly units
        warrior1 = Warrior(player_id=1)
        warrior1.health = warrior1.max_health - 2
        units[(3, 4)] = warrior1  # 1 tile away (left)
        self.board.add_unit(warrior1, 3, 4)
        
        warrior2 = Warrior(player_id=1)
        warrior2.health = warrior2.max_health - 3
        units[(4, 6)] = warrior2  # 2 tiles away (down)
        self.board.add_unit(warrior2, 4, 6)
        
        hunter1 = Hunter(player_id=1)
        hunter1.health = hunter1.max_health - 2
        units[(5, 3)] = hunter1  # Within 2 tiles (diagonal)
        self.board.add_unit(hunter1, 5, 3)
        
        # Within range - full health friendly unit
        full_health = Warrior(player_id=1)
        units[(6, 4)] = full_health  # 2 tiles away (right)
        self.board.add_unit(full_health, 6, 4)
        
        # Within range - enemy unit
        enemy = Warrior(player_id=2)
        enemy.health = enemy.max_health - 2
        units[(4, 2)] = enemy  # 2 tiles away (up)
        self.board.add_unit(enemy, 4, 2)
        
        # Outside range - damaged friendly unit
        out_of_range = Warrior(player_id=1)
        out_of_range.health = out_of_range.max_health - 2
        units[(7, 7)] = out_of_range  # 3+ tiles away (out of range)
        self.board.add_unit(out_of_range, 7, 7)
        
        # Record initial health values
        initial_health = {pos: unit.health for pos, unit in units.items()}

        mass_heal_ability = next(a for a in self.cleric.abilities if a.name == "Mass Heal")
        mass_heal_ability_idx = self.cleric.abilities.index(mass_heal_ability)
        
        # Use Mass Heal ability (centered on the Cleric)
        # Pass a mock game object for abilities that might need game.turn_count (like Frost Nova, not Mass Heal directly but good practice)
        mock_game = MagicMock()
        mock_game.board = self.board
        mock_game.turn_count = 1 # Example turn count
        success = self.cleric.use_ability(mass_heal_ability_idx, (4, 4), game=mock_game)
        
        # Verify the ability was used successfully
        self.assertTrue(success, "Mass Heal ability should execute successfully")
        
        # Verify AP was consumed
        self.assertEqual(self.cleric.current_ap, 10 - self.cleric.abilities[3].ap_cost, 
                        "AP should be consumed when using Mass Heal ability")
        
        # Check healing effects on all units
        # Friendly damaged units within range should be healed by 1
        self.assertEqual(units[(3, 4)].health, initial_health[(3, 4)] + 1, 
                        "Friendly damaged unit at (3,4) should be healed by 1")
        self.assertEqual(units[(4, 6)].health, initial_health[(4, 6)] + 1, 
                        "Friendly damaged unit at (4,6) should be healed by 1")
        self.assertEqual(units[(5, 3)].health, initial_health[(5, 3)] + 1, 
                        "Friendly damaged unit at (5,3) should be healed by 1")
        
        # Full health unit should not be healed above max health
        self.assertEqual(units[(6, 4)].health, full_health.max_health, 
                        "Full health unit should not be healed above max health")
        
        # Enemy units should not be healed
        self.assertEqual(units[(4, 2)].health, initial_health[(4, 2)], 
                        "Enemy unit should not be healed")
        
        # Units outside the range should not be healed
        self.assertEqual(units[(7, 7)].health, initial_health[(7, 7)], 
                        "Unit outside range should not be healed")
    
    def test_mass_heal_ability_max_health_cap(self):
        """Test that Mass Heal ability doesn't heal beyond max health"""
        # Place a friendly unit that only needs 1 health point
        warrior = Warrior(player_id=1)
        warrior.health = warrior.max_health - 1
        self.board.add_unit(warrior, 3, 4)  # Adjacent to Cleric

        mass_heal_ability = next(a for a in self.cleric.abilities if a.name == "Mass Heal")
        mass_heal_ability_idx = self.cleric.abilities.index(mass_heal_ability)
        
        # Use Mass Heal ability
        mock_game = MagicMock()
        mock_game.board = self.board
        mock_game.turn_count = 1
        success = self.cleric.use_ability(mass_heal_ability_idx, (4, 4), game=mock_game)
        
        # Verify the warrior was healed exactly to max health
        self.assertEqual(warrior.health, warrior.max_health,
                        "Warrior should be healed only to max health")

class TestClericCleanseAbility(unittest.TestCase):
    """Tests specifically for the Cleric's Cleanse ability"""
    
    def setUp(self):
        """Set up a fresh board and units for each test"""
        # Initialize pygame and create a dummy screen
        pygame.init()
        self.screen = pygame.Surface((800, 600))  # Create an off-screen surface
        
        # Create the game board with the dummy screen
        self.board = GameBoard(self.screen)
        self.cleric = Cleric(player_id=1)
        self.cleric.current_ap = 10  # Ensure plenty of AP for testing
        
        # Place the cleric at a central position
        self.board.add_unit(self.cleric, 4, 4)
        
        # Set up the cleric's board reference (normally done by the game)
        self.cleric.board = self.board
    
    def tearDown(self):
        """Clean up pygame resources"""
        pygame.quit()
    
    def test_cleanse_ability_targeting(self):
        """Test that Cleanse ability correctly targets adjacent friendly units with negative status effects"""
        # Place friendly units in different states around the cleric
        
        # Friendly unit with status effects (should be targetable)
        affected_warrior = Warrior(player_id=1)
        affected_warrior.immobilized = True
        affected_warrior.immobilized_until = 2
        self.board.add_unit(affected_warrior, 4, 5)  # Adjacent (right)
        
        # Friendly unit with poisoned status (should be targetable)
        poisoned_warrior = Warrior(player_id=1)
        poisoned_warrior.poisoned = True
        poisoned_warrior.poison_remaining = 3
        self.board.add_unit(poisoned_warrior, 3, 4)  # Adjacent (left)
        
        # Friendly unit with stunned status (should be targetable)
        stunned_warrior = Warrior(player_id=1)
        stunned_warrior.stunned = True
        stunned_warrior.stunned_until = 1
        self.board.add_unit(stunned_warrior, 4, 3)  # Adjacent (up)
        
        # Friendly unit with chilled status (should be targetable)
        chilled_warrior = Warrior(player_id=1)
        chilled_warrior.chilled = True
        self.board.add_unit(chilled_warrior, 5, 4)  # Adjacent (down)
        
        # Friendly unit with no status effects (should NOT be targetable)
        clean_warrior = Warrior(player_id=1)
        self.board.add_unit(clean_warrior, 5, 5)  # Diagonal (not adjacent orthogonally)
        
        # Enemy unit with status effects (should NOT be targetable)
        enemy_warrior = Warrior(player_id=2)
        enemy_warrior.immobilized = True
        self.board.add_unit(enemy_warrior, 3, 3)  # Diagonal

        cleanse_ability = next(a for a in self.cleric.abilities if a.name == "Cleanse")
        cleanse_ability_idx = self.cleric.abilities.index(cleanse_ability)
        
        # Get valid targets for cleanse ability
        cleanse_targets = self.cleric.get_ability_targets(cleanse_ability_idx, self.board)
        
        # Verify that all friendly units with status effects are valid targets
        self.assertEqual(len(cleanse_targets), 4, "There should be four valid targets")
        self.assertIn((4, 5), cleanse_targets, "Immobilized friendly unit should be a valid target")
        self.assertIn((3, 4), cleanse_targets, "Poisoned friendly unit should be a valid target")
        self.assertIn((4, 3), cleanse_targets, "Stunned friendly unit should be a valid target")
        self.assertIn((5, 4), cleanse_targets, "Chilled friendly unit should be a valid target")
        self.assertNotIn((5, 5), cleanse_targets, "Clean friendly unit should not be a valid target")
        self.assertNotIn((3, 3), cleanse_targets, "Enemy unit should not be a valid target")
    
    def test_cleanse_ability_effect(self):
        """Test that Cleanse ability correctly removes all negative status effects"""
        # Create a unit with multiple status effects
        warrior = Warrior(player_id=1)
        warrior.immobilized = True
        warrior.immobilized_until = 2
        warrior.stunned = True
        warrior.stunned_until = 1
        warrior.chilled = True
        warrior.poisoned = True
        warrior.poison_remaining = 3
        
        # Place the warrior adjacent to the cleric
        self.board.add_unit(warrior, 4, 5)

        cleanse_ability = next(a for a in self.cleric.abilities if a.name == "Cleanse")
        cleanse_ability_idx = self.cleric.abilities.index(cleanse_ability)
        
        # Use the Cleanse ability on the warrior
        mock_game = MagicMock()
        mock_game.board = self.board
        mock_game.turn_count = 1 # Needed for status effect durations if they were set relative to game turns
        success = self.cleric.use_ability(cleanse_ability_idx, (4, 5), game=mock_game)
        
        # Verify the ability was used successfully
        self.assertTrue(success, "Cleanse ability should execute successfully")
        
        # Verify AP was consumed
        self.assertEqual(self.cleric.current_ap, 10 - self.cleric.abilities[4].ap_cost,
                        "AP should be consumed when using Cleanse ability")
        
        # Verify all status effects were removed
        self.assertFalse(warrior.immobilized, "Immobilized status should be removed")
        self.assertEqual(warrior.immobilized_until, 0, "Immobilized counter should be reset")
        self.assertFalse(warrior.stunned, "Stunned status should be removed")
        self.assertEqual(warrior.stunned_until, 0, "Stunned counter should be reset")
        self.assertFalse(warrior.chilled, "Chilled status should be removed")
        self.assertFalse(warrior.poisoned, "Poisoned status should be removed")
        self.assertEqual(warrior.poison_remaining, 0, "Poison counter should be reset")
    
    def test_cleanse_ability_no_effect_on_healthy_unit(self):
        """Test that Cleanse ability has no negative effects on a unit with no status effects"""
        # Create a clean unit
        warrior = Warrior(player_id=1)
        
        # Track initial health and state
        initial_health = warrior.health
        initial_ap = warrior.current_ap
        
        # Place the warrior adjacent to the cleric
        self.board.add_unit(warrior, 4, 5)

        cleanse_ability = next(a for a in self.cleric.abilities if a.name == "Cleanse")
        cleanse_ability_idx = self.cleric.abilities.index(cleanse_ability)
        
        # Use the Cleanse ability on the warrior
        mock_game = MagicMock()
        mock_game.board = self.board
        mock_game.turn_count = 1
        success = self.cleric.use_ability(cleanse_ability_idx, (4, 5), game=mock_game)
        
        # Even though the unit had no status effects, the ability should still succeed
        # since it targets any friendly unit
        self.assertTrue(success, "Cleanse ability should execute on clean units too")
        
        # Verify the unit's stats didn't change negatively
        self.assertEqual(warrior.health, initial_health, "Health should remain unchanged")
        self.assertEqual(warrior.current_ap, initial_ap, "AP should remain unchanged")
        self.assertFalse(warrior.immobilized, "Unit should still not be immobilized")
        self.assertFalse(warrior.stunned, "Unit should still not be stunned")
        self.assertFalse(warrior.chilled, "Unit should still not be chilled")
        self.assertFalse(warrior.poisoned, "Unit should still not be poisoned")
    
    def test_cleanse_ability_out_of_range(self):
        """Test that Cleanse ability can't target units out of range"""
        # Create a unit with status effects but place it out of range
        warrior = Warrior(player_id=1)
        warrior.stunned = True
        
        # Place the warrior too far from the cleric (diagonal instead of adjacent)
        self.board.add_unit(warrior, 5, 5)

        cleanse_ability = next(a for a in self.cleric.abilities if a.name == "Cleanse")
        cleanse_ability_idx = self.cleric.abilities.index(cleanse_ability)
        
        # Get valid targets for cleanse ability
        cleanse_targets = self.cleric.get_ability_targets(cleanse_ability_idx, self.board)
        
        # Verify the warrior is not a valid target
        self.assertNotIn((5, 5), cleanse_targets, "Unit that is not orthogonally adjacent should not be targetable")

class TestClericSanctuaryAbility(unittest.TestCase):
    """Tests specifically for the Cleric's Sanctuary ability"""
    
    def setUp(self):
        """Set up a fresh board and units for each test"""
        # Initialize pygame and create a dummy screen
        pygame.init()
        self.screen = pygame.Surface((800, 600))  # Create an off-screen surface
        
        # Create the game board with the dummy screen
        self.board = GameBoard(self.screen)
        self.cleric = Cleric(player_id=1)
        self.cleric.current_ap = 10  # Ensure plenty of AP for testing
        
        # Place the cleric at a central position
        self.board.add_unit(self.cleric, 4, 4)
        
        # Set up the cleric's board reference (normally done by the game)
        self.cleric.board = self.board
    
    def tearDown(self):
        """Clean up pygame resources"""
        pygame.quit()
    
    def test_sanctuary_ability_targeting(self):
        """Test that Sanctuary ability correctly targets adjacent friendly units"""
        # Place friendly units around the cleric
        warrior = Warrior(player_id=1)
        self.board.add_unit(warrior, 4, 5)  # Adjacent (right)
        
        # Place a friendly unit diagonally (should NOT be targetable)
        hunter = Hunter(player_id=1)
        self.board.add_unit(hunter, 5, 5)  # Diagonal
        
        # Place an enemy unit (should NOT be targetable)
        enemy = Warrior(player_id=2)
        self.board.add_unit(enemy, 3, 4)  # Adjacent (left)

        sanctuary_ability = next(a for a in self.cleric.abilities if a.name == "Sanctuary")
        sanctuary_ability_idx = self.cleric.abilities.index(sanctuary_ability)
        
        # Get valid targets for sanctuary ability
        sanctuary_targets = self.cleric.get_ability_targets(sanctuary_ability_idx, self.board)
        
        # Verify targeting
        self.assertIn((4, 5), sanctuary_targets, "Adjacent friendly unit should be a valid target")
        self.assertNotIn((5, 5), sanctuary_targets, "Diagonal unit should not be a valid target")
        self.assertNotIn((3, 4), sanctuary_targets, "Enemy unit should not be a valid target")
    
    def test_sanctuary_ability_effect(self):
        """Test that Sanctuary ability correctly makes a unit untargetable"""
        # Place friendly and enemy units
        friendly_warrior = Warrior(player_id=1)
        self.board.add_unit(friendly_warrior, 4, 5)  # Adjacent to Cleric
        
        enemy_warrior = Warrior(player_id=2)
        self.board.add_unit(enemy_warrior, 4, 6)  # Adjacent to friendly warrior
        
        # Verify that the enemy can initially target our friendly unit
        enemy_attack_targets = enemy_warrior.get_valid_attacks(self.board)
        self.assertIn((4, 5), enemy_attack_targets, "Enemy should be able to target our unit initially")

        sanctuary_ability = next(a for a in self.cleric.abilities if a.name == "Sanctuary")
        sanctuary_ability_idx = self.cleric.abilities.index(sanctuary_ability)
        
        # Use Sanctuary ability on our friendly unit
        mock_game = MagicMock()
        mock_game.board = self.board
        mock_game.turn_count = 1
        success = self.cleric.use_ability(sanctuary_ability_idx, (4, 5), game=mock_game)
        
        # Verify the ability was used successfully
        self.assertTrue(success, "Sanctuary ability should execute successfully")
        
        # Verify AP was consumed
        self.assertEqual(self.cleric.current_ap, 10 - self.cleric.abilities[5].ap_cost, 
                        "AP should be consumed when using Sanctuary ability")
        
        # Verify the unit now has sanctuary status
        self.assertTrue(friendly_warrior.sanctuary, "Unit should have sanctuary status applied")
        
        # Verify the enemy can no longer target our friendly unit
        enemy_attack_targets = enemy_warrior.get_valid_attacks(self.board)
        self.assertNotIn((4, 5), enemy_attack_targets, "Enemy should not be able to target a unit with sanctuary")
    
    def test_sanctuary_duration(self):
        """Test that Sanctuary effect lasts for the correct duration (1 turn)"""
        # Place a friendly unit
        warrior = Warrior(player_id=1)
        self.board.add_unit(warrior, 4, 5)

        sanctuary_ability = next(a for a in self.cleric.abilities if a.name == "Sanctuary")
        sanctuary_ability_idx = self.cleric.abilities.index(sanctuary_ability)
        
        # Use Sanctuary on the warrior
        mock_game = MagicMock()
        mock_game.board = self.board
        mock_game.turn_count = 1 # Initial turn count when ability is used
        self.cleric.use_ability(sanctuary_ability_idx, (4, 5), game=mock_game)
        
        # Verify sanctuary is active
        self.assertTrue(warrior.sanctuary, "Sanctuary should be active after using the ability")
        self.assertIn((4, 5), self.cleric.sanctuary_units, "Unit should be in sanctuary_units list")
        self.assertEqual(self.cleric.sanctuary_until, 1, "Sanctuary should last for 1 turn cycle")
        
        # Simulate end of turn
        self.cleric.reset_turn()
        
        # Sanctuary should now be gone
        self.assertFalse(warrior.sanctuary, "Sanctuary should be removed after one turn cycle")
        self.assertEqual(self.cleric.sanctuary_until, 0, "Sanctuary duration should be 0")
        self.assertNotIn((4, 5), self.cleric.sanctuary_units, "Unit should no longer be in sanctuary_units list")

if __name__ == "__main__":
    unittest.main()

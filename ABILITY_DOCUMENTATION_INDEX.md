# 📚 Ability Documentation Index

## 🎯 **Where to Find Ability Documentation**

### **📋 Main Documentation Files:**

1. **`HUNTER_ABILITY_DOCUMENTATION.md`** - Complete Hunter ability specifications
2. **`GAME_DESIGN.md`** - Overall game design and ability concepts  
3. **`NEW_CLASSES_GUIDE.md`** - Documentation for all classes
4. **`STATUS_EFFECTS_GUIDE.md`** - Status effect system documentation
5. **`ABILITY_FIXES_SUMMARY.md`** - Recent ability fixes and improvements
6. **`REFACTORING_SUMMARY.md`** - Architecture improvements and system changes

### **🧪 Test Files (Contain Ability Specifications):**

- **`test_all_hunter_abilities.py`** - Complete Hunter abilities test
- **`test_hunter_abilities.py`** - Triple Shot and Spread Shot tests  
- **`test_hunter_spread_shot.py`** - Specific Spread Shot tests
- **`test_ricochet_shot.py`** - Ricochet Shot scenarios
- **`test_mage_targeting_fixes.py`** - Mage ability targeting tests
- **`test_mage_blink_movement.py`** - Mage movement specifications
- **`test_warrior_abilities.py`** - Warrior ability tests
- **`test_all_classes_abilities.py`** - Cross-class ability testing

### **💻 Source Code Documentation:**

Each unit class file contains detailed ability descriptions:
- **`units/hunter.py`** - Hunter abilities with comments
- **`units/warrior.py`** - Warrior abilities with comments
- **`units/mage.py`** - Mage abilities with comments
- **`units/rogue.py`** - Rogue abilities with comments
- **`units/cleric.py`** - Cleric abilities with comments

## 🎮 **Complete Ability Reference**

### **🏹 Hunter Abilities:**

| Ability | Pattern | Behavior | Status |
|---------|---------|----------|---------|
| **Basic Attack** | Diagonal | Single target damage | ✅ Working |
| **Ricochet Shot** | Diagonal | Hit primary + bounce **twice** (3 targets total) | ✅ **UPDATED** |
| **Triple Shot** | Diagonal | 3 arrows same direction | ✅ Working |
| **Knockback Shot** | Diagonal | Damage + knockback | ✅ Working |
| **Spread Shot** | Orthogonal | 3 arrows in spread pattern (orthogonal + 2 diagonals) | ✅ **FIXED** |
| **Crippling Shot** | Diagonal | Damage + Crippled (1 turn) | ✅ **FIXED** |

### **🔮 Mage Abilities:**

| Ability | Pattern | Behavior | Status |
|---------|---------|----------|---------|
| **Basic Attack** | Adjacent | Weak melee attack | ✅ Working |
| **Fireball** | **Orthogonal only** | Projectile + splash damage | ✅ **FIXED** |
| **Ice Spike** | **Orthogonal only** | Projectile damage | ✅ **FIXED** |
| **Teleport** | Circular (3 range) | Blink to empty tile | ✅ Working |
| **Frost Nova** | Self-centered AoE | Freeze nearby enemies | ✅ Working |
| **Arcane Missile** | Orthogonal | 3 missiles in direction | ✅ Working |
| **Cone of Cold** | T-pattern | Orthogonal + 3 tiles at distance 2 | ✅ Working |

### **⚔️ Warrior Abilities:**

| Ability | Pattern | Behavior | Status |
|---------|---------|----------|---------|
| **Basic Attack** | Adjacent | Melee damage | ✅ Working |
| **Cleave Attack** | Adjacent AoE | Hit multiple adjacent enemies | ✅ Working |
| **Shield Bash** | Adjacent | Damage + Stun (1 turn) | ✅ Working |
| **Charge** | Line | Move + attack at end | ⚠️ Targeting issues |
| **Defensive Stance** | Self | Reduce incoming damage | ✅ Working |
| **Riposte** | Self | Counter-attack when hit | ✅ Working |

### **🗡️ Rogue Abilities:**

| Ability | Pattern | Behavior | Status |
|---------|---------|----------|---------|
| **Basic Attack** | Diagonal | Single target damage | ✅ Working |
| **Backstab** | Knight move + diagonal | L-shaped move + attack | ⚠️ Targeting issues |
| **Poison Strike** | Diagonal | Damage + poison effect | ✅ Working |
| **Smoke Bomb** | Self-centered AoE | Concealment effect | ✅ Working |
| **Shadow Step** | Teleport | Move to empty tile | ✅ Working |
| **Fan of Knives** | Knight pattern | Damage all knight-move tiles | ✅ Working |
| **Assassination** | Diagonal adjacent | Double damage if target <50% HP | ✅ Working |

### **🛡️ Cleric Abilities:**

| Ability | Pattern | Behavior | Status |
|---------|---------|----------|---------|
| **Basic Attack** | Adjacent | Weak melee attack | ✅ Working |
| **Heal** | Orthogonal (1 tile) | Restore ally HP | ✅ Working |
| **Mass Heal** | + pattern | Heal all units in + shape | ✅ Working |
| **Cleanse** | Orthogonal (1 tile) | Remove negative status effects | ✅ **FIXED** |
| **Sanctuary** | Orthogonal (1 tile) | Protect ally from damage | ✅ Working |
| **Divine Protection** | Self | Damage reduction buff | ✅ Working |
| **Holy Smite** | Orthogonal (1 tile) | Damage enemy | ✅ **FIXED** |

## 🔧 **Recent Fixes Applied:**

### **Hunter Fixes:**
1. **Ricochet Shot**: Now bounces twice to hit 3 targets total
2. **Spread Shot**: Fixed to use proper spread pattern (orthogonal + 2 diagonals)
3. **Crippling Shot**: Duration now properly decrements (1 turn)

### **Mage Fixes:**
1. **Fireball/Ice Spike**: Restricted to orthogonal lines only
2. **Targeting Consistency**: Fixed targeting behavior after movement

### **System Fixes:**
1. **Status Effects**: Duration properly decrements each turn
2. **Configuration**: Health sliders work for all classes
3. **UI Display**: Status effects show correctly without crashes

## 📍 **How to Find Specific Information:**

### **For Ability Mechanics:**
- Check the specific unit class file (e.g., `units/hunter.py`)
- Look for the `_use_[ability_name]` method
- Read the docstring and implementation

### **For Targeting Patterns:**
- Check the `get_ability_targets` method in unit class files
- Look at test files for expected behavior
- Check `HUNTER_ABILITY_DOCUMENTATION.md` for detailed patterns

### **For Status Effects:**
- Check `core/status_effects.py` for effect definitions
- Look at `STATUS_EFFECTS_GUIDE.md` for documentation
- Check unit class files for how effects are applied

### **For Configuration:**
- Check `game_balance_config.json` for current values
- Look at `core/configuration_manager.py` for how config is loaded
- Check test files for configuration testing

## 🎯 **Quick Reference:**

- **All abilities working**: Mage (100%), Cleric (100%)
- **Most abilities working**: Hunter (83%), Warrior (83%), Rogue (86%)
- **Main issues remaining**: Complex targeting logic for Charge and Backstab
- **Configuration**: All health sliders working correctly
- **Status effects**: All working with proper duration

---

**Last Updated**: After Hunter and Mage fixes  
**Overall Status**: 30/33 abilities working (91% success rate)  
**Documentation Status**: ✅ COMPREHENSIVE

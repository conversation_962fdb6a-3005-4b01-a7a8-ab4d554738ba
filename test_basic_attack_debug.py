#!/usr/bin/env python3

import pygame
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state import Game
from units.warrior import Warrior
from units.rogue import Rogue

def test_basic_attack():
    """Test just the basic attack to debug the issue"""
    print("🔍 TESTING BASIC ATTACK DEBUG")
    print("=" * 40)
    
    # Initialize pygame
    pygame.init()
    
    # Create a game instance
    game = Game()
    
    # Create units
    warrior = Warrior(player_id=1)
    target = Rogue(player_id=2)
    
    game.board.add_unit(warrior, 4, 4)
    game.board.add_unit(target, 4, 5)  # Adjacent for attack
    
    # Set up game state
    game.current_player = 1
    game.current_player_ap = 10
    game.units_acted_this_turn = set()
    
    print(f"Warrior at {warrior.position}")
    print(f"Target at {target.position}")
    print(f"Warrior HP: {warrior.health}/{warrior.max_health}")
    print(f"Target HP: {target.health}/{target.max_health}")
    print(f"Game AP: {game.current_player_ap}")
    print(f"Warrior abilities: {[a.name for a in warrior.abilities]}")
    print(f"Basic Attack AP cost: {warrior.abilities[1].ap_cost}")
    
    # Test Basic Attack
    print("\n📋 TESTING BASIC ATTACK")
    attack_idx = 1  # Basic Attack
    
    print(f"Calling warrior.use_ability({attack_idx}, {target.position}, game)")
    success = warrior.use_ability(attack_idx, target.position, game)
    print(f"Attack success: {success}")
    print(f"Target HP after attack: {target.health}/{target.max_health}")
    print(f"Game AP after attack: {game.current_player_ap}")
    
    return success

if __name__ == "__main__":
    success = test_basic_attack()
    if success:
        print("\n✅ BASIC ATTACK WORKING!")
    else:
        print("\n❌ BASIC ATTACK BROKEN!")

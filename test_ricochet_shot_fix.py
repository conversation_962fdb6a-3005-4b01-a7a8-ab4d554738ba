#!/usr/bin/env python3

import pygame
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state import Game
from units.hunter import Hunter
from units.warrior import Warrior
from units.rogue import <PERSON>

def test_ricochet_shot_fix():
    """Test that <PERSON>'s Ricochet Shot works properly with diagonal bounces"""
    print("🏹 TESTING HUNTER RICOCHET SHOT FIX")
    print("=" * 50)
    
    # Initialize pygame
    pygame.init()
    
    try:
        # Create a game instance
        game = Game()
        
        # Create units
        hunter = Hunter(player_id=1)
        primary_target = Warrior(player_id=2)
        ricochet_target = Rogue(player_id=2)
        
        # Set up a ricochet scenario:
        # Hunter at (4,4), Primary target at (5,5) (diagonal), Ricochet target at (6,6) (diagonal)
        game.board.add_unit(hunter, 4, 4)
        game.board.add_unit(primary_target, 5, 5)  # Diagonal from hunter (valid Hunter attack)
        game.board.add_unit(ricochet_target, 6, 6)  # Diagonal from primary target
        
        # Set up game state
        game.current_player = 1
        game.current_player_ap = 10
        game.units_acted_this_turn = set()
        
        print(f"Hunter at {hunter.position}")
        print(f"Primary target (Warrior) at {primary_target.position}")
        print(f"Ricochet target (Rogue) at {ricochet_target.position}")
        print(f"Primary target HP: {primary_target.health}/{primary_target.max_health}")
        print(f"Ricochet target HP: {ricochet_target.health}/{ricochet_target.max_health}")
        
        # Find ricochet shot ability
        ricochet_idx = None
        for i, ability in enumerate(hunter.abilities):
            if ability.name == "Ricochet Shot":
                ricochet_idx = i
                break
        
        if ricochet_idx is None:
            print("❌ Ricochet Shot ability not found!")
            return False
        
        print(f"✓ Found Ricochet Shot ability (index {ricochet_idx})")
        
        # Get valid targets
        valid_targets = hunter.get_ability_targets(ricochet_idx, game.board)
        print(f"✓ Valid ricochet targets: {valid_targets}")
        
        if primary_target.position not in valid_targets:
            print(f"❌ Primary target {primary_target.position} not in valid targets!")
            return False
        
        print(f"✓ Primary target {primary_target.position} is valid")
        
        # Test ricochet shot
        print(f"\n🏹 TESTING RICOCHET SHOT")
        print(f"Shooting at primary target {primary_target.position}")
        print(f"Expected ricochet to {ricochet_target.position} (diagonal)")
        
        primary_initial_hp = primary_target.health
        ricochet_initial_hp = ricochet_target.health
        
        success = hunter.use_ability(ricochet_idx, primary_target.position, game)
        
        primary_damage = primary_initial_hp - primary_target.health
        ricochet_damage = ricochet_initial_hp - ricochet_target.health
        
        print(f"\nRicochet Shot success: {success}")
        print(f"Primary target damage: {primary_damage}")
        print(f"Ricochet target damage: {ricochet_damage}")
        print(f"Primary target HP: {primary_target.health}/{primary_target.max_health}")
        print(f"Ricochet target HP: {ricochet_target.health}/{ricochet_target.max_health}")
        
        # Verify results
        if success and primary_damage > 0 and ricochet_damage > 0:
            print("✅ RICOCHET SHOT WORKING!")
            print("✅ Both primary and ricochet targets took damage")
            print("✅ Diagonal ricochet functioning properly")
            return True
        elif success and primary_damage > 0 and ricochet_damage == 0:
            print("⚠️ Primary target hit but no ricochet occurred")
            print("❌ Ricochet logic may need adjustment")
            return False
        else:
            print("❌ RICOCHET SHOT FAILED")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ricochet_physics():
    """Test ricochet physics with different target arrangements"""
    print("\n🎯 TESTING RICOCHET PHYSICS")
    print("-" * 30)
    
    # Initialize pygame
    pygame.init()
    
    try:
        # Create a game instance
        game = Game()
        
        # Create units
        hunter = Hunter(player_id=1)
        
        # Test scenario: Multiple potential ricochet targets
        primary_target = Warrior(player_id=2)
        diagonal_target = Rogue(player_id=2)
        orthogonal_target = Warrior(player_id=2)
        
        # Hunter shoots east, should prefer diagonal ricochet
        game.board.add_unit(hunter, 3, 3)
        game.board.add_unit(primary_target, 3, 4)  # East of hunter
        game.board.add_unit(diagonal_target, 4, 5)  # Southeast of primary (diagonal)
        game.board.add_unit(orthogonal_target, 3, 5)  # East of primary (orthogonal)
        
        game.current_player = 1
        game.current_player_ap = 10
        game.units_acted_this_turn = set()
        
        print(f"Hunter at {hunter.position}")
        print(f"Primary target at {primary_target.position}")
        print(f"Diagonal option at {diagonal_target.position}")
        print(f"Orthogonal option at {orthogonal_target.position}")
        
        # Find ricochet shot ability
        ricochet_idx = None
        for i, ability in enumerate(hunter.abilities):
            if ability.name == "Ricochet Shot":
                ricochet_idx = i
                break
        
        diagonal_initial_hp = diagonal_target.health
        orthogonal_initial_hp = orthogonal_target.health
        
        print(f"\n🏹 Testing ricochet physics (should prefer diagonal)")
        success = hunter.use_ability(ricochet_idx, primary_target.position, game)
        
        diagonal_damage = diagonal_initial_hp - diagonal_target.health
        orthogonal_damage = orthogonal_initial_hp - orthogonal_target.health
        
        print(f"Diagonal target damage: {diagonal_damage}")
        print(f"Orthogonal target damage: {orthogonal_damage}")
        
        if diagonal_damage > 0 and orthogonal_damage == 0:
            print("✅ PHYSICS WORKING: Preferred diagonal ricochet!")
            return True
        elif orthogonal_damage > 0 and diagonal_damage == 0:
            print("⚠️ Used orthogonal ricochet instead of diagonal")
            return False
        else:
            print("❌ Unexpected ricochet behavior")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    test1_success = test_ricochet_shot_fix()
    test2_success = test_ricochet_physics()
    
    if test1_success and test2_success:
        print("\n🎉 ALL RICOCHET SHOT TESTS PASSED!")
        print("✅ Diagonal ricochets working properly")
        print("✅ Ricochet physics implemented correctly")
    else:
        print("\n💥 SOME RICOCHET SHOT TESTS FAILED!")
        print("❌ Ricochet shot needs further fixes")

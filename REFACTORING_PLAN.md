# 🔧 REFACTORING PLAN - TACTICAL PVP STRATEGY GAME

## 🎯 **PRIORITY 1: AP SYSTEM OVERHAUL** ⭐⭐⭐

### **Current System (Per-Unit AP):**
```python
# Each unit has individual AP
unit.current_ap = 6
unit.max_ap = 6
unit.use_ability(ability_idx, target, game)  # Deducts from unit.current_ap
```

### **Target System (Global Player AP):**
```python
# Global AP pool per player
game.player1_ap = 12  # Total AP for player 1's turn
game.player2_ap = 12  # Total AP for player 2's turn
game.current_player_ap = 12  # Active player's remaining AP

# Units can only do ONE action per turn (unless exceptions)
unit.has_acted_this_turn = False
unit.use_ability(ability_idx, target, game)  # Deducts from game.current_player_ap
```

### **Implementation Steps:**
1. **Add global AP to Game class**
2. **Add action tracking to units**
3. **Modify ability system to use global AP**
4. **Update UI to show global AP**
5. **Add "one action per unit" enforcement**
6. **Handle exceptions (multi-action abilities)**

---

## 🎯 **PRIORITY 2: TESTING EXPANSION** ⭐⭐

### **Current Testing:**
- ✅ Basic unit tests exist
- ✅ Some integration tests
- ❌ Missing comprehensive game flow tests
- ❌ Missing AP system tests

### **Needed Tests:**
1. **AP System Tests**
   - Global AP management
   - One action per unit enforcement
   - AP cost validation
   - Turn transitions

2. **Game Flow Tests**
   - Complete turn cycles
   - Victory conditions
   - Setup to gameplay transition
   - Error handling

3. **Unit Integration Tests**
   - All abilities with new AP system
   - Status effects with turn management
   - Passive abilities integration

---

## 🎯 **PRIORITY 3: CODE ORGANIZATION** ⭐

### **Current Structure: GOOD**
```
✅ Clear separation of concerns
✅ Modular unit classes
✅ Separate game logic files
✅ Configuration system
```

### **Minor Improvements Needed:**
1. **Constants Consolidation**
   - Move scattered constants to `game_constants.py`
   - Remove duplicate definitions

2. **Error Handling**
   - Add consistent error handling patterns
   - Improve debugging information

3. **Documentation**
   - Add docstrings to key methods
   - Update README with new AP system

---

## 🎯 **PRIORITY 4: PERFORMANCE & POLISH** ⭐

### **Current Performance: ADEQUATE**
- Game runs smoothly for current scope
- No major bottlenecks identified

### **Future Optimizations:**
1. **Caching**
   - Cache ability target calculations
   - Cache movement patterns

2. **Memory Management**
   - Optimize image loading
   - Clean up temporary objects

---

## 📋 **IMPLEMENTATION TIMELINE**

### **Phase 1: AP System (1-2 days)**
1. Design global AP system
2. Implement core changes
3. Update all abilities
4. Test basic functionality

### **Phase 2: Testing (1 day)**
1. Create AP system tests
2. Add game flow tests
3. Regression testing

### **Phase 3: Polish (0.5 days)**
1. Update UI for global AP
2. Add error handling
3. Documentation updates

---

## 🎮 **AP SYSTEM DESIGN PROPOSAL**

### **Global AP Pool:**
```python
class Game:
    def __init__(self):
        self.max_ap_per_turn = 12  # Configurable
        self.current_player_ap = 12
        self.units_acted_this_turn = set()  # Track which units acted
```

### **Unit Action Tracking:**
```python
class Unit:
    def __init__(self):
        self.has_acted_this_turn = False
        self.can_act_multiple_times = False  # For special abilities
```

### **Ability Cost System:**
```python
def use_ability(self, ability_idx, target_pos, game):
    # Check if unit can act
    if self.has_acted_this_turn and not self.can_act_multiple_times:
        return False
    
    # Check global AP
    ap_cost = self.abilities[ability_idx].ap_cost
    if game.current_player_ap < ap_cost:
        return False
    
    # Execute ability
    success = self._execute_ability(ability_idx, target_pos, game)
    
    if success:
        # Deduct from global AP
        game.current_player_ap -= ap_cost
        # Mark unit as acted
        self.has_acted_this_turn = True
        game.units_acted_this_turn.add(self)
    
    return success
```

### **Turn Reset:**
```python
def reset_turn(game):
    # Reset global AP
    game.current_player_ap = game.max_ap_per_turn
    
    # Reset all units' action status
    for unit in game.board.units.values():
        if unit.player_id == game.current_player:
            unit.has_acted_this_turn = False
    
    # Clear acted units tracking
    game.units_acted_this_turn.clear()
```

---

## 🧪 **TESTING STRATEGY**

### **Test Categories:**
1. **Unit Tests** - Individual components
2. **Integration Tests** - System interactions  
3. **Game Flow Tests** - Complete scenarios
4. **Regression Tests** - Prevent breaking changes

### **Key Test Scenarios:**
1. **AP Management**
   - Global AP deduction
   - One action per unit
   - Turn transitions
   - Invalid actions

2. **Game Flow**
   - Setup → Gameplay → Victory
   - Turn cycling
   - Error recovery

3. **Unit Abilities**
   - All abilities with new AP system
   - Status effects
   - Passive abilities

---

## 🎯 **RECOMMENDATIONS**

### **DO NOW:**
1. ✅ **Implement Global AP System** - Most impactful change
2. ✅ **Add comprehensive tests** - Ensure stability
3. ✅ **Update documentation** - Keep team aligned

### **DO LATER:**
1. ⏳ **Performance optimizations** - Only if needed
2. ⏳ **Advanced features** - After core is stable
3. ⏳ **UI polish** - After gameplay is finalized

### **DON'T DO YET:**
1. ❌ **Major architecture changes** - Current structure is good
2. ❌ **Premature optimization** - Focus on functionality first
3. ❌ **Feature creep** - Finish current features first

---

## 🎮 **GAME FLOW IMPACT**

### **Current Flow:**
```
Turn Start → Unit Selection → Multiple Actions → End Turn
```

### **New Flow:**
```
Turn Start (12 AP) → Unit Selection → One Action → Repeat → End Turn
```

### **Strategic Impact:**
- **More tactical decisions** - Limited actions per turn
- **Resource management** - Global AP pool
- **Turn planning** - Must prioritize actions
- **Faster gameplay** - Clearer turn structure

This change will significantly improve the tactical depth and game flow!

#!/usr/bin/env python3
"""
Test script for movement fixes: max ranges and knight movement for Rogue
"""

import pygame
from game_state import Game
from units.hunter import Hunter
from units.warrior import Warrior
from units.rogue import Rogue
from units.mage import Mage
from units.cleric import Cleric
from units.pawn import Pawn
from units.king import <PERSON>
from game_config import GAME_CONFIG

def test_movement_fixes():
    """Test max movement ranges and Rogue knight movement"""
    pygame.init()
    
    print("🚀 MOVEMENT FIXES TEST 🚀")
    print("=" * 40)
    
    # Test 1: Check new max movement ranges
    print("\n📋 TEST 1: Max Movement Ranges")
    print("-" * 30)
    
    print("New movement ranges from config:")
    print(f"  Hunter: {GAME_CONFIG.get('hunter_config', {}).get('movement_range', 'NOT SET')}")
    print(f"  Warrior: {GAME_CONFIG.get('warrior_config', {}).get('movement_range', 'NOT SET')}")
    print(f"  Rogue: {GAME_CONFIG.get('rogue_config', {}).get('movement_range', 'NOT SET')}")
    print(f"  Mage: {GAME_CONFIG.get('mage_config', {}).get('movement_range', 'NOT SET')}")
    print(f"  Cleric: {GAME_CONFIG.get('cleric_config', {}).get('movement_range', 'NOT SET')}")
    print(f"  Pawn: {GAME_CONFIG.get('pawn_config', {}).get('movement_range', 'NOT SET')}")
    print(f"  King: {GAME_CONFIG.get('king_config', {}).get('movement_range', 'NOT SET')}")
    
    # Test 2: Test Hunter with max range
    print("\n\n📋 TEST 2: Hunter with Max Range")
    print("-" * 35)
    
    game = Game()
    hunter = Hunter(1)
    hunter.position = (4, 4)
    hunter.board = game.board
    game.board.units = {(4, 4): hunter}
    
    moves = hunter.get_valid_moves(game.board)
    max_distance = max([max(abs(move[0] - 4), abs(move[1] - 4)) for move in moves]) if moves else 0
    
    print(f"Hunter at (4, 4):")
    print(f"  Config range: {GAME_CONFIG['hunter_config']['movement_range']}")
    print(f"  Actual max distance: {max_distance}")
    print(f"  Total moves available: {len(moves)}")
    print(f"  ✅ Can move {max_distance} tiles")
    
    # Show some example moves
    if moves:
        print(f"  Example moves: {moves[:8]}...")
    
    # Test 3: Test Rogue knight movement
    print("\n\n📋 TEST 3: Rogue Knight Movement")
    print("-" * 35)
    
    rogue = Rogue(1)
    rogue.position = (4, 4)
    rogue.board = game.board
    game.board.units = {(4, 4): rogue}
    
    rogue_moves = rogue.get_valid_moves(game.board)
    
    print(f"Rogue at (4, 4):")
    print(f"  Config range: {GAME_CONFIG['rogue_config']['movement_range']}")
    print(f"  Total moves available: {len(rogue_moves)}")
    
    # Analyze movement patterns
    knight_patterns = []
    for move in rogue_moves:
        dr, dc = move[0] - 4, move[1] - 4
        # Check if it's a valid knight move pattern
        if (abs(dr) == 2 and abs(dc) == 1) or (abs(dr) == 1 and abs(dc) == 2):
            knight_patterns.append(move)
    
    print(f"  Knight-pattern moves: {len(knight_patterns)}")
    print(f"  ✅ Uses L-shaped movement: {len(knight_patterns) > 0}")
    
    # Show some example knight moves
    if knight_patterns:
        print(f"  Example L-shaped moves: {knight_patterns[:8]}")
    
    # Test 4: Test Rogue jumping over entities
    print("\n\n📋 TEST 4: Rogue Jumping Over Entities")
    print("-" * 40)
    
    # Place some blocking units
    warrior1 = Warrior(2)
    warrior2 = Warrior(2)
    warrior1.position = (3, 3)
    warrior2.position = (2, 4)
    
    game.board.units = {
        (4, 4): rogue,
        (3, 3): warrior1,
        (2, 4): warrior2
    }
    
    rogue_moves_blocked = rogue.get_valid_moves(game.board)
    
    print(f"Rogue at (4, 4) with blocking units at (3, 3) and (2, 4):")
    print(f"  Moves available: {len(rogue_moves_blocked)}")
    
    # Check if rogue can still reach positions that would be blocked by normal movement
    can_jump = False
    for move in rogue_moves_blocked:
        # Check if this move would require jumping over a blocking unit
        if move == (2, 3):  # This would be a knight move that jumps over (3, 3)
            can_jump = True
            break
    
    print(f"  ✅ Can jump over entities: {can_jump}")
    
    # Test 5: Test other units with max ranges
    print("\n\n📋 TEST 5: Other Units with Max Ranges")
    print("-" * 40)
    
    units = [
        ("Warrior", Warrior(1)),
        ("Mage", Mage(1)),
        ("Cleric", Cleric(1)),
        ("Pawn", Pawn(1)),
        ("King", King(1))
    ]
    
    for unit_name, unit in units:
        unit.position = (4, 4)
        unit.board = game.board
        game.board.units = {(4, 4): unit}
        
        moves = unit.get_valid_moves(game.board)
        max_distance = max([max(abs(move[0] - 4), abs(move[1] - 4)) for move in moves]) if moves else 0
        config_range = GAME_CONFIG.get(f"{unit_name.lower()}_config", {}).get("movement_range", "NOT SET")
        
        print(f"  {unit_name}: {max_distance} tiles (config: {config_range}) ✅")
    
    print("\n" + "=" * 40)
    print("🎉 MOVEMENT FIXES COMPLETED!")
    print("\n✅ Fixes Applied:")
    print("  • All units have max movement ranges (3-5 tiles)")
    print("  • Hunter can move up to 5 tiles diagonally")
    print("  • Rogue moves like a knight (L-shaped)")
    print("  • Rogue can jump over entities")
    print("  • Config sliders should now work properly")
    
    print("\n🎮 Movement is now much more flexible!")

if __name__ == "__main__":
    test_movement_fixes()

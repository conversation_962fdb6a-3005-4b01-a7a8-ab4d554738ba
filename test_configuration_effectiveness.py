#!/usr/bin/env python3

import pygame
import sys
import os
import json
import json

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state import Game
from units.warrior import Warrior
from units.mage import Mage
from units.hunter import <PERSON>
from game_config import GAME_CONFIG

def test_configuration_effectiveness():
    """Test if configuration changes actually affect gameplay stats and costs"""
    print("🎮 TESTING CONFIGURATION EFFECTIVENESS")
    print("=" * 50)
    
    # Initialize pygame
    pygame.init()
    
    # Test 1: Baseline Configuration
    print("\n📋 TEST 1: Baseline Configuration")
    print("-" * 30)
    
    game = Game()
    
    # Create units with current config
    warrior = Warrior(player_id=1)
    mage = Mage(player_id=1)
    hunter = Hunter(player_id=1)
    
    # Record baseline stats
    baseline_warrior_hp = warrior.max_health
    baseline_mage_hp = mage.max_health
    baseline_hunter_hp = hunter.max_health
    
    # Find ability costs
    warrior_attack_cost = None
    mage_fireball_cost = None
    hunter_multishot_cost = None
    
    for ability in warrior.abilities:
        if ability.name == "Basic Attack":
            warrior_attack_cost = ability.ap_cost
            break
    
    for ability in mage.abilities:
        if ability.name == "Fireball":
            mage_fireball_cost = ability.ap_cost
            break
    
    for ability in hunter.abilities:
        if ability.name == "Multishot":
            hunter_multishot_cost = ability.ap_cost
            break
    
    print(f"Baseline Warrior HP: {baseline_warrior_hp}")
    print(f"Baseline Mage HP: {baseline_mage_hp}")
    print(f"Baseline Hunter HP: {baseline_hunter_hp}")
    print(f"Baseline Warrior Attack Cost: {warrior_attack_cost}")
    print(f"Baseline Mage Fireball Cost: {mage_fireball_cost}")
    print(f"Baseline Hunter Multishot Cost: {hunter_multishot_cost}")
    
    # Test 2: Modify Configuration (JSON file)
    print("\n📋 TEST 2: Modifying Configuration")
    print("-" * 30)

    # Create backup of original JSON config
    original_json_config = None
    if os.path.exists("game_balance_config.json"):
        with open("game_balance_config.json", 'r') as f:
            original_json_config = json.load(f)

    # Create new configuration with modified values
    new_config = {
        "class_data": {
            "Warrior": {"hp": 25, "movement": 3},  # Significantly different
            "Mage": {"hp": 12, "movement": 3},     # Significantly different
            "Hunter": {"hp": 18, "movement": 4}    # Significantly different
        },
        "ability_data": {
            "Warrior": {
                "Attack": {"ap_cost": 6, "damage": 4, "cooldown": 1}  # Very different
            },
            "Mage": {
                "Fireball": {"ap_cost": 8, "damage": 5, "cooldown": 3}  # Very different
            },
            "Hunter": {
                "Multishot": {"ap_cost": 7, "damage": 3, "cooldown": 2}  # Very different
            }
        }
    }

    # Write new configuration to file
    with open("game_balance_config.json", 'w') as f:
        json.dump(new_config, f, indent=2)

    print("Modified configuration (saved to JSON):")
    print(f"  Warrior HP: {new_config['class_data']['Warrior']['hp']}")
    print(f"  Mage HP: {new_config['class_data']['Mage']['hp']}")
    print(f"  Hunter HP: {new_config['class_data']['Hunter']['hp']}")
    print(f"  Warrior Attack Cost: {new_config['ability_data']['Warrior']['Attack']['ap_cost']}")
    print(f"  Mage Fireball Cost: {new_config['ability_data']['Mage']['Fireball']['ap_cost']}")
    print(f"  Hunter Multishot Cost: {new_config['ability_data']['Hunter']['Multishot']['ap_cost']}")
    
    # Test 3: Create New Units with Modified Config
    print("\n📋 TEST 3: Testing New Units with Modified Config")
    print("-" * 30)
    
    # Create new units (should use modified config)
    new_warrior = Warrior(player_id=1)
    new_mage = Mage(player_id=1)
    new_hunter = Hunter(player_id=1)
    
    # Check if new units have modified stats
    new_warrior_hp = new_warrior.max_health
    new_mage_hp = new_mage.max_health
    new_hunter_hp = new_hunter.max_health
    
    # Find new ability costs
    new_warrior_attack_cost = None
    new_mage_fireball_cost = None
    new_hunter_multishot_cost = None
    
    for ability in new_warrior.abilities:
        if ability.name == "Basic Attack":
            new_warrior_attack_cost = ability.ap_cost
            break
    
    for ability in new_mage.abilities:
        if ability.name == "Fireball":
            new_mage_fireball_cost = ability.ap_cost
            break
    
    for ability in new_hunter.abilities:
        if ability.name == "Multishot":
            new_hunter_multishot_cost = ability.ap_cost
            break
    
    print(f"New Warrior HP: {new_warrior_hp}")
    print(f"New Mage HP: {new_mage_hp}")
    print(f"New Hunter HP: {new_hunter_hp}")
    print(f"New Warrior Attack Cost: {new_warrior_attack_cost}")
    print(f"New Mage Fireball Cost: {new_mage_fireball_cost}")
    print(f"New Hunter Multishot Cost: {new_hunter_multishot_cost}")
    
    # Test 4: Verify Changes
    print("\n📋 TEST 4: Verification Results")
    print("-" * 30)
    
    hp_changes_work = (
        new_warrior_hp == 25 and  # Expected new values
        new_mage_hp == 12 and
        new_hunter_hp == 18
    )

    cost_changes_work = (
        new_warrior_attack_cost == 6 and  # Expected new values
        new_mage_fireball_cost == 8 and
        new_hunter_multishot_cost == 7
    )
    
    print(f"HP Changes Applied: {'✅ YES' if hp_changes_work else '❌ NO'}")
    print(f"Cost Changes Applied: {'✅ YES' if cost_changes_work else '❌ NO'}")
    
    if hp_changes_work:
        print("  ✅ HP configuration changes affect new units")
    else:
        print("  ❌ HP configuration changes NOT affecting new units")
        print(f"    Expected Warrior HP: 25, Got: {new_warrior_hp}")
        print(f"    Expected Mage HP: 12, Got: {new_mage_hp}")
        print(f"    Expected Hunter HP: 18, Got: {new_hunter_hp}")

    if cost_changes_work:
        print("  ✅ AP cost configuration changes affect new units")
    else:
        print("  ❌ AP cost configuration changes NOT affecting new units")
        print(f"    Expected Warrior Attack: 6, Got: {new_warrior_attack_cost}")
        print(f"    Expected Mage Fireball: 8, Got: {new_mage_fireball_cost}")
        print(f"    Expected Hunter Multishot: 7, Got: {new_hunter_multishot_cost}")
    
    # Test 5: In-Game Effectiveness
    print("\n📋 TEST 5: In-Game Effectiveness Test")
    print("-" * 30)
    
    # Test if the modified costs actually work in gameplay
    game = Game()
    game.board.add_unit(new_warrior, 4, 4)
    game.current_player = 1
    game.current_player_ap = 10
    game.units_acted_this_turn = set()
    
    # Test if warrior can use attack with new cost
    attack_ability_idx = None
    for i, ability in enumerate(new_warrior.abilities):
        if ability.name == "Basic Attack":
            attack_ability_idx = i
            break
    
    if attack_ability_idx is not None:
        can_use_attack = new_warrior.can_use_ability(attack_ability_idx, game)
        print(f"Warrior can use attack (cost {new_warrior_attack_cost}): {can_use_attack}")
        
        if can_use_attack:
            # Try to use the attack
            success = new_warrior.use_ability(attack_ability_idx, (4, 5), game)
            print(f"Attack execution success: {success}")
            print(f"AP after attack: {game.current_player_ap}")
            expected_ap = 10 - new_warrior_attack_cost
            print(f"Expected AP: {expected_ap}")

            if game.current_player_ap == expected_ap:
                print("  ✅ Modified AP costs working in gameplay")
            else:
                print("  ❌ Modified AP costs NOT working in gameplay")
        else:
            print("  ❌ Cannot use ability with modified cost")

    # Restore original JSON configuration
    if original_json_config:
        with open("game_balance_config.json", 'w') as f:
            json.dump(original_json_config, f, indent=2)
        print("  ✅ Original configuration restored")
    
    # Final Result
    print("\n" + "=" * 50)
    overall_success = hp_changes_work and cost_changes_work
    if overall_success:
        print("🎉 CONFIGURATION EFFECTIVENESS: WORKING!")
        print("✅ Configuration changes affect gameplay")
    else:
        print("❌ CONFIGURATION EFFECTIVENESS: BROKEN!")
        print("❌ Configuration changes do NOT affect gameplay")
        print("🔧 This needs to be fixed for balance sliders to work")
    
    return overall_success

if __name__ == "__main__":
    success = test_configuration_effectiveness()
    sys.exit(0 if success else 1)

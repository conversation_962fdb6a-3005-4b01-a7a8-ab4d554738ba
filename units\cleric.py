﻿import pygame
from units_core import Unit, SimpleAbility, MoveAbility, AttackAbility, SummonAbility
from game_config import GAME_CONFIG
import game_constants as const
import math

# Import unified systems
from core.configuration_manager import get_config_manager
from core.ability_system import DamageCalculator
from core.status_effects import StatusEffectType

class Cleric(Unit):
    """Cleric unit - Support unit with healing and buffing abilities."""
    def __init__(self, player_id):
        # Initialize with unified configuration system
        config_manager = get_config_manager()
        cleric_config = config_manager.get_unit_config("Cleric")

        super().__init__(
            player_id,
            health=cleric_config.get("health", 6),
            max_health=cleric_config.get("health", 6)
        )
        self.name = "Cleric"
        self.max_ap = cleric_config.get("max_ap", 7)
        self.current_ap = cleric_config.get("max_ap", 7)
        self.board = None
        self.image = self._create_placeholder_image((220, 220, 255) if player_id == 1 else (200, 200, 235))

        self.sanctuary_active_until = 0

        # Abilities with unified configuration
        self.abilities = [
            MoveAbility(self),
            AttackAbility(self),
            SimpleAbility("Heal", config_manager.get_ability_ap_cost("Cleric", "Heal"), "Restore 2 HP to an allied unit", cooldown=1, owner=self),
            SimpleAbility("Mass Heal", config_manager.get_ability_ap_cost("Cleric", "Mass Heal"), "Restore 1 HP to all allies in a 3x3 area", cooldown=3, owner=self),
            SimpleAbility("Cleanse", config_manager.get_ability_ap_cost("Cleric", "Cleanse"), "Remove all negative status effects from an ally", cooldown=2, owner=self),
            SimpleAbility("Sanctuary", config_manager.get_ability_ap_cost("Cleric", "Sanctuary"), "Target ally cannot be targeted by single-target enemy abilities for 1 turn", cooldown=4, owner=self),
            SimpleAbility("Divine Protection", config_manager.get_ability_ap_cost("Cleric", "Divine Protection"), "Target ally takes 50% less damage for 1 turn", cooldown=3, owner=self),
            SimpleAbility("Holy Smite", config_manager.get_ability_ap_cost("Cleric", "Holy Smite"), "Deal 1 damage to an enemy and Chills them (abilities cost +1 AP)", cooldown=2, owner=self),
            SummonAbility(self, config_manager.get_ability_ap_cost("Cleric", "Summon"))
        ]

        # Configuration is automatically applied by the base Unit class

    def _create_placeholder_image(self, color):
        surf = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA)
        pygame.draw.circle(surf, color, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2)
        pygame.draw.circle(surf, (255, 255, 255), (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2, 2)
        # Holy symbol (e.g., a cross or ankh-like shape)
        holy_color = (255, 255, 150)
        pygame.draw.rect(surf, holy_color, (const.CELL_SIZE//2 - 2, const.CELL_SIZE//2 - 10, 4, 20))
        pygame.draw.rect(surf, holy_color, (const.CELL_SIZE//2 - 8, const.CELL_SIZE//2 - 2, 16, 4))
        return surf

    def get_valid_moves(self, board):
        """Cleric moves exactly 1 tile in orthogonal directions only (N, S, E, W)."""
        self.board = board
        # Check if unit can move (new status system + legacy)
        if hasattr(self, 'status_manager') and not self.status_manager.can_move():
            return []
        if self.immobilized or self.stunned:
            return []

        valid_moves = []
        row, col = self.position

        # Orthogonal movement only (N, S, E, W) - 1 tile
        orthogonal_moves = [
            (-1, 0),  # North
            (1, 0),   # South
            (0, -1),  # West
            (0, 1)    # East
        ]

        for dr, dc in orthogonal_moves:
            new_row, new_col = row + dr, col + dc
            # Check bounds and if destination is empty
            if (0 <= new_row < const.BOARD_SIZE and 0 <= new_col < const.BOARD_SIZE and
                    (new_row, new_col) not in board.units):
                valid_moves.append((new_row, new_col))

        return valid_moves

    def get_valid_attacks(self, board):
        """Cleric has a standard melee attack."""
        self.board = board
        if self.has_status('Stunned'): return []
        valid_attacks = []
        row, col = self.position
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0: continue
                r, c = row + dr, col + dc
                if (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and (r,c) in board.units and
                        board.units[(r,c)].player_id != self.player_id and not board.units[(r,c)].sanctuary):
                    valid_attacks.append((r,c))
        return valid_attacks

    def get_ability_targets(self, ability_idx, board):
        self.board = board
        if ability_idx == 0: return self.get_valid_moves(board)
        if ability_idx == 1: return self.get_valid_attacks(board) # For Holy Smite or basic attack

        ability_name = self.abilities[ability_idx].name

        if ability_name == "Heal" or ability_name == "Cleanse" or ability_name == "Sanctuary" or ability_name == "Divine Protection":
            return self._get_friendly_targets_in_range(board, 3) # Example range of 3 for single target buffs/heals
        elif ability_name == "Mass Heal":
            return [self.position] # Targets self, area effect centered on Cleric
        elif ability_name == "Holy Smite":
            return self._get_enemy_targets_in_range(board, 3) # Example range of 3
        elif ability_name == "Summon":
            return self.get_valid_moves(board) # Can summon at any position Cleric could move to
        return []

    def _get_friendly_targets_in_range(self, board, max_range):
        targets = []
        for r_offset in range(-max_range, max_range + 1):
            for c_offset in range(-max_range, max_range + 1):
                # if abs(r_offset) + abs(c_offset) > max_range: continue # Manhattan distance
                if r_offset**2 + c_offset**2 > max_range**2 : continue # Euclidean distance (circular range)
                
                check_r, check_c = self.position[0] + r_offset, self.position[1] + c_offset
                if (0 <= check_r < const.BOARD_SIZE and 0 <= check_c < const.BOARD_SIZE and
                        (check_r, check_c) in board.units and
                        board.units[(check_r, check_c)].player_id == self.player_id):
                    targets.append((check_r, check_c))
        return targets

    def _get_enemy_targets_in_range(self, board, max_range):
        targets = []
        for r_offset in range(-max_range, max_range + 1):
            for c_offset in range(-max_range, max_range + 1):
                # if abs(r_offset) + abs(c_offset) > max_range: continue
                if r_offset**2 + c_offset**2 > max_range**2 : continue

                check_r, check_c = self.position[0] + r_offset, self.position[1] + c_offset
                if (0 <= check_r < const.BOARD_SIZE and 0 <= check_c < const.BOARD_SIZE and
                        (check_r, check_c) in board.units and
                        board.units[(check_r, check_c)].player_id != self.player_id and
                        not board.units[(check_r, check_c)].sanctuary):
                    targets.append((check_r, check_c))
        return targets

    def use_ability(self, ability_idx, target_pos, game=None):
        """
        Use an ability with unified ability execution system.
        Cleric-specific abilities are handled by registered methods.
        """
        # Ensure self.board is set if game object is provided
        if game:
            self.board = game.board

        # Register Cleric-specific ability handlers with the unified system
        self._register_ability_handlers()

        # Use unified ability executor (this handles all validation, AP spending, etc.)
        return super().use_ability(ability_idx, target_pos, game)

    def _register_ability_handlers(self):
        """Register Cleric-specific ability handlers with the unified ability system"""
        ability_executor = self.ability_executor

        # Register handlers for each Cleric ability with proper lambda wrappers
        ability_executor.register_ability_handler("Heal", lambda unit, target_pos, game: self._use_heal(target_pos, game))
        ability_executor.register_ability_handler("Mass Heal", lambda unit, target_pos, game: self._use_mass_heal(target_pos, game))
        ability_executor.register_ability_handler("Cleanse", lambda unit, target_pos, game: self._use_cleanse(target_pos, game))
        ability_executor.register_ability_handler("Sanctuary", lambda unit, target_pos, game: self._use_sanctuary(target_pos, game))
        ability_executor.register_ability_handler("Divine Protection", lambda unit, target_pos, game: self._use_divine_protection(target_pos, game))
        ability_executor.register_ability_handler("Holy Smite", lambda unit, target_pos, game: self._use_holy_smite(target_pos, game))

    def _use_heal(self, target_pos, game=None):
        """Heal: Restore HP to an allied unit using unified healing calculation"""
        target_unit = self.board.units.get(target_pos)
        if not (target_unit and target_unit.player_id == self.player_id):
            return False

        # Get configured healing amount
        config_manager = get_config_manager()
        heal_amount = config_manager.get_ability_damage("Cleric", "Heal")  # Using damage field for healing
        if heal_amount <= 0:
            heal_amount = 2  # Default healing

        old_health = target_unit.health
        target_unit.health = min(target_unit.max_health, target_unit.health + heal_amount)
        actual_healing = target_unit.health - old_health

        print(f"Heal restores {actual_healing} HP to {target_unit.name} ({old_health} -> {target_unit.health})")
        return True

    def _use_mass_heal(self, target_pos, game=None):
        """Mass Heal: Heal all allies in a + shape pattern using unified healing calculation"""
        # Get configured healing amount
        config_manager = get_config_manager()
        heal_amount = config_manager.get_ability_damage("Cleric", "Mass Heal")
        if heal_amount <= 0:
            heal_amount = 1  # Default mass healing

        # + shape pattern around the cleric
        heal_positions = [
            self.position,  # Self
            (self.position[0] - 1, self.position[1]),  # North
            (self.position[0] + 1, self.position[1]),  # South
            (self.position[0], self.position[1] - 1),  # West
            (self.position[0], self.position[1] + 1)   # East
        ]

        healed_count = 0
        for pos in heal_positions:
            if (0 <= pos[0] < const.BOARD_SIZE and 0 <= pos[1] < const.BOARD_SIZE):
                unit = self.board.units.get(pos)
                if unit and unit.player_id == self.player_id:
                    old_health = unit.health
                    unit.health = min(unit.max_health, unit.health + heal_amount)
                    actual_healing = unit.health - old_health
                    if actual_healing > 0:
                        print(f"Mass Heal restores {actual_healing} HP to {unit.name}")
                        healed_count += 1

        print(f"Mass Heal affected {healed_count} allied units")
        return True

    def _use_cleanse(self, target_pos, game=None):
        """Cleanse: Remove all negative status effects from an ally using unified status system"""
        target_unit = self.board.units.get(target_pos)
        if not (target_unit and target_unit.player_id == self.player_id):
            return False

        # Remove all negative status effects manually
        removed_effects = []
        negative_effects = [StatusEffectType.STUNNED, StatusEffectType.CRIPPLED, StatusEffectType.CHILLED]

        for effect_type in negative_effects:
            if hasattr(target_unit, 'status_effects') and effect_type in target_unit.status_effects:
                target_unit.status_effect_manager.remove_status_effect(target_unit, effect_type)
                removed_effects.append(effect_type.value)

        if removed_effects:
            print(f"Cleanse removes {', '.join(removed_effects)} from {target_unit.name}")
        else:
            print(f"Cleanse used on {target_unit.name} (no negative effects to remove)")
        return True

    def _use_sanctuary(self, target_pos, game=None):
        """Sanctuary: Target ally cannot be targeted by single-target enemy abilities"""
        target_unit = self.board.units.get(target_pos)
        if not (target_unit and target_unit.player_id == self.player_id):
            return False

        target_unit.sanctuary = True
        if game and hasattr(game, 'turn_count'):
            self.sanctuary_active_until = game.turn_count + 1
        print(f"{target_unit.name} is protected by Sanctuary")
        return True

    def _use_divine_protection(self, target_pos, game=None):
        """Divine Protection: Target ally takes 50% less damage for 1 turn"""
        target_unit = self.board.units.get(target_pos)
        if not (target_unit and target_unit.player_id == self.player_id):
            return False

        # Apply Divine Protection status effect using unified status system
        target_unit.apply_status('Divine Protection', 1)
        print(f"{target_unit.name} is protected by Divine Protection")
        return True

    def _use_holy_smite(self, target_pos, game=None):
        """Holy Smite: Deal damage to an enemy and apply Chilled status using unified systems"""
        target_unit = self.board.units.get(target_pos)
        if not (target_unit and target_unit.player_id != self.player_id):
            return False

        # Get configured damage using unified damage calculation
        smite_damage = DamageCalculator.calculate_ability_damage(self, "Holy Smite", target_pos)

        print(f"Holy Smite hits {target_unit.name} for {smite_damage} damage")
        target_unit.take_damage(smite_damage, self, game=game)

        # Apply Chilled status effect using unified status system
        target_unit.apply_status('Chilled', 1)
        print(f"{target_unit.name} is chilled by Holy Smite")
        return True

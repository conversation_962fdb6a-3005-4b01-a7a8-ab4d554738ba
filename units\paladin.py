import pygame
from units_core import Unit, SimpleAbility, MoveAbility, AttackAbility
from game_config import GAME_CONFIG
import game_constants as const
import math

class Paladin(Unit):
    """
    Paladin unit class - Holy warrior with healing, protection, and smite abilities.
    
    Paladins are versatile holy warriors who combine combat prowess with divine magic.
    They excel at protecting allies, healing, and dealing extra damage to evil enemies.
    
    Movement:
        - Pattern: Orthogonal only (N, S, E, W) like Warrior
        - Range: 2 tiles in chosen direction
        - Cannot jump over entities
        
    Abilities (8 total):
        0. Move - Orthogonal movement (2 tiles)
        1. Basic Attack - Melee attack with holy damage
        2. Lay on Hands - Heal ally for significant amount
        3. Divine Smite - Extra damage attack with stun chance
        4. Blessing - Grant ally damage resistance
        5. Consecrate - Create holy ground that heals allies
        6. Turn Undead - Fear effect on cursed enemies
        7. Divine Shield - Become immune to damage for 1 turn
        
    Default Passive:
        - Holy Aura: Adjacent allies take -1 damage
        
    Tactical Role:
        - Tank/Support hybrid
        - Ally protection specialist
        - Anti-curse/debuff fighter
    """
    def __init__(self, player_id):
        super().__init__(player_id, health=GAME_CONFIG.get("paladin_config", {}).get("health", 8), max_health=GAME_CONFIG.get("paladin_config", {}).get("health", 8))
        self.name = "Paladin"
        self.max_ap = GAME_CONFIG.get("paladin_config", {}).get("max_ap", 6)
        self.current_ap = GAME_CONFIG.get("paladin_config", {}).get("max_ap", 6)
        self.board = None
        
        # Paladin-specific state
        self.divine_shield_active = False
        self.divine_shield_until = 0
        self.consecrated_ground = {}  # Track consecrated positions
        self.blessing_targets = set()  # Track blessed allies
        
        # Load image (golden/white theme)
        self.image = self._create_placeholder_image((255, 215, 0) if player_id == 1 else (255, 255, 255))
        
        # Abilities
        self.abilities = [
            MoveAbility(self),
            AttackAbility(self),
            SimpleAbility("Lay on Hands", GAME_CONFIG.get("paladin_config", {}).get("lay_on_hands_ap_cost", 3), "Heal ally for 4 HP", cooldown=3, owner=self),
            SimpleAbility("Divine Smite", GAME_CONFIG.get("paladin_config", {}).get("divine_smite_ap_cost", 3), "Holy attack with stun chance", cooldown=2, owner=self),
            SimpleAbility("Blessing", GAME_CONFIG.get("paladin_config", {}).get("blessing_ap_cost", 2), "Ally takes -1 damage for 3 turns", cooldown=3, owner=self),
            SimpleAbility("Consecrate", GAME_CONFIG.get("paladin_config", {}).get("consecrate_ap_cost", 4), "Create healing ground for 3 turns", cooldown=4, owner=self),
            SimpleAbility("Turn Undead", GAME_CONFIG.get("paladin_config", {}).get("turn_undead_ap_cost", 3), "Fear cursed enemies", cooldown=3, owner=self),
            SimpleAbility("Divine Shield", GAME_CONFIG.get("paladin_config", {}).get("divine_shield_ap_cost", 4), "Immune to damage for 1 turn", cooldown=5, owner=self)
        ]
        
        # Add healing amounts to healing abilities
        self.abilities[2].heal_amount = 4  # Lay on Hands
        
        # Apply configuration from balance system
        try:
            from config_loader import apply_config_to_unit
            apply_config_to_unit(self)
        except ImportError:
            pass  # Config loader not available
    
    def _create_placeholder_image(self, color):
        """Create a placeholder image with holy symbols"""
        surf = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA)
        
        # Main circle (holy theme)
        pygame.draw.circle(surf, color, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2)
        pygame.draw.circle(surf, (100, 100, 100), (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2, 2)
        
        # Holy symbol (cross)
        center_x, center_y = const.CELL_SIZE//2, const.CELL_SIZE//2
        symbol_color = (255, 255, 255) if color == (255, 215, 0) else (255, 215, 0)
        
        # Draw cross
        pygame.draw.rect(surf, symbol_color, (center_x - 2, center_y - 8, 4, 16))
        pygame.draw.rect(surf, symbol_color, (center_x - 6, center_y - 2, 12, 4))
        
        return surf
    
    def get_valid_moves(self, board):
        """Paladin moves 2 tiles in orthogonal directions only (N, S, E, W)"""
        self.board = board
        # Check if unit can move (status effects)
        if hasattr(self, 'status_manager') and not self.status_manager.can_move():
            return []
        if self.immobilized or self.stunned:
            return []
        
        valid_moves = []
        row, col = self.position
        
        # Orthogonal directions: North, South, East, West
        orthogonal_directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]
        
        for dr, dc in orthogonal_directions:
            # Move up to 2 tiles in each direction
            for distance in range(1, 3):  # 1 and 2 tiles
                new_r, new_c = row + dr * distance, col + dc * distance
                
                # Check if position is on board
                if 0 <= new_r < const.BOARD_SIZE and 0 <= new_c < const.BOARD_SIZE:
                    # Check if position is empty
                    if (new_r, new_c) not in board.units:
                        valid_moves.append((new_r, new_c))
                    else:
                        # Can't move through units, stop checking further in this direction
                        break
                else:
                    # Off board, stop checking further in this direction
                    break
        
        return valid_moves
    
    def get_valid_attacks(self, board):
        """Paladin basic attack: melee (adjacent orthogonal)"""
        self.board = board
        if self.stunned:
            return []
        
        valid_attacks = []
        row, col = self.position
        
        # Adjacent orthogonal positions
        orthogonal_directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]
        
        for dr, dc in orthogonal_directions:
            r, c = row + dr, col + dc
            
            # Check if position is on board and has a unit
            if (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and
                (r, c) in board.units and not board.units[(r, c)].sanctuary):
                valid_attacks.append((r, c))
        
        return valid_attacks
    
    def get_ability_targets(self, ability_idx, board):
        """Get valid targets for Paladin abilities"""
        self.board = board
        if ability_idx == 0:
            return self.get_valid_moves(board)
        if ability_idx == 1:
            return self.get_valid_attacks(board)
        
        ability_name = self.abilities[ability_idx].name
        
        if ability_name in ["Lay on Hands", "Blessing"]:
            # Ally-targeting abilities (adjacent)
            return self._get_adjacent_allies(board)
        elif ability_name in ["Divine Smite", "Turn Undead"]:
            # Enemy-targeting abilities (adjacent)
            return self.get_valid_attacks(board)
        elif ability_name == "Consecrate":
            # Ground-targeting ability (adjacent empty spaces)
            return self._get_adjacent_empty_spaces(board)
        elif ability_name == "Divine Shield":
            # Self-target ability
            return [self.position]
        
        return []
    
    def _get_adjacent_allies(self, board):
        """Get adjacent allied units"""
        allies = []
        row, col = self.position
        
        # Check all adjacent positions
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:
                    continue
                
                r, c = row + dr, col + dc
                if (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and
                    (r, c) in board.units):
                    unit = board.units[(r, c)]
                    if unit.player_id == self.player_id and not unit.sanctuary:
                        allies.append((r, c))
        
        return allies
    
    def _get_adjacent_empty_spaces(self, board):
        """Get adjacent empty spaces for ground-targeting abilities"""
        empty_spaces = []
        row, col = self.position
        
        # Check adjacent orthogonal positions
        orthogonal_directions = [(-1, 0), (1, 0), (0, -1), (0, 1)]
        
        for dr, dc in orthogonal_directions:
            r, c = row + dr, col + dc
            
            if (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and
                (r, c) not in board.units):
                empty_spaces.append((r, c))
        
        return empty_spaces
    
    def use_ability(self, ability_idx, target_pos, game=None):
        """Use Paladin ability with global AP system"""
        if game:
            self.board = game.board
        elif not self.board:
            print(f"ERROR in {self.name}.use_ability: game object not passed and self.board not set.")
            if ability_idx > 1:
                print(f"Cannot use special ability {self.abilities[ability_idx].name} without board context.")
                return False
        
        # Standard move or attack - call super() to use the updated base class logic
        if ability_idx == 0:  # Move
            return super().use_ability(ability_idx, target_pos, game)
        
        if ability_idx == 1:  # Basic Attack
            return super().use_ability(ability_idx, target_pos, game)
        
        # For other Paladin-specific abilities - use global AP system
        return super().use_ability(ability_idx, target_pos, game)
    
    def _execute_paladin_ability(self, ability, target_pos, game):
        """Execute Paladin-specific abilities"""
        ability_name = ability.name
        
        if ability_name == "Lay on Hands":
            return self._use_lay_on_hands(ability, target_pos)
        elif ability_name == "Divine Smite":
            return self._use_divine_smite(ability, target_pos)
        elif ability_name == "Blessing":
            return self._use_blessing(ability, target_pos)
        elif ability_name == "Consecrate":
            return self._use_consecrate(ability, target_pos, game)
        elif ability_name == "Turn Undead":
            return self._use_turn_undead(ability, target_pos)
        elif ability_name == "Divine Shield":
            return self._use_divine_shield(ability, game)
        else:
            print(f"Unknown Paladin ability: {ability_name}")
            return False
    
    def _use_lay_on_hands(self, ability, target_pos):
        """Powerful healing ability"""
        target_unit = self.board.units.get(target_pos)
        if target_unit and target_unit.player_id == self.player_id:
            heal_amount = getattr(ability, 'heal_amount', 4)
            old_health = target_unit.health
            target_unit.health = min(target_unit.max_health, target_unit.health + heal_amount)
            actual_heal = target_unit.health - old_health
            
            print(f"{self.name} uses Lay on Hands on {target_unit.name}, healing {actual_heal} HP")
            return True
        return False
    
    def _use_divine_smite(self, ability, target_pos):
        """Holy attack with stun chance"""
        target_unit = self.board.units.get(target_pos)
        if target_unit:
            damage = ability.damage if ability.damage > 0 else 3
            
            # Extra damage against cursed enemies
            extra_damage = 0
            if hasattr(target_unit, 'cursed') and target_unit.cursed:
                extra_damage = 2
                print(f"Divine Smite deals extra damage to cursed enemy!")
            
            total_damage = damage + extra_damage
            target_unit.take_damage(total_damage, self)
            
            # 50% chance to stun
            import random
            if random.random() < 0.5:
                if hasattr(target_unit, 'status_manager'):
                    target_unit.status_manager.apply_effect("stunned", 1, self.player_id)
                else:
                    target_unit.stunned = True
                    target_unit.stunned_turns = 1
                print(f"{target_unit.name} is stunned by Divine Smite!")
            
            print(f"{self.name} smites {target_unit.name} for {total_damage} holy damage")
            return True
        return False
    
    def _use_blessing(self, ability, target_pos):
        """Grant damage resistance to ally"""
        target_unit = self.board.units.get(target_pos)
        if target_unit and target_unit.player_id == self.player_id:
            # Apply blessing effect (take -1 damage for 3 turns)
            if hasattr(target_unit, 'status_manager'):
                target_unit.status_manager.apply_effect("blessed", 3, self.player_id)
            else:
                target_unit.blessed = True
                target_unit.blessed_turns = 3
            
            self.blessing_targets.add(target_unit)
            print(f"{self.name} blesses {target_unit.name} - they take -1 damage for 3 turns")
            return True
        return False
    
    def _use_consecrate(self, ability, target_pos, game):
        """Create holy ground that heals allies"""
        # Create consecrated ground at target position
        self.consecrated_ground[target_pos] = {
            'turns_remaining': 3,
            'heal_amount': 1
        }
        
        print(f"{self.name} consecrates the ground at {target_pos} - allies will heal 1 HP per turn for 3 turns")
        return True
    
    def _use_turn_undead(self, ability, target_pos):
        """Fear effect on cursed enemies"""
        target_unit = self.board.units.get(target_pos)
        if target_unit:
            damage = ability.damage if ability.damage > 0 else 2
            
            # Check if target is cursed
            is_cursed = False
            if hasattr(target_unit, 'cursed') and target_unit.cursed:
                is_cursed = True
            elif hasattr(target_unit, 'status_manager'):
                is_cursed = target_unit.status_manager.has_effect("cursed")
            
            if is_cursed:
                # Extra damage and fear effect
                target_unit.take_damage(damage + 2, self)
                
                # Apply fear (force movement away)
                if hasattr(target_unit, 'status_manager'):
                    target_unit.status_manager.apply_effect("feared", 1, self.player_id)
                else:
                    target_unit.feared = True
                    target_unit.feared_turns = 1
                
                print(f"{self.name} turns {target_unit.name} (cursed) for {damage + 2} damage and applies fear!")
            else:
                # Normal damage
                target_unit.take_damage(damage, self)
                print(f"{self.name} attacks {target_unit.name} for {damage} damage")
            
            return True
        return False
    
    def _use_divine_shield(self, ability, game):
        """Become immune to damage for 1 turn"""
        self.divine_shield_active = True
        if game:
            self.divine_shield_until = game.turn_count + 1
        
        print(f"{self.name} activates Divine Shield - immune to damage for 1 turn")
        return True
    
    def take_damage(self, amount, attacker=None, game=None):
        """Override to handle Divine Shield"""
        if self.divine_shield_active:
            if game and game.turn_count >= self.divine_shield_until:
                self.divine_shield_active = False
            else:
                print(f"{self.name}'s Divine Shield blocks all damage!")
                return self.health
        
        return super().take_damage(amount, attacker, game)
    
    def reset_turn(self, game=None):
        """Reset turn-specific effects"""
        super().reset_turn(game=game)
        
        # Update consecrated ground
        expired_positions = []
        for pos, data in self.consecrated_ground.items():
            data['turns_remaining'] -= 1
            if data['turns_remaining'] <= 0:
                expired_positions.append(pos)
        
        for pos in expired_positions:
            del self.consecrated_ground[pos]
            print(f"Consecrated ground at {pos} expires")

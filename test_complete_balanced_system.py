#!/usr/bin/env python3
"""
Complete test of the balanced AP system with sliders
Tests the full integration of global AP + balance sliders
"""

import pygame
from game_state import Game
from units.warrior import Warrior
from units.mage import Mage
from units.cleric import <PERSON><PERSON><PERSON>

def test_complete_balanced_system():
    """Test the complete balanced AP system with sliders"""
    pygame.init()
    
    print("🎮 TESTING COMPLETE BALANCED SYSTEM 🎮")
    print("=" * 44)
    
    # Test 1: System Initialization
    print("📋 TEST 1: System Initialization")
    print("-" * 32)
    
    game = Game()
    
    print(f"Global AP System:")
    print(f"  Base AP: {game.base_ap}")
    print(f"  AP increment: {game.ap_increment}")
    print(f"  Max AP: {game.max_ap}")
    print(f"  Player 2 bonus: {game.player2_early_bonus}")
    
    print(f"\nBalance Slider System:")
    print(f"  Total sliders: {len(game.balance_sliders.sliders)}")
    print(f"  Categories: {game.balance_sliders.categories}")
    print(f"  Show UI: {game.show_balance_ui}")
    
    assert hasattr(game, 'balance_sliders'), "Balance slider system should be initialized"
    assert hasattr(game, 'current_player_ap'), "Global AP should be initialized"
    
    print("✅ System initialization working")
    
    # Test 2: AP Progression with First Player Advantage Mitigation
    print(f"\n📋 TEST 2: AP Progression Analysis")
    print("-" * 35)
    
    print("Turn | P1 AP | P2 AP | P2 Advantage | Phase")
    print("-" * 50)
    
    total_p1_ap = 0
    total_p2_ap = 0
    
    for turn in range(1, 11):
        p1_ap = game.calculate_turn_ap(turn, 1)
        p2_ap = game.calculate_turn_ap(turn, 2)
        advantage = p2_ap - p1_ap
        
        total_p1_ap += p1_ap
        total_p2_ap += p2_ap
        
        if turn <= 3:
            phase = "Early"
        elif turn <= 6:
            phase = "Mid"
        else:
            phase = "Late"
        
        print(f"{turn:4d} | {p1_ap:5d} | {p2_ap:5d} | {advantage:+11d} | {phase}")
    
    print(f"\nCumulative AP over 10 turns:")
    print(f"  Player 1 total: {total_p1_ap} AP")
    print(f"  Player 2 total: {total_p2_ap} AP")
    print(f"  Player 2 advantage: +{total_p2_ap - total_p1_ap} AP ({((total_p2_ap - total_p1_ap) / total_p1_ap * 100):.1f}%)")
    
    # Verify first player advantage mitigation
    early_advantage = sum(game.calculate_turn_ap(t, 2) - game.calculate_turn_ap(t, 1) for t in range(1, 4))
    late_advantage = sum(game.calculate_turn_ap(t, 2) - game.calculate_turn_ap(t, 1) for t in range(7, 11))
    
    assert early_advantage > 0, "Player 2 should have early game advantage"
    assert late_advantage == 0, "No advantage in late game"
    
    print("✅ First player advantage mitigation working")
    
    # Test 3: Balance Slider Integration
    print(f"\n📋 TEST 3: Balance Slider Integration")
    print("-" * 37)
    
    # Test slider value changes
    original_warrior_hp = game.balance_sliders.sliders["warrior_hp"].current_value
    original_max_ap = game.balance_sliders.sliders["max_ap"].current_value
    
    print(f"Original values:")
    print(f"  Warrior HP: {original_warrior_hp}")
    print(f"  Max AP: {original_max_ap}")
    
    # Modify slider values
    game.balance_sliders.update_slider("warrior_hp", 15)
    game.balance_sliders.update_slider("max_ap", 12)
    
    new_warrior_hp = game.balance_sliders.sliders["warrior_hp"].current_value
    new_max_ap = game.balance_sliders.sliders["max_ap"].current_value
    
    print(f"\nModified values:")
    print(f"  Warrior HP: {new_warrior_hp}")
    print(f"  Max AP: {new_max_ap}")
    
    # Apply changes
    changes = game.apply_balance_changes()
    print(f"\nApplied changes: {changes}")
    
    assert game.max_ap == 12, "Max AP should be updated"
    print("✅ Balance slider integration working")
    
    # Test 4: Unit Integration with Sliders
    print(f"\n📋 TEST 4: Unit Integration with Sliders")
    print("-" * 38)
    
    # Create units
    warrior = Warrior(1)
    mage = Mage(1)
    cleric = Cleric(2)
    
    # Position units
    warrior.position = (4, 4)
    mage.position = (4, 5)
    cleric.position = (4, 6)
    
    # Set up board
    game.board.units = {
        (4, 4): warrior,
        (4, 5): mage,
        (4, 6): cleric
    }
    
    print(f"Units before balance application:")
    print(f"  Warrior: {warrior.health}/{warrior.max_health} HP")
    print(f"  Mage: {mage.health}/{mage.max_health} HP")
    print(f"  Cleric: {cleric.health}/{cleric.max_health} HP")
    
    # Apply balance changes to units
    changes = game.apply_balance_changes()
    
    print(f"\nUnits after balance application:")
    print(f"  Warrior: {warrior.health}/{warrior.max_health} HP")
    print(f"  Mage: {mage.health}/{mage.max_health} HP")
    print(f"  Cleric: {cleric.health}/{cleric.max_health} HP")
    
    assert warrior.max_health == 15, "Warrior HP should be updated by slider"
    print("✅ Unit integration with sliders working")
    
    # Test 5: Complete Game Flow
    print(f"\n📋 TEST 5: Complete Game Flow")
    print("-" * 29)
    
    # Start Player 1's turn
    game.turn_count = 1
    game.start_player_turn(1)
    
    print(f"Turn 1, Player 1:")
    print(f"  AP available: {game.current_player_ap}")
    print(f"  Expected: {game.calculate_turn_ap(1, 1)}")
    
    # Set board references
    warrior.board = game.board
    mage.board = game.board
    
    # Test warrior action
    print(f"\n🚶 Warrior attempts move:")
    print(f"  Before: AP={game.current_player_ap}, Acted={warrior.has_acted_this_turn}")
    
    move_success = warrior.use_ability(0, (4, 3), game)
    
    print(f"  After: AP={game.current_player_ap}, Acted={warrior.has_acted_this_turn}")
    print(f"  Success: {move_success}")
    
    # Test mage action
    print(f"\n🔮 Mage attempts move:")
    print(f"  Before: AP={game.current_player_ap}, Acted={mage.has_acted_this_turn}")
    
    mage_success = mage.use_ability(0, (4, 7), game)
    
    print(f"  After: AP={game.current_player_ap}, Acted={mage.has_acted_this_turn}")
    print(f"  Success: {mage_success}")
    
    # Switch to Player 2
    game.start_player_turn(2)
    
    print(f"\nTurn 1, Player 2:")
    print(f"  AP available: {game.current_player_ap}")
    print(f"  Expected: {game.calculate_turn_ap(1, 2)} (with bonus)")
    
    assert game.current_player_ap == 2, "Player 2 should have 2 AP on turn 1"
    
    print("✅ Complete game flow working")
    
    # Test 6: Preset System
    print(f"\n📋 TEST 6: Preset System")
    print("-" * 23)
    
    # Save current state as custom preset
    success = game.balance_sliders.save_balance_preset("test_preset")
    print(f"Save custom preset: {success}")
    
    # Modify some values
    game.balance_sliders.update_slider("warrior_hp", 8)
    game.balance_sliders.update_slider("max_ap", 8)
    
    print(f"Modified values:")
    print(f"  Warrior HP: {game.balance_sliders.sliders['warrior_hp'].current_value}")
    print(f"  Max AP: {game.balance_sliders.sliders['max_ap'].current_value}")
    
    # Load balanced preset
    game.balance_sliders.load_balance_preset("balanced")
    
    print(f"After loading 'balanced' preset:")
    print(f"  Warrior HP: {game.balance_sliders.sliders['warrior_hp'].current_value}")
    print(f"  Max AP: {game.balance_sliders.sliders['max_ap'].current_value}")
    
    print("✅ Preset system working")
    
    print(f"\n" + "=" * 44)
    print("🎯 COMPLETE BALANCED SYSTEM SUMMARY")
    print("-" * 35)
    print("✅ Global AP System: 1→10 with P2 early bonus")
    print("✅ Balance Sliders: Real-time value adjustment")
    print("✅ Unit Integration: HP and stats updated")
    print("✅ Game Flow: Complete turn management")
    print("✅ Preset System: Save/load configurations")
    print("✅ First Player Advantage: Mitigated effectively")
    
    print("\n🚀 SYSTEM READY FOR GAMEPLAY TESTING!")
    print("   Use F1-F5 keys for balance controls:")
    print("   F1: Toggle balance UI")
    print("   F2: Conservative preset")
    print("   F3: Balanced preset")
    print("   F4: Aggressive preset")
    print("   F5: Apply current values")
    
    print(f"\n📊 BALANCE STATISTICS:")
    print(f"   Early game P2 advantage: +{early_advantage} AP")
    print(f"   Total 10-turn advantage: +{total_p2_ap - total_p1_ap} AP")
    print(f"   Advantage percentage: {((total_p2_ap - total_p1_ap) / total_p1_ap * 100):.1f}%")

if __name__ == "__main__":
    test_complete_balanced_system()

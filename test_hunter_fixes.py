#!/usr/bin/env python3
"""
Test the fixes for Hunter ability issues:
1. Spread Shot now uses proper spread pattern (orthogonal + 2 diagonals)
2. Ricochet Shot bounces exactly once (as intended)
3. Crippling Shot duration is properly decremented
"""

import pygame
pygame.init()

from game_state import Game
from units.hunter import Hunter
from units.warrior import Warrior
import game_logic

def test_spread_shot_new_pattern():
    """Test the new Spread Shot pattern (orthogonal + 2 diagonals)"""
    print("🎯 TEST 1: SPREAD SHOT NEW PATTERN")
    print("=" * 50)
    
    game = Game()
    hunter = Hunter(1)
    
    # Create 3 targets in spread pattern
    target_center = Warrior(2)  # North of hunter
    target_left = Warrior(2)    # NW of hunter  
    target_right = Warrior(2)   # NE of hunter
    
    # Set up positions
    hunter.position = (4, 4)
    target_center.position = (3, 4)  # North
    target_left.position = (3, 3)    # NW
    target_right.position = (3, 5)   # NE
    
    # Add to board
    game.board.add_unit(hunter, 4, 4)
    game.board.add_unit(target_center, 3, 4)
    game.board.add_unit(target_left, 3, 3)
    game.board.add_unit(target_right, 3, 5)
    
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Hunter at: {hunter.position}")
    print(f"Target Center (N) at: {target_center.position} - HP: {target_center.health}")
    print(f"Target Left (NW) at: {target_left.position} - HP: {target_left.health}")
    print(f"Target Right (NE) at: {target_right.position} - HP: {target_right.health}")
    
    # Use Spread Shot targeting north
    success = hunter.use_ability(5, target_center.position, game)
    
    print(f"\nSpread Shot success: {success}")
    print(f"Target Center HP after: {target_center.health}")
    print(f"Target Left HP after: {target_left.health}")
    print(f"Target Right HP after: {target_right.health}")
    
    # Count targets hit
    targets_hit = 0
    if target_center.health < 7: targets_hit += 1
    if target_left.health < 7: targets_hit += 1
    if target_right.health < 7: targets_hit += 1
    
    print(f"Total targets hit: {targets_hit}")
    
    if targets_hit == 3:
        print("✅ Spread Shot works correctly - hits all 3 targets in spread pattern")
        return True
    else:
        print("❌ Spread Shot issue - should hit 3 targets in spread pattern")
        return False

def test_ricochet_shot_single_bounce():
    """Test that Ricochet Shot bounces exactly once"""
    print("\n🏹 TEST 2: RICOCHET SHOT SINGLE BOUNCE")
    print("=" * 50)
    
    game = Game()
    hunter = Hunter(1)
    target1 = Warrior(2)
    target2 = Warrior(2)
    
    # Set up positions for ricochet
    hunter.position = (4, 4)
    target1.position = (2, 6)  # Primary target (NE)
    target2.position = (1, 7)  # Ricochet target (further NE)
    
    game.board.add_unit(hunter, 4, 4)
    game.board.add_unit(target1, 2, 6)
    game.board.add_unit(target2, 1, 7)
    
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Hunter at: {hunter.position}")
    print(f"Primary target at: {target1.position} - HP: {target1.health}")
    print(f"Ricochet target at: {target2.position} - HP: {target2.health}")
    
    # Use Ricochet Shot
    success = hunter.use_ability(2, target1.position, game)
    
    print(f"\nRicochet Shot success: {success}")
    print(f"Primary target HP after: {target1.health}")
    print(f"Ricochet target HP after: {target2.health}")
    
    # Check if both targets were hit
    primary_hit = target1.health < 7
    ricochet_hit = target2.health < 7
    
    if primary_hit and ricochet_hit:
        print("✅ Ricochet Shot works correctly - hits primary and bounces once")
        return True
    elif primary_hit and not ricochet_hit:
        print("❌ Ricochet Shot issue - hits primary but doesn't bounce")
        return False
    else:
        print("❌ Ricochet Shot issue - doesn't hit primary target")
        return False

def test_crippling_shot_duration():
    """Test that Crippling Shot duration decrements properly"""
    print("\n🦵 TEST 3: CRIPPLING SHOT DURATION")
    print("=" * 50)
    
    game = Game()
    hunter = Hunter(1)
    target = Warrior(2)
    
    # Set up positions
    hunter.position = (4, 4)
    target.position = (3, 5)  # Diagonal from hunter
    
    game.board.add_unit(hunter, 4, 4)
    game.board.add_unit(target, 3, 5)
    
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Hunter at: {hunter.position}")
    print(f"Target at: {target.position}")
    
    # Use Crippling Shot
    success = hunter.use_ability(6, target.position, game)
    
    print(f"Crippling Shot success: {success}")
    print(f"Target crippled: {target.has_status('Crippled')}")
    
    # Check initial duration
    from core.status_effects import StatusEffectType
    if StatusEffectType.CRIPPLED in target.status_effects:
        initial_duration = target.status_effects[StatusEffectType.CRIPPLED].duration
        print(f"Initial Crippled duration: {initial_duration}")
        
        # Simulate end of turn (this should decrement duration)
        target.reset_turn(game)
        
        # Check duration after turn end
        if StatusEffectType.CRIPPLED in target.status_effects:
            after_duration = target.status_effects[StatusEffectType.CRIPPLED].duration
            print(f"Duration after turn end: {after_duration}")
            
            if after_duration == initial_duration - 1:
                print("✅ Crippling Shot duration decrements correctly")
                
                # Test if effect expires after duration reaches 0
                if after_duration == 0:
                    print("✅ Effect should expire after this turn")
                    return True
                else:
                    # Simulate another turn end to see if it expires
                    target.reset_turn(game)
                    still_crippled = target.has_status('Crippled')
                    print(f"Still crippled after second turn: {still_crippled}")
                    
                    if not still_crippled:
                        print("✅ Crippling effect expires correctly")
                        return True
                    else:
                        print("❌ Crippling effect doesn't expire")
                        return False
            else:
                print(f"❌ Duration doesn't decrement (was {initial_duration}, now {after_duration})")
                return False
        else:
            print("✅ Crippled effect expired immediately (duration was 0)")
            return True
    else:
        print("❌ Crippled status not applied")
        return False

def main():
    print("🔧 HUNTER ABILITY FIXES VERIFICATION")
    print("=" * 60)
    
    results = []
    
    # Test each fix
    results.append(test_spread_shot_new_pattern())
    results.append(test_ricochet_shot_single_bounce())
    results.append(test_crippling_shot_duration())
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY OF FIXES:")
    print("-" * 30)
    
    fixes = [
        "Spread Shot spread pattern",
        "Ricochet Shot single bounce",
        "Crippling Shot duration"
    ]
    
    for i, (fix, result) in enumerate(zip(fixes, results)):
        status = "✅ FIXED" if result else "❌ STILL BROKEN"
        print(f"{i+1}. {fix}: {status}")
    
    working_count = sum(results)
    print(f"\n📈 Overall: {working_count}/{len(results)} fixes working")
    
    if working_count == len(results):
        print("🎉 All Hunter ability issues have been fixed!")
    else:
        print("🔧 Some Hunter abilities still need work")
    
    return working_count == len(results)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Comprehensive test suite for the refactored game systems.
Tests all major components to ensure functionality is preserved.
"""

import pygame
import sys
import os

# Initialize pygame to avoid import issues
pygame.init()

def test_unified_configuration_system():
    """Test that the unified configuration system is working"""
    print("🔧 Testing Unified Configuration System...")
    
    try:
        from core.configuration_manager import get_config_manager
        config_manager = get_config_manager()
        
        # Test unit configuration
        warrior_config = config_manager.get_unit_config("Warrior")
        assert "health" in warrior_config, "Warrior config missing health"
        assert "max_ap" in warrior_config, "Warrior config missing max_ap"
        
        # Test ability configuration
        warrior_ap_cost = config_manager.get_ability_ap_cost("Warrior", "Cleave Attack")
        assert warrior_ap_cost > 0, "Warrior Cleave Attack AP cost should be > 0"
        
        print("✅ Unified Configuration System: WORKING")
        return True
    except Exception as e:
        print(f"❌ Unified Configuration System: FAILED - {e}")
        return False

def test_damage_calculator():
    """Test that the unified damage calculator is working"""
    print("🗡️ Testing Damage Calculator...")
    
    try:
        from core.ability_system import DamageCalculator
        from units.warrior import Warrior
        
        warrior = Warrior(1)
        
        # Test basic attack damage calculation
        basic_damage = DamageCalculator.calculate_basic_attack_damage(warrior, (0, 0))
        assert basic_damage > 0, "Basic attack damage should be > 0"
        
        # Test ability damage calculation
        ability_damage = DamageCalculator.calculate_ability_damage(warrior, "Cleave Attack", (0, 0))
        assert ability_damage > 0, "Ability damage should be > 0"
        
        print("✅ Damage Calculator: WORKING")
        return True
    except Exception as e:
        print(f"❌ Damage Calculator: FAILED - {e}")
        return False

def test_status_effects_system():
    """Test that the unified status effects system is working"""
    print("🌟 Testing Status Effects System...")

    try:
        from core.status_effects import StatusEffectType
        from units.warrior import Warrior

        warrior = Warrior(1)

        # Test applying status effect
        warrior.apply_status("Stunned", 2)

        # Test checking status effect
        is_stunned = warrior.has_status("Stunned")
        assert is_stunned, "Warrior should be stunned"

        # Test status effect duration (check that the effect exists in the status_effects dict)
        assert StatusEffectType.STUNNED in warrior.status_effects, "Stunned effect should be in status_effects"
        stunned_effect = warrior.status_effects[StatusEffectType.STUNNED]
        assert stunned_effect.duration == 2, "Stunned duration should be 2"

        # Test movement restriction
        can_move = warrior.status_effect_manager.can_unit_move(warrior)
        assert not can_move, "Stunned warrior should not be able to move"

        print("✅ Status Effects System: WORKING")
        return True
    except Exception as e:
        print(f"❌ Status Effects System: FAILED - {e}")
        return False

def test_all_unit_classes():
    """Test that all unit classes can be instantiated and have basic functionality"""
    print("👥 Testing All Unit Classes...")
    
    try:
        from units.hunter import Hunter
        from units.warrior import Warrior
        from units.mage import Mage
        from units.rogue import Rogue
        from units.cleric import Cleric
        
        # Test instantiation
        units = [
            Hunter(1),
            Warrior(1), 
            Mage(1),
            Rogue(1),
            Cleric(1)
        ]
        
        # Test basic properties
        for unit in units:
            assert hasattr(unit, 'name'), f"{unit.__class__.__name__} missing name"
            assert hasattr(unit, 'health'), f"{unit.__class__.__name__} missing health"
            assert hasattr(unit, 'max_health'), f"{unit.__class__.__name__} missing max_health"
            assert hasattr(unit, 'abilities'), f"{unit.__class__.__name__} missing abilities"
            assert hasattr(unit, 'ability_executor'), f"{unit.__class__.__name__} missing ability_executor"
            assert hasattr(unit, 'status_effect_manager'), f"{unit.__class__.__name__} missing status_effect_manager"
            assert unit.health > 0, f"{unit.__class__.__name__} health should be > 0"
            assert len(unit.abilities) > 0, f"{unit.__class__.__name__} should have abilities"
        
        print("✅ All Unit Classes: WORKING")
        return True
    except Exception as e:
        print(f"❌ All Unit Classes: FAILED - {e}")
        return False

def test_ability_system_integration():
    """Test that the ability system integration is working"""
    print("⚔️ Testing Ability System Integration...")
    
    try:
        from game_state import Game
        from units.warrior import Warrior
        from units.rogue import Rogue
        
        # Create game instance
        game = Game()
        warrior = Warrior(1)
        target = Rogue(2)
        
        # Add units to board
        game.board.add_unit(warrior, 4, 4)
        game.board.add_unit(target, 4, 5)
        
        # Set up game state
        game.current_player = 1
        game.current_player_ap = 10
        
        # Test basic attack
        initial_hp = target.health
        success = warrior.use_ability(1, target.position, game)  # Basic attack
        
        assert success, "Basic attack should succeed"
        assert target.health < initial_hp, "Target should take damage"
        assert game.current_player_ap < 10, "AP should be consumed"
        
        print("✅ Ability System Integration: WORKING")
        return True
    except Exception as e:
        print(f"❌ Ability System Integration: FAILED - {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 COMPREHENSIVE REFACTORED SYSTEMS TEST")
    print("=" * 50)
    
    tests = [
        test_unified_configuration_system,
        test_damage_calculator,
        test_status_effects_system,
        test_all_unit_classes,
        test_ability_system_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
        print()
    
    print("=" * 50)
    print(f"📊 RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL SYSTEMS WORKING PERFECTLY!")
        print("✅ Refactoring completed successfully!")
        return True
    else:
        print("❌ Some systems need attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Debug script to help identify the game interaction issue
"""

import pygame
from game_state import Game

def debug_game_issue():
    """Create a minimal test to identify the issue"""
    pygame.init()
    
    print("🔍 GAME INTERACTION DEBUG 🔍")
    print("=" * 35)
    
    # Create a game instance like the real game does
    try:
        game = Game(fullscreen=False)
        print(f"✅ Game created successfully")
        print(f"Game state: {getattr(game, 'state', 'Not set')}")
        print(f"Current player: {getattr(game, 'current_player', 'Not set')}")
        print(f"Setup phase: {getattr(game, 'setup_phase', 'Not set')}")
        
        # Check board units
        print(f"Board units: {len(game.board.units)}")
        
        # Check if there are any units on the board
        if game.board.units:
            print("Units on board:")
            for pos, unit in game.board.units.items():
                print(f"  {pos}: {unit.name} (Player {unit.player_id}) - AP: {unit.current_ap}/{unit.max_ap}")
                
                # Check if unit can move
                moves = unit.get_valid_moves(game.board)
                print(f"    Valid moves: {len(moves)}")
                print(f"    Can use move ability: {unit.can_use_ability(0)}")
                
                # Check passives
                if hasattr(unit, 'passive_manager'):
                    print(f"    Passives: {len(unit.passive_manager.passive_abilities)}")
                    for passive in unit.passive_manager.passive_abilities:
                        print(f"      • {passive.name}")
        else:
            print("❌ No units on board - this might be the issue!")
            print("The game might be in setup phase or units weren't created properly")
        
        # Check game buttons and UI elements
        if hasattr(game, 'unit_buttons'):
            print(f"Unit buttons: {len(game.unit_buttons)}")
        
        if hasattr(game, 'available_units'):
            print(f"Available units: {game.available_units}")
        
        # Check if the game is in setup phase
        from game_constants import STATE_SETUP, STATE_PLAYING, STATE_GAME_OVER
        if hasattr(game, 'state'):
            if game.state == STATE_SETUP:
                print("🚨 Game is in SETUP phase - you need to place units first!")
            elif game.state == STATE_PLAYING:
                print("✅ Game is in PLAYING phase")
            elif game.state == STATE_GAME_OVER:
                print("🚨 Game is in GAME_OVER phase")
        
    except Exception as e:
        print(f"❌ Error creating game: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 35)
    print("🔍 DEBUG COMPLETE")
    print("\nMost likely issues:")
    print("1. Game is in SETUP phase - place units first")
    print("2. No units on board - check unit placement")
    print("3. Wrong player turn - check turn management")
    print("4. UI event handling - check mouse click processing")
    
    print("\n💡 Quick fixes to try:")
    print("• Make sure you've placed units during setup")
    print("• Check if it's your turn (current player)")
    print("• Try right-clicking or different mouse buttons")
    print("• Check if units have AP remaining")

if __name__ == "__main__":
    debug_game_issue()

#!/usr/bin/env python3

import pygame
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state import Game
from units.mage import Mage
from units.warrior import Warrior

def test_mage_damage_fix():
    """Test that Mage damage configuration is working"""
    print("🔮 TESTING MAGE DAMAGE CONFIGURATION FIX")
    print("=" * 50)
    
    # Initialize pygame
    pygame.init()
    
    try:
        # Create a game instance
        game = Game()
        
        # Create units
        mage = Mage(player_id=1)
        target = Warrior(player_id=2)
        
        game.board.add_unit(mage, 4, 4)
        game.board.add_unit(target, 4, 5)  # Adjacent for attack
        
        # Set up game state
        game.current_player = 1
        game.current_player_ap = 10
        game.units_acted_this_turn = set()
        
        print(f"Mage at {mage.position}")
        print(f"Target at {target.position}")
        print(f"Target HP: {target.health}/{target.max_health}")
        
        # Test configured damage values
        from config_loader import ConfigLoader
        config_loader = ConfigLoader()
        
        print(f"\n📋 CONFIGURED DAMAGE VALUES:")
        print(f"Basic Attack: {config_loader.get_ability_damage('Mage', 'Attack')}")
        print(f"Fireball: {config_loader.get_ability_damage('Mage', 'Fireball')}")
        print(f"Ice Spike: {config_loader.get_ability_damage('Mage', 'Ice Spike')}")
        print(f"Arcane Missile: {config_loader.get_ability_damage('Mage', 'Arcane Missile')}")
        print(f"Cone of Cold: {config_loader.get_ability_damage('Mage', 'Cone of Cold')}")
        
        # Test 1: Basic Attack
        print(f"\n🔮 TEST 1: Basic Attack")
        initial_hp = target.health
        attack_success = mage.use_ability(1, target.position, game)
        damage_dealt = initial_hp - target.health
        expected_damage = config_loader.get_ability_damage('Mage', 'Attack')
        
        print(f"Attack success: {attack_success}")
        print(f"Damage dealt: {damage_dealt}")
        print(f"Expected damage: {expected_damage}")
        
        if abs(damage_dealt - expected_damage) < 0.01:
            print("✅ Basic Attack damage configuration WORKING!")
        else:
            print("❌ Basic Attack damage configuration BROKEN!")
        
        # Reset for next test
        target.health = target.max_health
        game.current_player_ap = 10
        game.units_acted_this_turn = set()
        mage.has_acted_this_turn = False
        
        # Test 2: Fireball
        print(f"\n🔥 TEST 2: Fireball")
        fireball_idx = None
        for i, ability in enumerate(mage.abilities):
            if ability.name == "Fireball":
                fireball_idx = i
                break
        
        if fireball_idx is not None:
            initial_hp = target.health
            fireball_success = mage.use_ability(fireball_idx, target.position, game)
            damage_dealt = initial_hp - target.health
            expected_damage = config_loader.get_ability_damage('Mage', 'Fireball')
            
            print(f"Fireball success: {fireball_success}")
            print(f"Damage dealt: {damage_dealt}")
            print(f"Expected damage: {expected_damage}")
            
            if abs(damage_dealt - expected_damage) < 0.01:
                print("✅ Fireball damage configuration WORKING!")
            else:
                print("❌ Fireball damage configuration BROKEN!")
        else:
            print("❌ Fireball ability not found!")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_mage_damage_fix()
    if success:
        print("\n🎉 MAGE DAMAGE CONFIGURATION TEST COMPLETE!")
    else:
        print("\n💥 MAGE DAMAGE CONFIGURATION TEST FAILED!")

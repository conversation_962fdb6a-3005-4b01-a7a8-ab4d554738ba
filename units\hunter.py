import pygame
from units_core import Unit, SimpleAbility, MoveAbility, AttackAbility, SummonAbility
from game_config import GAME_CONFIG
import game_constants as const
import math

# Import unified systems
from core.configuration_manager import get_config_manager
from core.ability_system import DamageCalculator
from core.status_effects import StatusEffectType

class Hunter(Unit):
    """Hunter unit - Ranged attacker with unique movement (jump over one unit)"""
    def __init__(self, player_id):
        # Initialize with unified configuration system
        config_manager = get_config_manager()
        hunter_config = config_manager.get_unit_config("Hunter")

        super().__init__(
            player_id,
            health=hunter_config.get("health", 5),
            max_health=hunter_config.get("health", 5)
        )
        self.name = "Hunter"
        self.max_ap = hunter_config.get("max_ap", 7)
        self.current_ap = hunter_config.get("max_ap", 7)
        self.board = None  # Will be set later

        # Load image (placeholder for now)
        self.image = self._create_placeholder_image((0, 150, 0) if player_id == 1 else (150, 0, 0))

        # Abilities with unified configuration
        self.abilities = [
            MoveAbility(self),
            AttackAbility(self),
            SimpleAbility("Ricochet Shot", config_manager.get_ability_ap_cost("Hunter", "Ricochet Shot"), "Shot bounces to a second target", cooldown=2, owner=self),
            SimpleAbility("Triple Shot", config_manager.get_ability_ap_cost("Hunter", "Triple Shot"), "Fire three arrows in a cone", cooldown=3, owner=self),
            SimpleAbility("Knockback Shot", config_manager.get_ability_ap_cost("Hunter", "Knockback Shot"), "Push target back 1 tile", cooldown=1, owner=self),
            SimpleAbility("Spread Shot", config_manager.get_ability_ap_cost("Hunter", "Spread Shot"), "Hit up to 3 targets in a line", cooldown=2, owner=self),
            SimpleAbility("Crippling Shot", config_manager.get_ability_ap_cost("Hunter", "Crippling Shot"), "Immobilize target for 1 turn", cooldown=3, owner=self),
            SummonAbility(self, config_manager.get_ability_ap_cost("Hunter", "Summon"))
        ]
        # Store ability names that are directional for easier checking in game_logic or game_state
        self.directional_abilities = {"Triple Shot", "Spread Shot"}

        # Configuration is automatically applied by the base Unit class


    def _create_placeholder_image(self, color):
        """Create a placeholder image with a bow and arrow symbol"""
        surf = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA) # Use const.CELL_SIZE
        
        # Main circle
        pygame.draw.circle(surf, color, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2) # Use const.CELL_SIZE
        pygame.draw.circle(surf, (255, 255, 255), (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2, 2) # Use const.CELL_SIZE
        
        # Draw bow (arc)
        bow_color = (200, 200, 100)
        pygame.draw.arc(surf, bow_color, [const.CELL_SIZE//2 - 20, const.CELL_SIZE//2 - 15, 15, 30], math.radians(270-20), math.radians(270+20), 3) # Use const.CELL_SIZE
        # Draw arrow
        arrow_color = (150, 100, 50)
        pygame.draw.line(surf, arrow_color, (const.CELL_SIZE//2 - 15, const.CELL_SIZE//2), (const.CELL_SIZE//2 + 15, const.CELL_SIZE//2), 3) # Use const.CELL_SIZE
        pygame.draw.polygon(surf, arrow_color, [
            (const.CELL_SIZE//2 + 15, const.CELL_SIZE//2),
            (const.CELL_SIZE//2 + 10, const.CELL_SIZE//2 - 4),
            (const.CELL_SIZE//2 + 10, const.CELL_SIZE//2 + 4)
        ]) # Use const.CELL_SIZE for all
        return surf

    def get_valid_moves(self, board):
        """Hunter moves 1 square diagonally, or jumps 2 squares diagonally over one unit."""
        self.board = board
        valid_moves = []
        row, col = self.position

        if self.has_status('Immobilized') or self.has_status('Stunned'):
            return []

        # Standard 1-square diagonal moves
        diagonal_directions = [(-1, -1), (-1, 1), (1, -1), (1, 1)]
        for dr, dc in diagonal_directions:
            new_row, new_col = row + dr, col + dc
            if 0 <= new_row < const.BOARD_SIZE and 0 <= new_col < const.BOARD_SIZE and (new_row, new_col) not in board.units: # Use const.BOARD_SIZE
                valid_moves.append((new_row, new_col))

        # Jump moves (2 squares diagonally)
        for dr_jump, dc_jump in diagonal_directions: # Iterate over base diagonal directions
            # Scale them for a 2-square jump
            jump_dr, jump_dc = dr_jump * 2, dc_jump * 2
            
            jump_to_row, jump_to_col = row + jump_dr, col + jump_dc
            # The 'over' tile is simply the 1-step diagonal tile in that direction
            # over_row, over_col = row + dr_jump, col + dc_jump 

            if not (0 <= jump_to_row < const.BOARD_SIZE and 0 <= jump_to_col < const.BOARD_SIZE): # Use const.BOARD_SIZE
                continue # Jump destination is off board

            # Can jump if destination is empty, regardless of what's on the 'over' tile
            if (jump_to_row, jump_to_col) not in board.units:
                valid_moves.append((jump_to_row, jump_to_col))
        
        return list(set(valid_moves)) # Remove duplicates if any

    def get_valid_attacks(self, board):
        """Hunters attack in straight diagonal lines up to 4 tiles away."""
        self.board = board
        valid_attacks = []
        row, col = self.position

        if self.has_status('Stunned'): # Immobilized doesn't prevent attacks for hunter
            return []

        # Diagonal directions only
        directions = [(-1, -1), (-1, 1), (1, -1), (1, 1)]
        max_range = 4

        for dr, dc in directions:
            for i in range(1, max_range + 1):
                r, c = row + dr * i, col + dc * i
                if not (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE): # Use const.BOARD_SIZE
                    break # Off board
                if (r,c) in board.units:
                    if board.units[(r,c)].player_id != self.player_id and not board.units[(r,c)].sanctuary:
                        valid_attacks.append((r,c))
                    break # Line of sight blocked by friendly or enemy
        return valid_attacks

    def get_attack_damage(self, target_pos):
        """Calculate Hunter basic attack damage using unified damage calculation"""
        return DamageCalculator.calculate_basic_attack_damage(self, target_pos)

    def get_ability_targets(self, ability_idx, board): # Renamed from get_valid_ability_targets for consistency
        """Get valid targets for Hunter abilities."""
        self.board = board
        # Basic move
        if ability_idx == 0: return self.get_valid_moves(board)
        # Basic attack
        if ability_idx == 1: return self.get_valid_attacks(board)

        ability_name = self.abilities[ability_idx].name
        
        # Ricochet Shot: Primary target same as basic attack.
        if ability_name == "Ricochet Shot":
            return self.get_valid_attacks(board)

        # Triple Shot: Targets a direction (represented by the first tile in that direction)
        elif ability_name == "Triple Shot":
            # Returns adjacent tiles; the ability will fire towards and past it.
            targets = []
            row, col = self.position
            for dr in [-1, 0, 1]:
                for dc in [-1, 0, 1]:
                    if dr == 0 and dc == 0: continue
                    r, c = row + dr, col + dc
                    if 0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE: # Use const.BOARD_SIZE
                        targets.append((r,c)) # Can target empty or occupied tile to define direction
            return targets
            
        # Knockback Shot: Same targets as basic attack.
        elif ability_name == "Knockback Shot":
            return self.get_valid_attacks(board)

        # Spread Shot: Targets a direction (represented by the first tile in that direction)
        # The ability will affect the target tile and two tiles perpendicular to the line of shot.
        elif ability_name == "Spread Shot":
            # Returns adjacent tiles; the ability will fire towards and past it.
            targets = []
            row, col = self.position
            # Orthogonal directions only for simplicity of spread
            for dr, dc in [(-1,0), (1,0), (0,-1), (0,1)]:
                r, c = row + dr, col + dc
                if 0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE: # Use const.BOARD_SIZE
                    targets.append((r,c))
            return targets

        # Crippling Shot: Same targets as basic attack.
        elif ability_name == "Crippling Shot":
            return self.get_valid_attacks(board)

        # Summon: Can summon at any position the Hunter could move to
        elif ability_name == "Summon":
            return self.get_valid_moves(board)

        return []

    def use_ability(self, ability_idx, target_pos, game=None):
        """
        Use an ability with unified ability execution system.
        Hunter-specific abilities are handled by registered methods.
        """
        # Ensure self.board is set if game object is provided
        if game:
            self.board = game.board

        # Register Hunter-specific ability handlers with the unified system
        self._register_ability_handlers()

        # Use unified ability executor (this handles all validation, AP spending, etc.)
        return super().use_ability(ability_idx, target_pos, game)

    def _register_ability_handlers(self):
        """Register Hunter-specific ability handlers with the unified ability system"""
        ability_executor = self.ability_executor

        # Register handlers for each Hunter ability with proper lambda wrappers
        ability_executor.register_ability_handler("Ricochet Shot", lambda unit, target_pos, game: self._use_ricochet_shot(target_pos, game))
        ability_executor.register_ability_handler("Triple Shot", lambda unit, target_pos, game: self._use_triple_shot(target_pos, game))
        ability_executor.register_ability_handler("Knockback Shot", lambda unit, target_pos, game: self._use_knockback_shot(target_pos, game))
        ability_executor.register_ability_handler("Spread Shot", lambda unit, target_pos, game: self._use_spread_shot(target_pos, game))
        ability_executor.register_ability_handler("Crippling Shot", lambda unit, target_pos, game: self._use_crippling_shot(target_pos, game))

    def _use_triple_shot(self, target_pos, game=None):
        """Triple Shot: Fire three arrows in a cone pattern using unified damage calculation"""
        # Get configured damage using unified damage calculation
        shot_damage = DamageCalculator.calculate_ability_damage(self, "Triple Shot", target_pos)

        # Target pos indicates direction of middle shot
        shot_dir_row, shot_dir_col = target_pos[0] - self.position[0], target_pos[1] - self.position[1]
        # Normalize (crude)
        if shot_dir_row != 0: shot_dir_row //= abs(shot_dir_row)
        if shot_dir_col != 0: shot_dir_col //= abs(shot_dir_col)

        directions = [
            (shot_dir_row, shot_dir_col), # Middle shot
            # Perpendicular directions (approximate for diagonals)
            (shot_dir_col if shot_dir_row == 0 else shot_dir_row, -shot_dir_row if shot_dir_col == 0 else -shot_dir_col),
            (-shot_dir_col if shot_dir_row == 0 else -shot_dir_row, shot_dir_row if shot_dir_col == 0 else shot_dir_col)
        ]

        # Correct perpendiculars for pure diagonal shots
        if abs(shot_dir_row) == 1 and abs(shot_dir_col) == 1:
            directions = [
                (shot_dir_row, shot_dir_col),
                (shot_dir_row, 0), # one side
                (0, shot_dir_col)  # other side
            ]
        elif shot_dir_row == 0: # Horizontal
             directions = [(0, shot_dir_col), (shot_dir_col, shot_dir_col), (-shot_dir_col, shot_dir_col)]
        elif shot_dir_col == 0: # Vertical
             directions = [(shot_dir_row, 0), (shot_dir_row, shot_dir_row), (shot_dir_row, -shot_dir_row)]

        for dr, dc in directions:
            self._fire_shot_in_direction((dr, dc), shot_damage)
        return True

    def _use_knockback_shot(self, target_pos, game=None):
        """Knockback Shot: Damage and push target back using unified damage calculation"""
        target_unit = self.board.units.get(target_pos)
        if not (target_unit and target_unit.player_id != self.player_id):
            return False

        # Get configured damage using unified damage calculation
        knockback_damage = DamageCalculator.calculate_ability_damage(self, "Knockback Shot", target_pos)
        target_unit.take_damage(knockback_damage, self)

        # Knockback logic
        kb_dir_row, kb_dir_col = target_pos[0] - self.position[0], target_pos[1] - self.position[1]
        if kb_dir_row != 0: kb_dir_row //= abs(kb_dir_row)
        if kb_dir_col != 0: kb_dir_col //= abs(kb_dir_col)

        new_pos_row, new_pos_col = target_pos[0] + kb_dir_row, target_pos[1] + kb_dir_col
        if 0 <= new_pos_row < const.BOARD_SIZE and 0 <= new_pos_col < const.BOARD_SIZE and (new_pos_row, new_pos_col) not in self.board.units:
            # Move unit
            del self.board.units[target_unit.position]
            target_unit.position = (new_pos_row, new_pos_col)
            self.board.units[(new_pos_row, new_pos_col)] = target_unit
            print(f"{target_unit.name} knocked back to {target_unit.position}")
        else:
            print(f"{target_unit.name} knockback blocked or off board.")
        return True

    def _use_spread_shot(self, target_pos, game=None):
        """
        Spread Shot (Multishot): Fire arrows in orthogonal direction plus the two diagonals
        that contain that orthogonal (3 arrows total in a spread pattern).

        User preference: "Multishot ability should fire arrows in orthogonal direction
        plus the two diagonals that contain that orthogonal (3 arrows total in a spread pattern)."
        """
        # Get configured damage using unified damage calculation
        spread_damage = DamageCalculator.calculate_ability_damage(self, "Spread Shot", target_pos)

        # Determine primary shot direction (must be orthogonal)
        shot_dir_row, shot_dir_col = target_pos[0] - self.position[0], target_pos[1] - self.position[1]

        # Normalize to get orthogonal direction
        if shot_dir_row != 0: shot_dir_row = shot_dir_row // abs(shot_dir_row)
        if shot_dir_col != 0: shot_dir_col = shot_dir_col // abs(shot_dir_col)

        # Ensure it's orthogonal (not diagonal)
        if abs(shot_dir_row) == 1 and abs(shot_dir_col) == 1:
            # If diagonal was targeted, convert to nearest orthogonal
            if abs(target_pos[0] - self.position[0]) > abs(target_pos[1] - self.position[1]):
                shot_dir_col = 0  # Make it vertical
            else:
                shot_dir_row = 0  # Make it horizontal

        # Define the 3 spread directions: orthogonal + 2 diagonals
        spread_directions = [(shot_dir_row, shot_dir_col)]  # Primary orthogonal direction

        if shot_dir_row != 0:  # Vertical shot (North or South)
            spread_directions.append((shot_dir_row, -1))  # Add diagonal left
            spread_directions.append((shot_dir_row, 1))   # Add diagonal right
        else:  # Horizontal shot (East or West)
            spread_directions.append((-1, shot_dir_col))  # Add diagonal up
            spread_directions.append((1, shot_dir_col))   # Add diagonal down

        print(f"Spread Shot firing in directions: {spread_directions}")

        targets_hit_count = 0

        # Fire arrow in each of the 3 directions
        for direction in spread_directions:
            dr, dc = direction

            # Fire arrow in this direction until it hits something or goes off board
            for distance in range(1, const.BOARD_SIZE):
                check_r = self.position[0] + dr * distance
                check_c = self.position[1] + dc * distance

                # Check if position is on board
                if not (0 <= check_r < const.BOARD_SIZE and 0 <= check_c < const.BOARD_SIZE):
                    break

                # Check if there's a unit at this position
                unit_at_pos = self.board.units.get((check_r, check_c))
                if unit_at_pos:
                    if unit_at_pos.player_id != self.player_id:
                        # Hit enemy
                        print(f"Spread Shot arrow hits {unit_at_pos.name} at ({check_r}, {check_c})")
                        unit_at_pos.take_damage(spread_damage, self)
                        targets_hit_count += 1
                        break  # Arrow stops after hitting target
                    else:
                        # Hit friendly unit - arrow stops
                        print(f"Spread Shot arrow stopped by friendly {unit_at_pos.name} at ({check_r}, {check_c})")
                        break

        print(f"Spread Shot hit {targets_hit_count} targets total")
        return True

    def _use_crippling_shot(self, target_pos, game=None):
        """Crippling Shot: Damage and immobilize target using unified systems"""
        target_unit = self.board.units.get(target_pos)
        if not (target_unit and target_unit.player_id != self.player_id):
            return False

        # Get configured damage using unified damage calculation
        crippling_damage = DamageCalculator.calculate_ability_damage(self, "Crippling Shot", target_pos)
        target_unit.take_damage(crippling_damage, self)

        # Apply Crippled status effect using unified status system
        target_unit.apply_status("Crippled", 1)
        print(f"{target_unit.name} is crippled for 1 turn")
        return True

    # Helper for abilities like Triple Shot
    def _fire_shot_in_direction(self, direction, damage=None):
        """Fire a shot in a specific direction with configurable damage"""
        dr, dc = direction
        max_range = 4 # Same as basic attack range

        # Use provided damage or calculate default
        if damage is None:
            damage = DamageCalculator.calculate_ability_damage(self, "Triple Shot")

        for i in range(1, max_range + 1):
            r, c = self.position[0] + dr * i, self.position[1] + dc * i
            if not (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE):
                return # Off board

            target_unit = self.board.units.get((r,c))
            if target_unit:
                if target_unit.player_id != self.player_id and not target_unit.sanctuary:
                    print(f"Shot hits {target_unit.name} at {(r,c)}")
                    target_unit.take_damage(damage, self)
                return # Shot stops at first unit hit (friend or foe, unless foe has sanctuary and is hit)
        # print(f"Shot fired in direction {direction} missed.") # Optional: for debugging

    def _use_ricochet_shot(self, target_pos, game=None):
        """
        Ricochet Shot: Hit primary target, then ricochet to a secondary target.
        The ricochet follows realistic physics - bounces in a logical direction.
        Uses unified damage calculation system.
        """
        # Get configured damage using unified damage calculation
        shot_damage = DamageCalculator.calculate_ability_damage(self, "Ricochet Shot", target_pos)

        # Hit primary target
        primary_target_unit = self.board.units.get(target_pos)
        if not (primary_target_unit and primary_target_unit.player_id != self.player_id):
            return False

        primary_target_unit.take_damage(shot_damage, self)
        print(f"Ricochet Shot hits {primary_target_unit.name} at {target_pos}")

        # Calculate ricochet direction based on incoming shot trajectory
        hunter_pos = self.position
        shot_direction = (target_pos[0] - hunter_pos[0], target_pos[1] - hunter_pos[1])

        # Find potential ricochet targets in logical bounce directions
        ricochet_targets = self._find_ricochet_targets(target_pos, shot_direction)

        if ricochet_targets:
            # Pick the best ricochet target (closest in the bounce direction)
            best_target = ricochet_targets[0]
            ricochet_target_unit = self.board.units.get(best_target)
            if ricochet_target_unit:
                print(f"Ricochet Shot bounces to {ricochet_target_unit.name} at {best_target}")
                ricochet_target_unit.take_damage(shot_damage, self)
        else:
            print("Ricochet Shot has no valid bounce targets")

        return True

    def _find_ricochet_targets(self, primary_pos, shot_direction):
        """
        Find valid ricochet targets based on realistic bounce physics.
        Prioritizes targets in diagonal directions from the primary target.
        """
        potential_targets = []

        # Define possible ricochet directions (8 directions around primary target)
        ricochet_directions = [
            (-1, -1), (-1, 0), (-1, 1),  # North directions
            (0, -1),           (0, 1),   # West, East
            (1, -1),  (1, 0),  (1, 1)    # South directions
        ]

        # Prioritize directions that make physical sense for a ricochet
        # Prefer diagonal bounces and directions that continue the shot's momentum
        for dr, dc in ricochet_directions:
            ricochet_pos = (primary_pos[0] + dr, primary_pos[1] + dc)

            # Check if position is on board and has an enemy unit
            if (0 <= ricochet_pos[0] < const.BOARD_SIZE and
                0 <= ricochet_pos[1] < const.BOARD_SIZE and
                ricochet_pos in self.board.units):

                target_unit = self.board.units[ricochet_pos]
                if (target_unit.player_id != self.player_id and
                    target_unit.is_alive() and
                    not target_unit.sanctuary):

                    # Calculate priority based on ricochet physics
                    priority = self._calculate_ricochet_priority(shot_direction, (dr, dc))
                    potential_targets.append((priority, ricochet_pos))

        # Sort by priority (lower number = higher priority)
        potential_targets.sort()
        return [pos for priority, pos in potential_targets]

    def _calculate_ricochet_priority(self, shot_direction, ricochet_direction):
        """
        Calculate ricochet priority based on physics.
        Lower number = higher priority (better ricochet target).
        """
        shot_dr, shot_dc = shot_direction
        ricochet_dr, ricochet_dc = ricochet_direction

        # Normalize shot direction for comparison
        if shot_dr != 0:
            shot_dr = shot_dr // abs(shot_dr)
        if shot_dc != 0:
            shot_dc = shot_dc // abs(shot_dc)

        # Priority 1: Diagonal ricochets (most realistic)
        if abs(ricochet_dr) == 1 and abs(ricochet_dc) == 1:
            diagonal_bonus = 0
        else:
            diagonal_bonus = 2

        # Priority 2: Continuing momentum (forward bounces)
        momentum_bonus = 0
        if (shot_dr != 0 and ricochet_dr * shot_dr > 0) or (shot_dc != 0 and ricochet_dc * shot_dc > 0):
            momentum_bonus = -1  # Bonus for continuing momentum

        # Priority 3: Avoid direct backwards bounces
        if ricochet_dr == -shot_dr and ricochet_dc == -shot_dc:
            backwards_penalty = 5
        else:
            backwards_penalty = 0

        return diagonal_bonus + momentum_bonus + backwards_penalty

    @property
    def multi_stage_abilities(self): # Example for future use if some abilities need multiple targeting steps
        return []

    # def get_ability_targets(self, ability_idx, board): # Overrides Unit.get_ability_targets
    #     return self.get_ability_targets(board, ability_idx) 
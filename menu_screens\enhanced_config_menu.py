#!/usr/bin/env python3
"""
Enhanced Configuration Menu for Tactical PvP Strategy Game

Provides per-class configuration with sliders for:
- Base stats (HP, Max AP, Movement Range)
- Ability costs, damage, and cooldowns
- No friendly fire settings
"""

import pygame
import sys
from pygame import gfxdraw
from menu_screens.button import Button
from game_config import GAME_CONFIG

# Constants
WINDOW_WIDTH = 1280
WINDOW_HEIGHT = 720
DARK_GRAY = (30, 30, 30)
LIGHT_GRAY = (200, 200, 200)
SELECTED_COLOR = (0, 200, 100)
DISABLED_COLOR = (100, 100, 100)
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)

class EnhancedConfigMenu:
    def __init__(self, screen, clock):
        self.screen = screen
        self.clock = clock
        self.running = True
        
        self.title_font = pygame.font.Font(None, 74)
        self.button_font = pygame.font.Font(None, 48)
        self.option_font = pygame.font.Font(None, 36)
        self.label_font = pygame.font.Font(None, 28)
        
        current_screen_width, current_screen_height = self.screen.get_size()
        self.back_button = Button(current_screen_width//2 - 100, current_screen_height - 100, 200, 60, "Back")
        self.save_button = Button(current_screen_width//2 - 100, current_screen_height - 180, 200, 60, "Save Changes")
        
        self.class_buttons = self._create_class_buttons()
        self.selected_class = "Hunter"  # Start with Hunter
        
        self.sliders = self._create_sliders()
        self.original_config = self._deep_copy_config(GAME_CONFIG)
        
        # Track changes
        self.config_changed = False
    
    def _deep_copy_config(self, config):
        """Create a deep copy of the config"""
        import copy
        return copy.deepcopy(config)
    
    def _create_class_buttons(self):
        """Create buttons for each class"""
        buttons = {}
        button_width = 120
        button_height = 40
        start_x = 50
        y_pos = 120
        
        class_names = ["Hunter", "Warrior", "Rogue", "Mage", "Cleric", "King", "Pawn"]
        
        for i, class_name in enumerate(class_names):
            x_pos = start_x + (i % 4) * (button_width + 20)
            y_offset = 0 if i < 4 else button_height + 10
            buttons[class_name] = Button(x_pos, y_pos + y_offset, button_width, button_height, class_name)
        
        return buttons
    
    def _create_sliders(self):
        """Create sliders for the currently selected class"""
        sliders = {}
        current_screen_width, _ = self.screen.get_size()
        slider_width = 250
        slider_height = 25
        start_x = current_screen_width//2 - slider_width//2
        start_y = 250
        spacing = 35
        
        # Get config for selected class
        class_config_key = f"{self.selected_class.lower()}_config"
        class_config = GAME_CONFIG.get(class_config_key, {})
        
        slider_idx = 0
        
        # Base stats
        base_stats = [
            ("health", "Health", 1, 15),
            ("max_ap", "Max AP", 3, 15),
            ("movement_range", "Movement Range", 1, 5)
        ]
        
        for key, label, min_val, max_val in base_stats:
            sliders[key] = {
                'rect': pygame.Rect(start_x, start_y + spacing * slider_idx, slider_width, slider_height),
                'label': label,
                'min': min_val,
                'max': max_val,
                'value': class_config.get(key, min_val),
                'dragging': False,
                'category': 'base'
            }
            slider_idx += 1
        
        # Add separator
        slider_idx += 0.5
        
        # Ability-specific settings
        ability_configs = self._get_ability_configs_for_class(self.selected_class)
        
        for ability_key, label, min_val, max_val in ability_configs:
            sliders[ability_key] = {
                'rect': pygame.Rect(start_x, start_y + spacing * slider_idx, slider_width, slider_height),
                'label': label,
                'min': min_val,
                'max': max_val,
                'value': class_config.get(ability_key, min_val),
                'dragging': False,
                'category': 'ability'
            }
            slider_idx += 1
        
        return sliders
    
    def _get_ability_configs_for_class(self, class_name):
        """Get ability configuration options for a specific class"""
        ability_configs = []
        
        if class_name == "Hunter":
            ability_configs = [
                ("ricochet_shot_ap_cost", "Ricochet Shot AP", 1, 5),
                ("triple_shot_ap_cost", "Triple Shot AP", 1, 5),
                ("knockback_shot_ap_cost", "Knockback Shot AP", 1, 4),
                ("multishot_ap_cost", "Multishot AP", 1, 5),
                ("piercing_shot_ap_cost", "Piercing Shot AP", 1, 5),
                ("crippling_shot_ap_cost", "Crippling Shot AP", 1, 4)
            ]
        elif class_name == "Rogue":
            ability_configs = [
                ("backstab_ap_cost", "Backstab AP", 1, 4),
                ("poison_strike_ap_cost", "Poison Strike AP", 1, 4),
                ("smoke_bomb_ap_cost", "Smoke Bomb AP", 2, 5),
                ("shadow_step_ap_cost", "Shadow Step AP", 1, 4),
                ("fan_of_knives_ap_cost", "Fan of Knives AP", 1, 4),
                ("assassination_ap_cost", "Assassination AP", 2, 6)
            ]
        elif class_name == "Warrior":
            ability_configs = [
                ("cleave_attack_ap_cost", "Cleave Attack AP", 1, 4),
                ("shield_bash_ap_cost", "Shield Bash AP", 1, 4),
                ("charge_ap_cost", "Charge AP", 2, 5),
                ("defensive_stance_ap_cost", "Defensive Stance AP", 1, 4),
                ("riposte_ap_cost", "Riposte AP", 1, 4)
            ]
        elif class_name == "Mage":
            ability_configs = [
                ("fireball_ap_cost", "Fireball AP", 2, 5),
                ("ice_spike_ap_cost", "Ice Spike AP", 1, 4),
                ("teleport_ap_cost", "Teleport AP", 1, 4),
                ("frost_nova_ap_cost", "Frost Nova AP", 2, 6),
                ("arcane_missile_ap_cost", "Arcane Missile AP", 2, 5),
                ("cone_of_cold_ap_cost", "Cone of Cold AP", 2, 5)
            ]
        elif class_name == "Cleric":
            ability_configs = [
                ("heal_ap_cost", "Heal AP", 1, 4),
                ("mass_heal_ap_cost", "Mass Heal AP", 3, 6),
                ("cleanse_ap_cost", "Cleanse AP", 1, 4),
                ("sanctuary_ap_cost", "Sanctuary AP", 3, 7),
                ("divine_protection_ap_cost", "Divine Protection AP", 2, 5),
                ("holy_smite_ap_cost", "Holy Smite AP", 2, 5)
            ]
        elif class_name == "King":
            ability_configs = [
                ("royal_decree_ap_cost", "Royal Decree AP", 2, 5),
                ("divine_shield_ap_cost", "Divine Shield AP", 2, 5),
                ("tactical_retreat_ap_cost", "Tactical Retreat AP", 2, 5),
                ("inspire_ap_cost", "Inspire AP", 3, 6),
                ("royal_execution_ap_cost", "Royal Execution AP", 3, 7)
            ]
        # Pawn has minimal abilities, so fewer configs
        
        return ability_configs
    
    def handle_event(self, event):
        """Handle pygame events"""
        if event.type == pygame.QUIT:
            self.running = False
            return "quit"
        
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                self.running = False
                return "back"
        
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left click
                mouse_pos = pygame.mouse.get_pos()
                
                # Check back button
                if self.back_button.is_clicked(mouse_pos):
                    self.running = False
                    return "back"
                
                # Check save button
                if self.save_button.is_clicked(mouse_pos):
                    self._save_config()
                    return "saved"
                
                # Check class buttons
                for class_name, button in self.class_buttons.items():
                    if button.is_clicked(mouse_pos):
                        if class_name != self.selected_class:
                            self.selected_class = class_name
                            self.sliders = self._create_sliders()  # Recreate sliders for new class
                        break
                
                # Check sliders
                for slider in self.sliders.values():
                    if slider['rect'].collidepoint(mouse_pos):
                        slider['dragging'] = True
                        self._update_slider_value(slider, mouse_pos[0])
                        break
        
        if event.type == pygame.MOUSEBUTTONUP:
            if event.button == 1:  # Left click release
                for slider in self.sliders.values():
                    slider['dragging'] = False
        
        if event.type == pygame.MOUSEMOTION:
            mouse_pos = pygame.mouse.get_pos()
            for slider in self.sliders.values():
                if slider['dragging']:
                    self._update_slider_value(slider, mouse_pos[0])
        
        return None
    
    def _update_slider_value(self, slider, mouse_x):
        """Update slider value based on mouse position"""
        relative_x = mouse_x - slider['rect'].x
        relative_x = max(0, min(relative_x, slider['rect'].width))
        
        # Calculate value
        value_range = slider['max'] - slider['min']
        normalized = relative_x / slider['rect'].width
        new_value = slider['min'] + (normalized * value_range)
        
        # Round to integer
        slider['value'] = round(new_value)
        self.config_changed = True
    
    def _save_config(self):
        """Save current slider values to game config"""
        class_config_key = f"{self.selected_class.lower()}_config"
        
        if class_config_key not in GAME_CONFIG:
            GAME_CONFIG[class_config_key] = {}
        
        for key, slider in self.sliders.items():
            GAME_CONFIG[class_config_key][key] = slider['value']
        
        # Write to file
        with open('game_config.py', 'w') as f:
            f.write(f'GAME_CONFIG = {repr(GAME_CONFIG)}')
        
        self.config_changed = False
        print(f"Saved {self.selected_class} configuration")
    
    def draw(self):
        """Draw the configuration menu"""
        self.screen.fill(DARK_GRAY)
        
        # Title
        title_text = self.title_font.render("Unit Configuration", True, WHITE)
        title_rect = title_text.get_rect(center=(WINDOW_WIDTH//2, 50))
        self.screen.blit(title_text, title_rect)
        
        # Class selection
        class_text = self.option_font.render("Select Class:", True, WHITE)
        self.screen.blit(class_text, (50, 90))
        
        # Draw class buttons
        for class_name, button in self.class_buttons.items():
            color = SELECTED_COLOR if class_name == self.selected_class else LIGHT_GRAY
            button.draw(self.screen, color)
        
        # Current class title
        class_title = self.button_font.render(f"{self.selected_class} Configuration", True, WHITE)
        self.screen.blit(class_title, (50, 220))
        
        # Draw sliders
        self._draw_sliders()
        
        # Draw buttons
        self.back_button.draw(self.screen)
        save_color = SELECTED_COLOR if self.config_changed else LIGHT_GRAY
        self.save_button.draw(self.screen, save_color)
        
        # Instructions
        if self.config_changed:
            change_text = self.label_font.render("* Unsaved changes", True, (255, 200, 0))
            self.screen.blit(change_text, (WINDOW_WIDTH//2 - 100, WINDOW_HEIGHT - 250))
    
    def _draw_sliders(self):
        """Draw all sliders for the current class"""
        current_category = None
        
        for key, slider in self.sliders.items():
            # Draw category headers
            if slider['category'] != current_category:
                current_category = slider['category']
                if current_category == 'base':
                    header_text = self.option_font.render("Base Stats", True, WHITE)
                elif current_category == 'ability':
                    header_text = self.option_font.render("Ability Costs", True, WHITE)
                
                header_y = slider['rect'].y - 25
                self.screen.blit(header_text, (slider['rect'].x, header_y))
            
            # Draw slider background
            pygame.draw.rect(self.screen, LIGHT_GRAY, slider['rect'])
            pygame.draw.rect(self.screen, BLACK, slider['rect'], 2)
            
            # Draw slider handle
            value_ratio = (slider['value'] - slider['min']) / (slider['max'] - slider['min'])
            handle_x = slider['rect'].x + (value_ratio * slider['rect'].width)
            handle_rect = pygame.Rect(handle_x - 5, slider['rect'].y - 2, 10, slider['rect'].height + 4)
            pygame.draw.rect(self.screen, SELECTED_COLOR, handle_rect)
            
            # Draw label and value
            label_text = self.label_font.render(f"{slider['label']}: {slider['value']}", True, WHITE)
            label_y = slider['rect'].y - 20
            self.screen.blit(label_text, (slider['rect'].x, label_y))
    
    def run(self):
        """Main menu loop"""
        while self.running:
            for event in pygame.event.get():
                result = self.handle_event(event)
                if result:
                    return result
            
            self.draw()
            pygame.display.flip()
            self.clock.tick(60)
        
        return "back"

#!/usr/bin/env python3
"""
Test script for Cleric movement:
- Should move exactly 1 tile in orthogonal directions only
- No diagonal movement
- No multi-tile movement
"""

import pygame
from game_state import Game
from units.cleric import Cleric
from units.warrior import Warrior

def test_cleric_movement():
    """Test Cleric's 1-tile orthogonal movement"""
    pygame.init()
    
    print("⛪ TESTING CLERIC MOVEMENT ⛪")
    print("=" * 32)
    
    # Test 1: Basic Movement Pattern
    print("📋 TEST 1: Basic Movement Pattern")
    print("-" * 33)
    
    game = Game()
    cleric = Cleric(1)
    cleric.position = (4, 4)  # Center of board
    cleric.board = game.board
    game.board.units = {(4, 4): cleric}
    
    print(f"Cleric at center position: {cleric.position}")
    
    # Get movement options
    moves = cleric.get_valid_moves(game.board)
    print(f"Available moves: {moves}")
    print(f"Number of moves: {len(moves)}")
    
    # Expected moves: exactly 1 tile in each orthogonal direction
    expected_moves = [
        (3, 4),  # North (up 1)
        (5, 4),  # South (down 1)
        (4, 3),  # West (left 1)
        (4, 5)   # East (right 1)
    ]
    
    print(f"\n🔍 Checking expected moves:")
    all_correct = True
    for expected in expected_moves:
        if expected in moves:
            print(f"  ✅ {expected} (1 tile orthogonal)")
        else:
            print(f"  ❌ {expected} (missing)")
            all_correct = False
    
    # Check for unexpected moves (diagonals or multi-tile)
    unexpected_moves = [move for move in moves if move not in expected_moves]
    if unexpected_moves:
        print(f"  ⚠️  Unexpected moves: {unexpected_moves}")
        all_correct = False
    
    if all_correct and len(moves) == 4:
        print(f"✅ Perfect orthogonal movement - 4 adjacent positions!")
    else:
        print(f"❌ Movement issues detected")
    
    # Test 2: Blocked Movement
    print(f"\n📋 TEST 2: Blocked Movement")
    print("-" * 27)
    
    game2 = Game()
    cleric2 = Cleric(1)
    blocker1 = Warrior(2)  # Block north
    blocker2 = Warrior(2)  # Block south
    
    # Position cleric and blockers
    cleric2.position = (4, 4)
    blocker1.position = (3, 4)  # 1 tile north
    blocker2.position = (5, 4)  # 1 tile south
    
    # Set up board
    cleric2.board = game2.board
    game2.board.units = {
        (4, 4): cleric2,
        (3, 4): blocker1,
        (5, 4): blocker2
    }
    
    print(f"Setup:")
    print(f"  Cleric at {cleric2.position}")
    print(f"  Blocker (North) at {blocker1.position}")
    print(f"  Blocker (South) at {blocker2.position}")
    
    # Get movement options
    moves2 = cleric2.get_valid_moves(game2.board)
    print(f"\nAvailable moves: {moves2}")
    
    # Should only have East and West available
    expected_unblocked = [
        (4, 3),  # West (unblocked)
        (4, 5)   # East (unblocked)
    ]
    
    print(f"\n🔍 Checking unblocked moves:")
    for expected in expected_unblocked:
        if expected in moves2:
            print(f"  ✅ {expected} (unblocked)")
        else:
            print(f"  ❌ {expected} (should be available)")
    
    # Check that blocked positions are not available
    blocked_positions = [(3, 4), (5, 4)]
    for blocked in blocked_positions:
        if blocked not in moves2:
            print(f"  ✅ {blocked} correctly blocked")
        else:
            print(f"  ❌ {blocked} should be blocked")
    
    if len(moves2) == 2:
        print(f"✅ Correct blocking behavior: {len(moves2)} moves available")
    else:
        print(f"❌ Incorrect blocking: {len(moves2)} moves (should be 2)")
    
    # Test 3: Board Edge Behavior
    print(f"\n📋 TEST 3: Board Edge Behavior")
    print("-" * 31)
    
    game3 = Game()
    cleric3 = Cleric(1)
    cleric3.position = (0, 0)  # Top-left corner
    cleric3.board = game3.board
    game3.board.units = {(0, 0): cleric3}
    
    print(f"Cleric at corner position: {cleric3.position}")
    
    # Get movement options
    moves3 = cleric3.get_valid_moves(game3.board)
    print(f"Available moves: {moves3}")
    
    # From (0,0), only South (1,0) and East (0,1) should be available
    expected_corner_moves = [
        (1, 0),  # South (down 1)
        (0, 1)   # East (right 1)
    ]
    
    print(f"\n🔍 Checking corner moves:")
    for expected in expected_corner_moves:
        if expected in moves3:
            print(f"  ✅ {expected} (valid corner move)")
        else:
            print(f"  ❌ {expected} (missing valid corner move)")
    
    # Check no off-board moves
    off_board_moves = [move for move in moves3 if move[0] < 0 or move[1] < 0 or move[0] >= 9 or move[1] >= 9]
    if not off_board_moves:
        print(f"  ✅ No off-board moves detected")
    else:
        print(f"  ❌ Off-board moves found: {off_board_moves}")
    
    if len(moves3) == 2:
        print(f"✅ Correct corner behavior: {len(moves3)} moves available")
    else:
        print(f"❌ Incorrect corner behavior: {len(moves3)} moves (should be 2)")
    
    # Test 4: No Diagonal Movement
    print(f"\n📋 TEST 4: No Diagonal Movement")
    print("-" * 31)
    
    game4 = Game()
    cleric4 = Cleric(1)
    cleric4.position = (4, 4)  # Center position
    cleric4.board = game4.board
    game4.board.units = {(4, 4): cleric4}
    
    # Get movement options
    moves4 = cleric4.get_valid_moves(game4.board)
    
    # Check that no diagonal moves are present
    diagonal_positions = [
        (3, 3), (3, 5),  # NW, NE
        (5, 3), (5, 5)   # SW, SE
    ]
    
    print(f"Checking for diagonal moves:")
    diagonal_found = False
    for diagonal in diagonal_positions:
        if diagonal in moves4:
            print(f"  ❌ {diagonal} (diagonal move found - should not exist)")
            diagonal_found = True
        else:
            print(f"  ✅ {diagonal} (correctly excluded)")
    
    if not diagonal_found:
        print(f"✅ No diagonal movement: All diagonals correctly excluded")
    else:
        print(f"❌ Diagonal movement detected: Should be orthogonal only")
    
    print(f"\n" + "=" * 32)
    print("🎯 CLERIC MOVEMENT SUMMARY")
    print("-" * 25)
    print("✅ Distance: Exactly 1 tile")
    print("✅ Directions: Orthogonal only (N,S,E,W)")
    print("✅ Pattern: 4 adjacent positions maximum")
    print("✅ Blocking: Respects occupied tiles")
    print("✅ Edges: Handles board boundaries")
    print("✅ No Diagonals: Pure orthogonal movement")
    
    print("\n⛪ Cleric movement is now simple and precise!")

if __name__ == "__main__":
    test_cleric_movement()

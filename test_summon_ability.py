#!/usr/bin/env python3
"""
Test script for the new Summon ability across all classes.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from units.hunter import Hunter
from units.warrior import Warrior
from units.rogue import Rogue
from units.mage import Mage
from units.cleric import Cleric
from units.pawn import Pawn
from game_board import GameBoard
from game_state import Game
import pygame

def test_summon_ability():
    """Test the Summon ability for each class."""
    print("Testing Summon Ability Implementation")
    print("=" * 50)
    
    # Initialize pygame (required for unit creation)
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    
    # Create a game board
    board = GameBoard(screen)
    
    # Create a simple game state
    game = Game()
    game.board = board
    game.turn_count = 1
    
    # Test each class
    classes_to_test = [
        ("<PERSON>", <PERSON>),
        ("Warrior", Warrior), 
        ("Rogue", Rogue),
        ("Ma<PERSON>", Mage),
        ("<PERSON><PERSON><PERSON>", Cleric)
    ]
    
    for class_name, UnitClass in classes_to_test:
        print(f"\nTesting {class_name}:")
        print("-" * 20)
        
        # Create unit
        unit = UnitClass(player_id=1)
        unit.position = (4, 4)  # Center of board
        board.add_unit(unit, 4, 4)
        
        # Check if Summon ability exists
        summon_ability = None
        summon_idx = None
        for i, ability in enumerate(unit.abilities):
            if ability.name == "Summon":
                summon_ability = ability
                summon_idx = i
                break
        
        if summon_ability:
            print(f"✓ {class_name} has Summon ability (AP cost: {summon_ability.ap_cost}, Cooldown: {summon_ability.cooldown})")
            
            # Test targeting
            targets = unit.get_ability_targets(summon_idx, board)
            print(f"✓ Valid summon targets: {len(targets)} positions")
            print(f"  Sample targets: {targets[:5] if len(targets) > 5 else targets}")
            
            # Test execution if there are valid targets
            if targets:
                target_pos = targets[0]
                print(f"  Testing summon at position {target_pos}")
                
                # Check initial AP
                initial_ap = unit.current_ap
                print(f"  Initial AP: {initial_ap}")
                
                # Execute summon
                success = unit.use_ability(summon_idx, target_pos, game)
                
                if success:
                    print(f"  ✓ Summon executed successfully!")
                    print(f"  ✓ AP after summon: {unit.current_ap} (used {initial_ap - unit.current_ap} AP)")
                    
                    # Check if pawn was created
                    summoned_unit = board.units.get(target_pos)
                    if summoned_unit and summoned_unit.name == "Pawn":
                        print(f"  ✓ Pawn successfully summoned at {target_pos}")
                        print(f"  ✓ Pawn belongs to player {summoned_unit.player_id}")
                        print(f"  ✓ Pawn health: {summoned_unit.health}/{summoned_unit.max_health}")
                    else:
                        print(f"  ✗ No pawn found at target position {target_pos}")
                else:
                    print(f"  ✗ Summon execution failed")
            else:
                print(f"  ⚠ No valid targets available for summoning")
        else:
            print(f"✗ {class_name} does not have Summon ability")
        
        # Clean up for next test
        board.units.clear()
        print()
    
    print("=" * 50)
    print("Summon ability test completed!")
    pygame.quit()

if __name__ == "__main__":
    test_summon_ability()

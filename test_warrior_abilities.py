#!/usr/bin/env python3
"""
Test Warrior abilities to identify issues and verify intended functionality.
"""

import pygame
import sys

# Initialize pygame to avoid import issues
pygame.init()

def test_warrior_abilities():
    """Test all Warrior abilities to identify issues"""
    print("🗡️ TESTING WARRIOR ABILITIES")
    print("=" * 50)
    
    try:
        from game_state import Game
        from units.warrior import Warrior
        from units.rogue import Rogue
        
        # Create game instance
        game = Game()
        warrior = Warrior(1)
        target1 = Rogue(2)
        target2 = Rogue(2)
        
        # Set up board positions
        game.board.add_unit(warrior, 4, 4)
        game.board.add_unit(target1, 4, 5)  # Adjacent for basic attacks
        game.board.add_unit(target2, 5, 5)  # Diagonal for cleave test
        
        # Set up game state
        game.current_player = 1
        game.current_player_ap = 10
        
        print(f"Warrior at {warrior.position}")
        print(f"Target1 at {target1.position}")
        print(f"Target2 at {target2.position}")
        print(f"Initial AP: {game.current_player_ap}")
        print()
        
        # Test 1: Cleave Attack
        print("🔥 TEST 1: Cleave Attack")
        print("-" * 30)
        initial_hp1 = target1.health
        initial_hp2 = target2.health
        
        print(f"Target1 HP before: {initial_hp1}")
        print(f"Target2 HP before: {initial_hp2}")
        
        success = warrior.use_ability(2, target1.position, game)  # Cleave Attack
        
        print(f"Cleave Attack success: {success}")
        print(f"Target1 HP after: {target1.health}")
        print(f"Target2 HP after: {target2.health}")
        print(f"AP after: {game.current_player_ap}")
        print()
        
        # Test 2: Shield Bash
        print("🛡️ TEST 2: Shield Bash")
        print("-" * 30)
        game.current_player_ap = 10  # Reset AP
        warrior.has_acted_this_turn = False  # Reset action status for testing
        game.units_acted_this_turn.discard(warrior)
        
        initial_hp = target1.health
        print(f"Target HP before: {initial_hp}")
        print(f"Target stunned before: {target1.has_status('Stunned')}")
        
        success = warrior.use_ability(3, target1.position, game)  # Shield Bash
        
        print(f"Shield Bash success: {success}")
        print(f"Target HP after: {target1.health}")
        print(f"Target stunned after: {target1.has_status('Stunned')}")
        print(f"AP after: {game.current_player_ap}")
        print()
        
        # Test 3: Charge
        print("⚡ TEST 3: Charge")
        print("-" * 30)
        game.current_player_ap = 10  # Reset AP
        warrior.has_acted_this_turn = False  # Reset action status for testing
        game.units_acted_this_turn.discard(warrior)
        
        # Move target further away for charge test
        game.board.move_unit(target1.position, (4, 7))
        target1.position = (4, 7)
        
        print(f"Warrior at: {warrior.position}")
        print(f"Target at: {target1.position}")
        print(f"Target HP before: {target1.health}")
        
        success = warrior.use_ability(4, target1.position, game)  # Charge
        
        print(f"Charge success: {success}")
        print(f"Warrior position after: {warrior.position}")
        print(f"Target HP after: {target1.health}")
        print(f"AP after: {game.current_player_ap}")
        print()
        
        # Test 4: Defensive Stance
        print("🛡️ TEST 4: Defensive Stance")
        print("-" * 30)
        game.current_player_ap = 10  # Reset AP
        warrior.has_acted_this_turn = False  # Reset action status for testing
        game.units_acted_this_turn.discard(warrior)
        
        print(f"Defensive stance before: {warrior.defensive_stance_active}")
        
        success = warrior.use_ability(5, warrior.position, game)  # Defensive Stance
        
        print(f"Defensive Stance success: {success}")
        print(f"Defensive stance after: {warrior.defensive_stance_active}")
        print(f"AP after: {game.current_player_ap}")
        print()
        
        # Test 5: Riposte
        print("⚔️ TEST 5: Riposte")
        print("-" * 30)
        game.current_player_ap = 10  # Reset AP
        warrior.has_acted_this_turn = False  # Reset action status for testing
        game.units_acted_this_turn.discard(warrior)
        
        print(f"Riposte active before: {warrior.riposte_active}")
        
        success = warrior.use_ability(6, warrior.position, game)  # Riposte
        
        print(f"Riposte success: {success}")
        print(f"Riposte active after: {warrior.riposte_active}")
        print(f"AP after: {game.current_player_ap}")
        print()
        
        # Test 6: Test Riposte trigger
        print("⚔️ TEST 6: Riposte Trigger")
        print("-" * 30)
        
        # Move target adjacent to warrior
        game.board.move_unit(target1.position, (4, 5))
        target1.position = (4, 5)
        
        warrior_hp_before = warrior.health
        target_hp_before = target1.health
        
        print(f"Warrior HP before: {warrior_hp_before}")
        print(f"Target HP before: {target_hp_before}")
        print(f"Riposte active: {warrior.riposte_active}")
        
        # Target attacks warrior to trigger riposte
        target1.attack(warrior.position)
        
        print(f"Warrior HP after: {warrior.health}")
        print(f"Target HP after: {target1.health}")
        print(f"Riposte active after: {warrior.riposte_active}")
        print()
        
        print("✅ All Warrior ability tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Warrior ability test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_warrior_abilities()
    sys.exit(0 if success else 1)

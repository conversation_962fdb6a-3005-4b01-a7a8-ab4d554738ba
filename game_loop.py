import pygame
import game_setup
import game_logic
import game_ui # game_ui is now called by game_draw
import game_visuals # For process_mouse_motion
import game_constants as const # Import constants
from game_state import Game # DARK_GRAY removed
import game_draw # Import the new game_draw module

# FPS = 60 # FPS is now in const

def run_game_loop(game_instance: Game):
    """Runs the main game loop."""
    running = True
    while running:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    # In playing state, ESC first tries to cancel selection, then exits to menu
                    if game_instance.state == const.STATE_PLAYING and (game_instance.selected_unit or game_instance.selected_ability):
                        game_logic.cancel_selection(game_instance) # game_logic.cancel_selection needs game_instance
                    else:
                        running = False # Signal to exit loop (back to main menu)

                # BALANCE SLIDER HOTKEYS
                elif event.key == pygame.K_F1:
                    # Toggle balance slider UI
                    game_instance.show_balance_ui = not game_instance.show_balance_ui
                    print(f"Balance UI: {'ON' if game_instance.show_balance_ui else 'OFF'}")

                elif event.key == pygame.K_F2:
                    # Load conservative preset
                    game_instance.balance_sliders.load_balance_preset("conservative")
                    game_instance.apply_balance_changes()
                    print("Loaded 'Conservative' balance preset")

                elif event.key == pygame.K_F3:
                    # Load balanced preset
                    game_instance.balance_sliders.load_balance_preset("balanced")
                    game_instance.apply_balance_changes()
                    print("Loaded 'Balanced' balance preset")

                elif event.key == pygame.K_F4:
                    # Load aggressive preset
                    game_instance.balance_sliders.load_balance_preset("aggressive")
                    game_instance.apply_balance_changes()
                    print("Loaded 'Aggressive' balance preset")

                elif event.key == pygame.K_F5:
                    # Apply current slider values
                    changes = game_instance.apply_balance_changes()
                    print(f"Applied balance changes: {changes}")

            elif event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1:  # Left click
                    # Check balance slider UI first (if visible)
                    if game_instance.show_balance_ui:
                        if game_instance.balance_sliders.handle_click(event.pos, (50, 100)):
                            # Slider was clicked, apply changes immediately
                            changes = game_instance.apply_balance_changes()
                            if changes:
                                print(f"Balance updated: {changes}")
                            continue  # Don't process other clicks

                    if game_instance.state == const.STATE_SETUP:
                        game_setup.process_setup_click(game_instance, event.pos)
                    elif game_instance.state == const.STATE_PLAYING:
                        # process_game_click returns False if it wants to signal exit (e.g. Escape button clicked)
                        if not game_logic.process_game_click(game_instance, event.pos):
                            running = False
                    elif game_instance.state == const.STATE_GAME_OVER:
                        running = False # Click to exit game over screen
                elif event.button == 3:  # Right click
                    if game_instance.state == const.STATE_PLAYING:
                        game_logic.cancel_selection(game_instance)
            
            elif event.type == pygame.MOUSEMOTION:
                # Pass the game_instance to process_mouse_motion as it might need access to game.hover_pos, etc.
                game_visuals.process_mouse_motion(game_instance, event.pos)
        
        game_draw.draw_game(game_instance)
        
        game_instance.clock.tick(const.FPS) # Use const.FPS

    # Loop finished, typically means returning to main menu or quitting app.
    # Pygame.quit() should be handled by the main application entry point. 
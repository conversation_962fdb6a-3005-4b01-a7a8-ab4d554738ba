#!/usr/bin/env python3

import pygame
import sys
import os

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from game_state import Game
from units.warrior import Warrior
from units.rogue import Rogue

def test_current_state():
    """Test the current state to see what's broken"""
    print("🔍 TESTING CURRENT GAME STATE")
    print("=" * 40)
    
    # Initialize pygame
    pygame.init()
    
    # Create a game instance
    game = Game()
    
    # Create units
    warrior = Warrior(player_id=1)
    target = Rogue(player_id=2)
    
    game.board.add_unit(warrior, 4, 4)
    game.board.add_unit(target, 4, 5)  # Adjacent for attack
    
    # Set up game state
    game.current_player = 1
    game.current_player_ap = 10
    game.units_acted_this_turn = set()
    
    print(f"Warrior at {warrior.position}")
    print(f"Target at {target.position}")
    print(f"Warrior HP: {warrior.health}/{warrior.max_health}")
    print(f"Target HP: {target.health}/{target.max_health}")
    print(f"Game AP: {game.current_player_ap}")
    
    # Test 1: Basic Attack
    print("\n📋 TEST 1: Warrior Basic Attack")
    attack_idx = 1  # Basic Attack
    
    success = warrior.use_ability(attack_idx, target.position, game)
    print(f"Attack success: {success}")
    print(f"Target HP after attack: {target.health}/{target.max_health}")
    print(f"Game AP after attack: {game.current_player_ap}")
    
    # Reset for next test
    target.health = target.max_health
    game.current_player_ap = 10
    game.units_acted_this_turn = set()
    warrior.has_acted_this_turn = False  # Reset warrior action flag
    
    # Test 2: Shield Bash
    print("\n📋 TEST 2: Warrior Shield Bash")
    shield_bash_idx = None
    for i, ability in enumerate(warrior.abilities):
        if ability.name == "Shield Bash":
            shield_bash_idx = i
            break
    
    if shield_bash_idx is None:
        print("❌ Warrior doesn't have Shield Bash ability!")
        return False
    
    print(f"✓ Warrior has Shield Bash ability (index {shield_bash_idx})")
    print(f"  AP cost: {warrior.abilities[shield_bash_idx].ap_cost}")
    
    success = warrior.use_ability(shield_bash_idx, target.position, game)
    print(f"Shield Bash success: {success}")
    print(f"Target HP after Shield Bash: {target.health}/{target.max_health}")
    print(f"Target status effects: {getattr(target, 'status_effects', 'None')}")
    print(f"Game AP after Shield Bash: {game.current_player_ap}")
    
    # Test 3: Check if stunned unit can move
    if hasattr(target, 'status_effects') and target.status_effects:
        print("\n📋 TEST 3: Stunned Unit Movement")
        print(f"Target status effects: {target.status_effects}")
        
        # Try to move the stunned unit
        game.current_player = 2  # Switch to target's turn
        game.current_player_ap = 10
        game.units_acted_this_turn = set()
        
        move_success = target.use_ability(0, (4, 6), game)  # Try to move
        print(f"Stunned unit move attempt: {move_success}")
        print(f"Target position after move attempt: {target.position}")
        
        if move_success:
            print("❌ STUNNED UNIT CAN MOVE - STATUS EFFECTS BROKEN!")
            return False
        else:
            print("✅ Stunned unit cannot move - status effects working")
    
    return True

if __name__ == "__main__":
    success = test_current_state()
    if success:
        print("\n🎉 CURRENT STATE WORKING!")
    else:
        print("\n❌ CURRENT STATE BROKEN!")

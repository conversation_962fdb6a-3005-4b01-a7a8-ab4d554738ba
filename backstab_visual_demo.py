#!/usr/bin/env python3
"""
Visual demonstration of the Backstab mechanic
Shows exactly how the L-shaped movement and attack works
"""

def show_backstab_demo():
    print("🗡️ BACKSTAB MECHANIC VISUAL DEMO 🗡️")
    print("=" * 40)
    
    print("\n📋 How Backstab Works:")
    print("1. Target must be diagonally adjacent to <PERSON>")
    print("2. Rogue moves in L-shape (knight move) to get adjacent to target")
    print("3. Rogue attacks target from new position (simulating 'from behind')")
    print("4. Deals base damage + 1 backstab bonus")
    
    print("\n🎯 Example Scenario:")
    print("Initial Position:")
    print("┌─┬─┬─┬─┬─┐")
    print("│ │ │ │ │ │")
    print("├─┼─┼─┼─┼─┤")
    print("│ │ │W│ │ │  W = Warrior (target)")
    print("├─┼─┼─┼─┼─┤")
    print("│ │ │ │R│ │  R = Rogue")
    print("├─┼─┼─┼─┼─┤")
    print("│ │ │ │ │ │")
    print("├─┼─┼─┼─┼─┤")
    print("│ │ │ │ │ │")
    print("└─┴─┴─┴─┴─┘")
    
    print("\nStep 1: <PERSON> identifies diagonal target")
    print("- Warrior is at (2,2), diagonally adjacent to <PERSON> at (3,3)")
    print("- Backstab can target diagonally adjacent units")
    
    print("\nStep 2: Rogue finds knight move path")
    print("- <PERSON> needs to move in L-shape to get adjacent to Warrior")
    print("- Possible knight moves from (3,3): (1,2), (1,4), (2,1), (2,5), (4,1), (4,5), (5,2), (5,4)")
    print("- Knight move (1,2) puts Rogue adjacent to Warrior at (2,2)")
    
    print("\nStep 3: Execute backstab sequence")
    print("After Knight Move:")
    print("┌─┬─┬─┬─┬─┐")
    print("│ │R│ │ │ │  R = Rogue (new position)")
    print("├─┼─┼─┼─┼─┤")
    print("│ │ │W│ │ │  W = Warrior (target)")
    print("├─┼─┼─┼─┼─┤")
    print("│ │ │ │ │ │")
    print("├─┼─┼─┼─┼─┤")
    print("│ │ │ │ │ │")
    print("├─┼─┼─┼─┼─┤")
    print("│ │ │ │ │ │")
    print("└─┴─┴─┴─┴─┘")
    
    print("\nStep 4: Attack from behind")
    print("- Rogue is now adjacent to Warrior")
    print("- Attacks with backstab damage (base + 1 bonus)")
    print("- Simulates attacking from behind without facing mechanics")
    
    print("\n🎮 In Your Game:")
    print("1. Select Rogue")
    print("2. Click Backstab ability")
    print("3. Only diagonally adjacent units with valid knight move paths will be highlighted")
    print("4. Click target to execute the backstab sequence")
    print("5. Watch Rogue move in L-shape and attack!")
    
    print("\n⚔️ Tactical Considerations:")
    print("✅ Can backstab allies (friendly fire)")
    print("✅ Requires clear knight move path")
    print("✅ Deals extra damage (+1 backstab bonus)")
    print("✅ Repositions Rogue tactically")
    print("❌ Limited to diagonally adjacent targets")
    print("❌ Requires free space for knight move")
    
    print("\n🗡️ Perfect for hit-and-run tactics!")

if __name__ == "__main__":
    show_backstab_demo()

# 🛠️ Development Guide - Tactical PvP Strategy Game

## 🎯 **Context Engineering Principles**

This project follows **Context Engineering** rather than "vibe coding" for maintainable, scalable development.

### **Core Principles**
1. **Documentation-Driven Development** - Write docs first, code second
2. **Systematic Architecture** - Consistent patterns across codebase
3. **Test-Driven Features** - Every feature has tests
4. **Configuration-Driven Design** - Avoid hardcoded values
5. **Modular Components** - Clear separation of concerns

---

## 📁 **Project Structure**

```
📦 Tactical PvP Strategy Game
├── 🎮 Core Game Files
│   ├── game.py              # Main entry point
│   ├── game_state.py        # Game state management
│   ├── game_loop.py         # Main game loop
│   ├── game_logic.py        # Game rules and logic
│   └── game_ui.py           # User interface
│
├── 🏗️ System Components
│   ├── game_config.py       # Configuration settings
│   ├── game_settings.py     # Player preferences
│   ├── passive_system.py    # Passive abilities
│   ├── status_effects.py    # Status effect system
│   └── units_core.py        # Base unit class
│
├── 👥 Unit Classes
│   └── units/
│       ├── hunter.py        # Hunter class (8 abilities)
│       ├── warrior.py       # Warrior class
│       ├── rogue.py         # Rogue class (knight movement)
│       ├── mage.py          # Mage class
│       ├── cleric.py        # Cleric class
│       ├── king.py          # King class
│       └── pawn.py          # Pawn class
│
├── 🖥️ User Interface
│   └── menu_screens/
│       ├── ability_selection_menu.py
│       ├── config_menu.py
│       └── options_menu.py
│
└── 🧪 Testing
    └── tests/
        └── test_suite.py    # Comprehensive test suite
```

---

## 🔧 **Adding New Features**

### **1. Adding a New Unit Class**

```python
# Step 1: Create new unit file (units/barbarian.py)
from units_core import Unit
from game_config import GAME_CONFIG

class Barbarian(Unit):
    def __init__(self, player_id):
        # Define abilities
        abilities = [
            MoveAbility(),
            AttackAbility(),
            RageAbility(),      # New ability
            BerserkerAbility()  # New ability
        ]
        
        # Initialize with config
        super().__init__(
            name="Barbarian",
            health=GAME_CONFIG.get("barbarian_config", {}).get("health", 8),
            max_ap=GAME_CONFIG.get("barbarian_config", {}).get("max_ap", 6),
            abilities=abilities,
            player_id=player_id
        )
    
    def get_valid_moves(self, board):
        # Define movement pattern
        pass

# Step 2: Add to game_config.py
"barbarian_config": {
    "health": 8,
    "max_ap": 6,
    "movement_range": 2,
    "rage_ap_cost": 3
}

# Step 3: Add to game_settings.py
DEFAULT_SELECTED_PASSIVES = {
    "Barbarian": ["damage_reduction", "berserker_rage"]
}

# Step 4: Write tests
def test_barbarian_creation(self):
    barbarian = Barbarian(1)
    self.assertEqual(barbarian.name, "Barbarian")
    self.assertGreater(barbarian.health, 0)
```

### **2. Adding a New Ability**

```python
# Step 1: Define ability class
class RageAbility(Ability):
    def __init__(self):
        super().__init__(
            name="Rage",
            ap_cost=GAME_CONFIG.get("barbarian_config", {}).get("rage_ap_cost", 3),
            description="Increase damage for 3 turns",
            cooldown=5
        )

# Step 2: Add targeting logic
def get_ability_targets(self, ability_index, board):
    if self.abilities[ability_index].name == "Rage":
        return [self.position]  # Self-target
    return []

# Step 3: Add execution logic
def use_ability(self, ability_index, target_pos, game):
    if self.abilities[ability_index].name == "Rage":
        # Apply rage status effect
        apply_rage(self, duration=3)
        return True
    return False

# Step 4: Write tests
def test_rage_ability(self):
    barbarian = Barbarian(1)
    result = barbarian.use_ability(2, barbarian.position, game)
    self.assertTrue(result)
```

### **3. Adding a New Passive**

```python
# Step 1: Add to PassiveType enum
class PassiveType(Enum):
    BERSERKER_RAGE = "berserker_rage"

# Step 2: Create effect class
class BerserkerRageEffect(PassiveEffect):
    def apply_effect(self, context):
        if context.get("action") == "deal_damage":
            if self.owner.health < self.owner.max_health // 2:
                damage = context.get("damage", 0)
                return damage * 2  # Double damage when low HP
        return context.get("damage", 0)

# Step 3: Add to PASSIVE_ABILITIES
PASSIVE_ABILITIES = {
    PassiveType.BERSERKER_RAGE: PassiveAbility(
        PassiveType.BERSERKER_RAGE,
        "Berserker Rage",
        "Deal double damage when below 50% health"
    )
}

# Step 4: Write tests
def test_berserker_rage(self):
    unit = Barbarian(1)
    unit.health = 2  # Low health
    passive = PASSIVE_ABILITIES[PassiveType.BERSERKER_RAGE]
    unit.passive_manager.add_passive(passive)
    # Test double damage
```

---

## 🧪 **Testing Guidelines**

### **Test Categories**
1. **Unit Tests** - Test individual functions
2. **Integration Tests** - Test feature interactions
3. **Regression Tests** - Prevent breaking changes

### **Testing Checklist**
- [ ] Unit creation and basic properties
- [ ] Movement mechanics
- [ ] Ability functionality
- [ ] Passive effects
- [ ] Configuration integration
- [ ] Error handling

### **Running Tests**
```bash
# Run all tests
python tests/test_suite.py

# Run specific test class
python -m unittest tests.test_suite.TestUnitCore

# Run with verbose output
python tests/test_suite.py -v
```

---

## 🔄 **Refactoring Guidelines**

### **When to Refactor**
- Functions longer than 50 lines
- Code duplication (DRY principle)
- Deep nesting (>3 levels)
- Unclear naming
- Hard to test code

### **Refactoring Process**
1. **Write tests first** - Ensure behavior is preserved
2. **Small incremental changes** - Don't refactor everything at once
3. **Run tests after each change** - Catch regressions early
4. **Update documentation** - Keep docs in sync

### **Example Refactoring**
```python
# BEFORE - Large function doing multiple things
def process_unit_setup(self, unit):
    # 50 lines of mixed logic
    pass

# AFTER - Split into focused functions
def process_unit_setup(self, unit):
    """Main setup process for units."""
    self._apply_selected_abilities(unit)
    self._apply_selected_passives(unit)
    self._validate_unit_configuration(unit)

def _apply_selected_abilities(self, unit):
    """Apply player-selected abilities to unit."""
    pass

def _apply_selected_passives(self, unit):
    """Apply player-selected passives to unit."""
    pass
```

---

## 📊 **Performance Guidelines**

### **Optimization Priorities**
1. **Profile first** - Don't guess where bottlenecks are
2. **Optimize hot paths** - Game loop, rendering, pathfinding
3. **Cache expensive calculations** - Movement ranges, targeting
4. **Lazy loading** - Load resources when needed

### **Performance Monitoring**
```python
# Add timing to critical functions
import time

def expensive_function():
    start_time = time.time()
    # Function logic
    end_time = time.time()
    if end_time - start_time > 0.1:  # 100ms threshold
        print(f"Slow function: {end_time - start_time:.3f}s")
```

---

## 📝 **Documentation Standards**

### **Function Documentation**
```python
def use_ability(self, ability_index, target_pos, game):
    """
    Execute an ability on a target position.
    
    Args:
        ability_index (int): Index of ability in self.abilities list
        target_pos (tuple): (row, col) coordinates of target
        game (Game): Current game state object
        
    Returns:
        bool: True if ability was successfully used, False otherwise
        
    Raises:
        ValueError: If ability_index is out of range
        TypeError: If target_pos is not a valid coordinate tuple
        
    Example:
        >>> hunter = Hunter(1)
        >>> result = hunter.use_ability(1, (3, 4), game)
        >>> print(result)
        True
    """
```

### **Class Documentation**
```python
class Hunter(Unit):
    """
    Hunter unit class with ranged abilities and diagonal movement.
    
    Hunters specialize in ranged combat with various arrow-based abilities.
    They move diagonally and excel at controlling battlefield positioning.
    
    Attributes:
        abilities (list): 8 abilities including Move, Basic Attack, and 6 specials
        movement_pattern (str): "diagonal" - moves only diagonally
        range (int): 7 squares for most abilities
        
    Default Passives:
        - Extra Damage: +1 damage to all attacks
        
    Example:
        >>> hunter = Hunter(player_id=1)
        >>> hunter.position = (4, 4)
        >>> moves = hunter.get_valid_moves(board)
        >>> len(moves) > 0
        True
    """
```

---

## 🎯 **Best Practices Summary**

### **Code Quality**
- ✅ Use descriptive variable names
- ✅ Keep functions focused and small
- ✅ Add comprehensive docstrings
- ✅ Handle errors gracefully
- ✅ Follow consistent naming conventions

### **Architecture**
- ✅ Maintain clear module boundaries
- ✅ Use dependency injection
- ✅ Prefer composition over inheritance
- ✅ Keep configuration external
- ✅ Design for testability

### **Development Workflow**
1. **Plan** - Document what you're building
2. **Test** - Write tests for new functionality
3. **Implement** - Write the minimal code to pass tests
4. **Refactor** - Improve code quality
5. **Document** - Update documentation
6. **Review** - Check for issues and improvements

**Following these guidelines ensures maintainable, scalable, and robust code!** 🚀

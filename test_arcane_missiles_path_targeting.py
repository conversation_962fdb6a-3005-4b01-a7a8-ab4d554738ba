#!/usr/bin/env python3
"""
Test Arcane Missiles path targeting fix:
- Should work when clicking on any tile in the path
- Should work when clicking on targets
- Should work when clicking on directional tiles
"""

import pygame
from game_state import Game
from units.mage import Mage
from units.warrior import Warrior

def test_arcane_missiles_path_targeting():
    """Test Arcane Missiles with different click positions"""
    pygame.init()
    
    print("🎯 TESTING ARCANE MISSILES PATH TARGETING 🎯")
    print("=" * 48)
    
    # Test 1: Click on directional tile (adjacent to mage)
    print("📋 TEST 1: Click on Directional Tile")
    print("-" * 35)
    
    game = Game()
    mage = Mage(1)
    target = Warrior(2)
    
    # Position units
    mage.position = (4, 4)
    target.position = (2, 4)  # 2 tiles north
    
    # Set up board
    mage.board = game.board
    game.board.units = {
        (4, 4): mage,
        (2, 4): target
    }
    
    print(f"Setup:")
    print(f"  Mage at {mage.position}")
    print(f"  Target at {target.position}")
    print(f"  Target HP before: {target.health}")
    
    # Find Arcane Missile ability
    arcane_ability_idx = None
    for i, ability in enumerate(mage.abilities):
        if ability.name == "Arcane Missile":
            arcane_ability_idx = i
            break
    
    # Click on directional tile (1 tile north of mage)
    directional_click = (3, 4)  # 1 tile north
    print(f"\n🎯 Clicking on directional tile: {directional_click}")
    result1 = mage.use_ability(arcane_ability_idx, directional_click, game)
    
    print(f"\nResults:")
    print(f"  Ability result: {result1}")
    print(f"  Target HP after: {target.health}")
    damage1 = 7 - target.health
    
    if damage1 > 0:
        print(f"✅ Directional click works: {damage1} damage dealt")
    else:
        print(f"❌ Directional click failed: No damage dealt")
    
    # Test 2: Click on target tile (far from mage)
    print(f"\n📋 TEST 2: Click on Target Tile")
    print("-" * 30)
    
    game2 = Game()
    mage2 = Mage(1)
    target2 = Warrior(2)
    
    # Position units
    mage2.position = (4, 4)
    target2.position = (1, 4)  # 3 tiles north
    
    # Set up board
    mage2.board = game2.board
    game2.board.units = {
        (4, 4): mage2,
        (1, 4): target2
    }
    
    print(f"Setup:")
    print(f"  Mage at {mage2.position}")
    print(f"  Target at {target2.position}")
    print(f"  Target HP before: {target2.health}")
    
    # Click directly on target tile (3 tiles north of mage)
    target_click = (1, 4)  # 3 tiles north
    print(f"\n🎯 Clicking directly on target: {target_click}")
    result2 = mage2.use_ability(arcane_ability_idx, target_click, game2)
    
    print(f"\nResults:")
    print(f"  Ability result: {result2}")
    print(f"  Target HP after: {target2.health}")
    damage2 = 7 - target2.health
    
    if damage2 > 0:
        print(f"✅ Target click works: {damage2} damage dealt")
    else:
        print(f"❌ Target click failed: No damage dealt")
    
    # Test 3: Click on middle path tile
    print(f"\n📋 TEST 3: Click on Middle Path Tile")
    print("-" * 35)
    
    game3 = Game()
    mage3 = Mage(1)
    target3 = Warrior(2)
    
    # Position units
    mage3.position = (4, 4)
    target3.position = (1, 4)  # 3 tiles north
    
    # Set up board
    mage3.board = game3.board
    game3.board.units = {
        (4, 4): mage3,
        (1, 4): target3
    }
    
    print(f"Setup:")
    print(f"  Mage at {mage3.position}")
    print(f"  Target at {target3.position}")
    print(f"  Target HP before: {target3.health}")
    
    # Click on middle path tile (2 tiles north of mage)
    middle_click = (2, 4)  # 2 tiles north
    print(f"\n🎯 Clicking on middle path tile: {middle_click}")
    result3 = mage3.use_ability(arcane_ability_idx, middle_click, game3)
    
    print(f"\nResults:")
    print(f"  Ability result: {result3}")
    print(f"  Target HP after: {target3.health}")
    damage3 = 7 - target3.health
    
    if damage3 > 0:
        print(f"✅ Middle path click works: {damage3} damage dealt")
    else:
        print(f"❌ Middle path click failed: No damage dealt")
    
    # Test 4: Click on diagonal (should fail)
    print(f"\n📋 TEST 4: Click on Diagonal (Should Fail)")
    print("-" * 40)
    
    game4 = Game()
    mage4 = Mage(1)
    target4 = Warrior(2)
    
    # Position units
    mage4.position = (4, 4)
    target4.position = (2, 2)  # Diagonal
    
    # Set up board
    mage4.board = game4.board
    game4.board.units = {
        (4, 4): mage4,
        (2, 2): target4
    }
    
    print(f"Setup:")
    print(f"  Mage at {mage4.position}")
    print(f"  Target at {target4.position} (diagonal)")
    print(f"  Target HP before: {target4.health}")
    
    # Click on diagonal tile (should fail)
    diagonal_click = (2, 2)  # Diagonal
    print(f"\n🎯 Clicking on diagonal tile: {diagonal_click}")
    result4 = mage4.use_ability(arcane_ability_idx, diagonal_click, game4)
    
    print(f"\nResults:")
    print(f"  Ability result: {result4}")
    print(f"  Target HP after: {target4.health}")
    damage4 = 7 - target4.health
    
    if result4 == False and damage4 == 0:
        print(f"✅ Diagonal click correctly failed: No damage dealt")
    else:
        print(f"❌ Diagonal click should have failed")
    
    print(f"\n" + "=" * 48)
    print("🎯 PATH TARGETING FIX SUMMARY")
    print("-" * 30)
    print("✅ Directional tiles: Should work")
    print("✅ Target tiles: Should work")
    print("✅ Middle path tiles: Should work")
    print("✅ Diagonal tiles: Should fail")
    print("\n🚀 You can now click anywhere in the path!")
    print("   The direction will be calculated automatically.")

if __name__ == "__main__":
    test_arcane_missiles_path_targeting()

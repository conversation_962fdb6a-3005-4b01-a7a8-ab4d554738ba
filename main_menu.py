import pygame
import sys
from pygame import gfxdraw
from game_state import Game # Import Game from game_state
from game_loop import run_game_loop # Import the game loop runner
from game_units import <PERSON>, <PERSON>, <PERSON>, Pa<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> # Removed GAME_CONFIG
from game_config import <PERSON>ME_CONFIG # Added import for GAME_CONFIG
from game_settings import SELECTED_ABILITIES  # Import from game_settings instead of defining here
from menu_screens.button import Button # Import Button
from menu_screens.settings_menu import Settings # Import Settings menu
from menu_screens.config_menu import ConfigMenu # Import ConfigMenu
from menu_screens.ability_selection_menu import AbilitySelectionMenu # Import AbilitySelectionMenu
from menu_screens.options_menu import OptionsMenu # Import OptionsMenu
from menu_screens.credits_screen import CreditsScreen # Import CreditsScreen
import game_constants as const # Import constants

# Constants previously defined here are now in game_constants.py or accessible via const
# WINDOW_WIDTH = 1280
# WINDOW_HEIGHT = 720
# FPS = 60
BACKGROUND_IMAGE_PATH = "assets/main_menu_background.png.jpg" # Path to the background image

# Available resolutions
RESOLUTIONS = [
    (800, 600),
    (1024, 768),
    (1280, 720),
    (1366, 768),
    (1600, 900),
    (1920, 1080)
]

# Colors previously defined here are now in game_constants.py or menu_screens/button.py
# DARK_GRAY = (30, 30, 30)
# LIGHT_GRAY = (200, 200, 200) # Keep if used elsewhere in main_menu.py, or move if only for Button
# SELECTED_COLOR = (0, 200, 100)
# DISABLED_COLOR = (100, 100, 100)

class MainMenu:
    def __init__(self, screen, clock):
        self.screen = screen
        self.clock = clock
        self.running = True
        
        self.title_font = pygame.font.Font(const.FONT_DEFAULT_NAME, 90) # Use const.FONT_DEFAULT_NAME 
        self.button_font = pygame.font.Font(const.FONT_DEFAULT_NAME, 52) # Use const.FONT_DEFAULT_NAME

        # Preferences that Settings menu can change
        self.fullscreen_preference = False 
        self.current_resolution = (const.WINDOW_WIDTH, const.WINDOW_HEIGHT) # Use const for default
        
        self.background_image = None
        try:
            self.background_image = pygame.image.load(BACKGROUND_IMAGE_PATH)
            self.background_image = pygame.transform.scale(self.background_image, (const.WINDOW_WIDTH, const.WINDOW_HEIGHT)) # Use const
        except pygame.error as e:
            print(f"Error loading background image: {e}. Using default gradient.")
            self.background_image = None

        self._update_buttons() 

    def _update_buttons(self):
        # Uses global WINDOW_WIDTH, WINDOW_HEIGHT for initial setup
        # If resolution changes via Settings, these globals should ideally update too,
        # or screen.get_size() should be used more consistently.
        current_screen_width, current_screen_height = self.screen.get_size()

        button_width = 300
        button_height = 70
        spacing = 20
        total_height = (button_height + spacing) * 5 - spacing 
        start_y = (current_screen_height - total_height) // 2

        self.start_button = Button(current_screen_width//2 - button_width//2, start_y, button_width, button_height, "Start Game")
        self.options_button = Button(current_screen_width//2 - button_width//2, start_y + button_height + spacing, button_width, button_height, "Options")
        self.credits_button = Button(current_screen_width//2 - button_width//2, start_y + 2*(button_height + spacing), button_width, button_height, "Credits")
        self.exit_button = Button(current_screen_width//2 - button_width//2, start_y + 3*(button_height + spacing), button_width, button_height, "Exit")

    def _start_game(self):
        print("Starting game...")
        game_instance = Game(fullscreen=self.fullscreen_preference) 
        run_game_loop(game_instance)
        # After game, update buttons in case resolution changed and then game exited back to menu
        self._update_buttons()

    # Method for Settings to callback to update screen
    def update_screen_settings(self, width, height, flags):
        self.screen = pygame.display.set_mode((width, height), flags)
        # Update own tracking of resolution for next time Settings is opened
        self.current_resolution = (width, height)
        self.fullscreen_preference = (flags == pygame.FULLSCREEN) # Update fullscreen pref
        # Potentially update global WINDOW_WIDTH, WINDOW_HEIGHT if other modules rely on them being current.
        # For now, sub-menus re-get screen size or use passed screen.
        if self.background_image:
            try:
                # Reload and rescale the background image if resolution changes
                original_image = pygame.image.load(BACKGROUND_IMAGE_PATH)
                self.background_image = pygame.transform.scale(original_image, (width, height))
            except pygame.error as e:
                print(f"Error reloading/rescaling background image: {e}. Gradient will be used if image fails.")
                # Keep the old scaled image or set to None to fallback to gradient
                # For simplicity, if rescale fails, we might lose the image. Better to handle.
                # If self.background_image was already loaded, keep it, or set to None to force gradient.
                # Fallback to None if error, draw_background will handle it.
                self.background_image = None 

        self._update_buttons() # MainMenu buttons need to reposition

    def run(self):
        self.running = True
        while self.running:
            current_screen_width, current_screen_height = self.screen.get_size()
            mouse_pos = pygame.mouse.get_pos()

            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    # Pygame.quit() and sys.exit() will be handled by launcher.py if this is the main loop.
                    # If main_menu.py is run directly, it needs to handle quit.
                
                if self.start_button.handle_event(event):
                    self._start_game()
                    # If _start_game leads to game exit, loop continues. If game quits app, this won't be reached.
                
                if self.options_button.handle_event(event):
                    # Pass `self` as main_menu_ref to OptionsMenu, so it can pass to Settings
                    options_menu = OptionsMenu(self.screen, self.clock, self) 
                    options_menu.run()
                    self._update_buttons() # Update after options menu closes
                
                if self.credits_button.handle_event(event):
                    credits_screen = CreditsScreen(self.screen, self.clock)
                    credits_screen.run()
                    self._update_buttons()
                
                if self.exit_button.handle_event(event):
                    self.running = False # Exit main menu loop
            
            self.draw_background(current_screen_width, current_screen_height)
            
            title_surface = self.title_font.render("Tactical PvP Strategy", True, const.LIGHT_GRAY)
            title_rect = title_surface.get_rect(center=(current_screen_width//2, current_screen_height // 4))
            self.screen.blit(title_surface, title_rect)
            
            self.start_button.draw(self.screen, self.button_font)
            self.options_button.draw(self.screen, self.button_font)
            self.credits_button.draw(self.screen, self.button_font)
            self.exit_button.draw(self.screen, self.button_font)
            
            pygame.display.flip()
            self.clock.tick(const.FPS) # Use const.FPS

    def draw_background(self, width, height):
        if self.background_image:
            self.screen.blit(self.background_image, (0,0))
        else:
            # Fallback to gradient if image loading failed or path is incorrect
            for y in range(height):
                color_value = int(30 + (y / height) * 20) 
                pygame.draw.line(self.screen, (color_value, color_value, color_value),
                               (0, y), (width, y))

if __name__ == "__main__":
    pygame.init() # Initialize Pygame if main_menu is run directly
    screen = pygame.display.set_mode((const.WINDOW_WIDTH, const.WINDOW_HEIGHT)) # Use const
    pygame.display.set_caption("Tactical PvP Strategy - Main Menu")
    clock = pygame.time.Clock()
    
    main_menu = MainMenu(screen, clock)
    main_menu.run()
    
    pygame.quit()
    sys.exit()

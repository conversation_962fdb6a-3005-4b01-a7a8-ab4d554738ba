#!/usr/bin/env python3
"""
Test the Arcane Missiles board reference fix
"""

import pygame
from game_state import Game
from units.mage import Mage
from units.warrior import Warrior

def test_arcane_missiles_board_fix():
    """Test if the board reference fix works"""
    pygame.init()
    
    print("🔧 TESTING ARCANE MISSILES BOARD FIX 🔧")
    print("=" * 42)
    
    # Test 1: Normal case (should work)
    print("📋 TEST 1: Normal Case with Board Reference")
    print("-" * 42)
    
    game = Game()
    mage = Mage(1)
    target = Warrior(2)
    
    # Position units
    mage.position = (4, 4)
    target.position = (3, 4)
    
    # Set up board
    mage.board = game.board  # Ensure board is set
    game.board.units = {
        (4, 4): mage,
        (3, 4): target
    }
    
    print(f"Setup:")
    print(f"  Mage board reference: {mage.board is not None}")
    print(f"  Target HP before: {target.health}")
    
    # Find Arcane Missile ability
    arcane_ability_idx = None
    for i, ability in enumerate(mage.abilities):
        if ability.name == "Arcane Missile":
            arcane_ability_idx = i
            break
    
    # Use Arcane Missiles
    result = mage.use_ability(arcane_ability_idx, (3, 4), game)
    
    print(f"\nResults:")
    print(f"  Ability result: {result}")
    print(f"  Target HP after: {target.health}")
    
    if target.health < 7:
        print(f"✅ Normal case works: {7 - target.health} damage dealt")
    else:
        print(f"❌ Normal case failed: No damage dealt")
    
    # Test 2: Broken case (no board reference)
    print(f"\n📋 TEST 2: Broken Case without Board Reference")
    print("-" * 47)
    
    game2 = Game()
    mage2 = Mage(1)
    target2 = Warrior(2)
    
    # Position units
    mage2.position = (4, 4)
    target2.position = (3, 4)
    
    # Set up board but DON'T set mage.board
    game2.board.units = {
        (4, 4): mage2,
        (3, 4): target2
    }
    
    print(f"Setup:")
    print(f"  Mage board reference: {mage2.board}")
    print(f"  Target HP before: {target2.health}")
    
    # Try to use Arcane Missiles without proper board reference
    print(f"\n🚀 Using Arcane Missiles without board reference...")
    
    # Call the internal method directly to test the fix
    result2 = mage2._use_arcane_missile((3, 4))
    
    print(f"\nResults:")
    print(f"  Direct method result: {result2}")
    print(f"  Target HP after: {target2.health}")
    
    if result2 == False:
        print(f"✅ Board reference check working: Method correctly failed")
    else:
        print(f"❌ Board reference check not working: Method should have failed")
    
    # Test 3: Game flow simulation
    print(f"\n📋 TEST 3: Game Flow Simulation")
    print("-" * 33)
    
    game3 = Game()
    mage3 = Mage(1)
    target3 = Warrior(2)
    
    # Position units
    mage3.position = (4, 4)
    target3.position = (3, 4)
    
    # Set up board
    game3.board.units = {
        (4, 4): mage3,
        (3, 4): target3
    }
    
    # Simulate game selection (this should set board reference)
    game3.selected_unit = mage3
    
    print(f"Setup:")
    print(f"  Mage board reference before: {mage3.board}")
    
    # Simulate ability targeting (this should also set board reference)
    targets = mage3.get_ability_targets(arcane_ability_idx, game3.board)
    print(f"  Mage board reference after targeting: {mage3.board is not None}")
    print(f"  Valid targets: {len(targets)}")
    
    # Use ability through normal game flow
    original_hp = target3.health
    result3 = mage3.use_ability(arcane_ability_idx, (3, 4), game3)
    
    print(f"\nResults:")
    print(f"  Game flow result: {result3}")
    print(f"  Target HP: {original_hp} → {target3.health}")
    
    if target3.health < original_hp:
        print(f"✅ Game flow works: {original_hp - target3.health} damage dealt")
    else:
        print(f"❌ Game flow failed: No damage dealt")
    
    print(f"\n" + "=" * 42)
    print("🎯 BOARD REFERENCE FIX SUMMARY")
    print("-" * 30)
    print("This test checks if the board reference")
    print("is properly maintained throughout the")
    print("Arcane Missiles execution process.")
    print("\nIf Test 2 shows the error message,")
    print("the fix is working correctly!")

if __name__ == "__main__":
    test_arcane_missiles_board_fix()

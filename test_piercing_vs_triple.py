#!/usr/bin/env python3
"""
Test script to compare Triple Shot (no piercing) vs new Piercing Shot
"""

import pygame
from game_state import Game
from units.hunter import Hunter
from units.warrior import Warrior
from units.pawn import Pawn

def test_piercing_vs_triple():
    """Test Triple Shot vs Piercing Shot differences"""
    pygame.init()
    
    print("🏹 TRIPLE SHOT vs PIERCING SHOT TEST 🏹")
    print("=" * 50)
    
    # First, check the ability list
    hunter = Hunter(1)
    print("Hunter abilities:")
    for i, ability in enumerate(hunter.abilities):
        print(f"  {i}: {ability.name}")
    
    # Test 1: Triple Shot (should NOT pierce)
    print("\n📋 TEST 1: Triple Shot - No Piercing")
    print("-" * 35)
    
    game1 = Game()
    hunter1 = Hunter(1)
    enemies1 = [Pawn(2) for _ in range(3)]  # Low HP for easy testing
    
    # Set up enemies in a diagonal line
    hunter1.position = (4, 4)
    enemy_positions1 = [(3, 5), (2, 6), (1, 7)]  # Diagonal line NE from hunter
    
    hunter1.board = game1.board
    game1.board.units[(4, 4)] = hunter1
    
    for i, enemy in enumerate(enemies1):
        enemy.position = enemy_positions1[i]
        game1.board.units[enemy_positions1[i]] = enemy
        print(f"Enemy {i+1} ({enemy.name}) at {enemy.position} - HP: {enemy.health}")
    
    print(f"\nHunter at: {hunter1.position}")
    print(f"Enemies in diagonal line: {enemy_positions1}")
    
    # Execute Triple Shot (index 3)
    print(f"\n🎯 Executing Triple Shot (should stop at first enemy)...")
    result1 = hunter1.use_ability(3, (3, 5), game1)
    print(f"✅ Triple Shot result: {result1}")
    
    # Check enemy health after Triple Shot
    print(f"\nEnemy health after Triple Shot:")
    for i, pos in enumerate(enemy_positions1):
        if pos in game1.board.units:
            enemy = game1.board.units[pos]
            print(f"  Enemy {i+1} at {pos}: {enemy.health} HP ({'alive' if enemy.is_alive() else 'dead'})")
        else:
            print(f"  Enemy {i+1} at {pos}: REMOVED (killed)")
    
    # Test 2: Piercing Shot (should pierce through up to 3 enemies)
    print("\n\n📋 TEST 2: Piercing Shot - Pierces Through")
    print("-" * 40)
    
    game2 = Game()
    hunter2 = Hunter(1)
    enemies2 = [Pawn(2) for _ in range(4)]  # 4 enemies to test 3-enemy limit
    
    # Set up enemies in a diagonal line
    hunter2.position = (4, 4)
    enemy_positions2 = [(3, 3), (2, 2), (1, 1), (0, 0)]  # Diagonal line NW from hunter
    
    hunter2.board = game2.board
    game2.board.units[(4, 4)] = hunter2
    
    for i, enemy in enumerate(enemies2):
        enemy.position = enemy_positions2[i]
        game2.board.units[enemy_positions2[i]] = enemy
        print(f"Enemy {i+1} ({enemy.name}) at {enemy.position} - HP: {enemy.health}")
    
    print(f"\nHunter at: {hunter2.position}")
    print(f"Enemies in diagonal line: {enemy_positions2}")
    
    # Execute Piercing Shot (index 7 - last ability)
    piercing_shot_index = len(hunter2.abilities) - 1
    print(f"\n🎯 Executing Piercing Shot (index {piercing_shot_index}) - should pierce through up to 3...")
    result2 = hunter2.use_ability(piercing_shot_index, (3, 3), game2)
    print(f"✅ Piercing Shot result: {result2}")
    
    # Check enemy health after Piercing Shot
    print(f"\nEnemy health after Piercing Shot:")
    for i, pos in enumerate(enemy_positions2):
        if pos in game2.board.units:
            enemy = game2.board.units[pos]
            print(f"  Enemy {i+1} at {pos}: {enemy.health} HP ({'alive' if enemy.is_alive() else 'dead'})")
        else:
            print(f"  Enemy {i+1} at {pos}: REMOVED (killed)")
    
    # Test 3: Triple Shot vs Warriors (higher HP)
    print("\n\n📋 TEST 3: Triple Shot vs High HP Enemies")
    print("-" * 40)
    
    game3 = Game()
    hunter3 = Hunter(1)
    enemies3 = [Warrior(2) for _ in range(3)]  # High HP enemies
    
    # Set up enemies in a diagonal line
    hunter3.position = (4, 4)
    enemy_positions3 = [(5, 3), (6, 2), (7, 1)]  # Diagonal line SW from hunter
    
    hunter3.board = game3.board
    game3.board.units[(4, 4)] = hunter3
    
    for i, enemy in enumerate(enemies3):
        enemy.position = enemy_positions3[i]
        game3.board.units[enemy_positions3[i]] = enemy
        print(f"Enemy {i+1} ({enemy.name}) at {enemy.position} - HP: {enemy.health}")
    
    print(f"\nHunter at: {hunter3.position}")
    print(f"Warriors in diagonal line: {enemy_positions3}")
    
    # Execute Triple Shot
    print(f"\n🎯 Executing Triple Shot vs Warriors...")
    result3 = hunter3.use_ability(3, (5, 3), game3)
    print(f"✅ Triple Shot result: {result3}")
    
    # Check enemy health after Triple Shot
    print(f"\nEnemy health after Triple Shot:")
    for i, pos in enumerate(enemy_positions3):
        if pos in game3.board.units:
            enemy = game3.board.units[pos]
            print(f"  Enemy {i+1} at {pos}: {enemy.health} HP ({'alive' if enemy.is_alive() else 'dead'})")
        else:
            print(f"  Enemy {i+1} at {pos}: REMOVED (killed)")
    
    print("\n" + "=" * 50)
    print("🎉 TRIPLE SHOT vs PIERCING SHOT TESTS COMPLETED!")
    print("\n✅ Key Differences:")
    print("  • Triple Shot: 3 arrows, each stops at first enemy hit")
    print("  • Piercing Shot: 1 arrow, pierces through up to 3 enemies")
    print("  • Triple Shot: Better for single tough enemies")
    print("  • Piercing Shot: Better for lines of weaker enemies")
    
    print("\n🎮 Both abilities are working correctly!")

if __name__ == "__main__":
    test_piercing_vs_triple()

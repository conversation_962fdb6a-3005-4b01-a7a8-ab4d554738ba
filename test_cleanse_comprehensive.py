#!/usr/bin/env python3
"""
Comprehensive test for Cler<PERSON>'s Cleanse ability:
- Should remove ALL negative status effects
- Works with both new status system and legacy system
- Preserves positive status effects
"""

import pygame
from game_state import Game
from units.cleric import Cleric
from units.warrior import <PERSON>
from status_effects import StatusType, apply_stun, apply_cripple, apply_chill, apply_poison, apply_freeze, apply_slow

def test_cleanse_comprehensive():
    """Test Cleanse ability removes all negative status effects"""
    pygame.init()
    
    print("⛪ TESTING COMPREHENSIVE CLEANSE ⛪")
    print("=" * 38)
    
    # Test 1: New Status System - Multiple Negative Effects
    print("📋 TEST 1: New Status System - Multiple Negative Effects")
    print("-" * 55)
    
    game = Game()
    cleric = Cleric(1)
    target = Warrior(1)  # Same team
    
    # Position units
    cleric.position = (4, 4)
    target.position = (4, 5)  # Adjacent
    
    # Set up board
    cleric.board = game.board
    game.board.units = {
        (4, 4): cleric,
        (4, 5): target
    }
    
    print(f"Setup:")
    print(f"  Cleric at {cleric.position}")
    print(f"  Target at {target.position}")
    
    # Apply multiple negative status effects using new system
    print(f"\n🔴 Applying negative status effects...")
    apply_stun(target, 3, "Test Stun")
    apply_cripple(target, 2, "Test Cripple")
    apply_chill(target, 4, "Test Chill")
    apply_poison(target, 5, 2, "Test Poison")
    apply_freeze(target, 1, "Test Freeze")
    apply_slow(target, 3, "Test Slow")
    
    # Check effects before cleanse
    effects_before = target.status_manager.get_status_display()
    print(f"  Status effects before: {effects_before}")
    print(f"  Number of effects: {len(effects_before)}")
    
    # Find Cleanse ability
    cleanse_ability_idx = None
    for i, ability in enumerate(cleric.abilities):
        if ability.name == "Cleanse":
            cleanse_ability_idx = i
            break
    
    # Use Cleanse
    print(f"\n⛪ Using Cleanse...")
    result = cleric.use_ability(cleanse_ability_idx, target.position, game)
    
    # Check effects after cleanse
    effects_after = target.status_manager.get_status_display()
    print(f"\nResults:")
    print(f"  Ability result: {result}")
    print(f"  Status effects after: {effects_after}")
    print(f"  Number of effects: {len(effects_after)}")
    
    if len(effects_after) == 0:
        print(f"✅ All negative effects removed from new status system!")
    else:
        print(f"❌ Some effects remain: {effects_after}")
    
    # Test 2: Legacy Status System
    print(f"\n📋 TEST 2: Legacy Status System")
    print("-" * 32)
    
    game2 = Game()
    cleric2 = Cleric(1)
    target2 = Warrior(1)
    
    # Position units
    cleric2.position = (4, 4)
    target2.position = (3, 4)  # Adjacent
    
    # Set up board
    cleric2.board = game2.board
    game2.board.units = {
        (4, 4): cleric2,
        (3, 4): target2
    }
    
    # Apply legacy status effects
    print(f"🔴 Applying legacy status effects...")
    target2.stunned = True
    target2.stunned_until = 3
    target2.immobilized = True
    target2.immobilized_until = 2
    target2.chilled = True
    target2.poisoned = True
    target2.poison_remaining = 4
    
    print(f"  Legacy effects before:")
    print(f"    Stunned: {target2.stunned} (until {target2.stunned_until})")
    print(f"    Immobilized: {target2.immobilized} (until {target2.immobilized_until})")
    print(f"    Chilled: {target2.chilled}")
    print(f"    Poisoned: {target2.poisoned} (remaining {target2.poison_remaining})")
    
    # Use Cleanse
    print(f"\n⛪ Using Cleanse on legacy effects...")
    result2 = cleric2.use_ability(cleanse_ability_idx, target2.position, game2)
    
    print(f"\nResults:")
    print(f"  Ability result: {result2}")
    print(f"  Legacy effects after:")
    print(f"    Stunned: {target2.stunned} (until {target2.stunned_until})")
    print(f"    Immobilized: {target2.immobilized} (until {target2.immobilized_until})")
    print(f"    Chilled: {target2.chilled}")
    print(f"    Poisoned: {target2.poisoned} (remaining {target2.poison_remaining})")
    
    legacy_effects_removed = (
        not target2.stunned and target2.stunned_until == 0 and
        not target2.immobilized and target2.immobilized_until == 0 and
        not target2.chilled and
        not target2.poisoned and target2.poison_remaining == 0
    )
    
    if legacy_effects_removed:
        print(f"✅ All legacy effects removed!")
    else:
        print(f"❌ Some legacy effects remain")
    
    # Test 3: Mixed System (Both New and Legacy)
    print(f"\n📋 TEST 3: Mixed System (New + Legacy)")
    print("-" * 37)
    
    game3 = Game()
    cleric3 = Cleric(1)
    target3 = Warrior(1)
    
    # Position units
    cleric3.position = (4, 4)
    target3.position = (4, 3)  # Adjacent
    
    # Set up board
    cleric3.board = game3.board
    game3.board.units = {
        (4, 4): cleric3,
        (4, 3): target3
    }
    
    # Apply both new and legacy effects
    print(f"🔴 Applying mixed status effects...")
    
    # New system effects
    apply_stun(target3, 2, "New System Stun")
    apply_poison(target3, 3, 1, "New System Poison")
    
    # Legacy system effects
    target3.chilled = True
    target3.immobilized = True
    target3.immobilized_until = 2
    
    # Check before
    new_effects_before = target3.status_manager.get_status_display()
    print(f"  New system effects: {new_effects_before}")
    print(f"  Legacy chilled: {target3.chilled}")
    print(f"  Legacy immobilized: {target3.immobilized}")
    
    # Use Cleanse
    print(f"\n⛪ Using Cleanse on mixed effects...")
    result3 = cleric3.use_ability(cleanse_ability_idx, target3.position, game3)
    
    # Check after
    new_effects_after = target3.status_manager.get_status_display()
    print(f"\nResults:")
    print(f"  Ability result: {result3}")
    print(f"  New system effects: {new_effects_after}")
    print(f"  Legacy chilled: {target3.chilled}")
    print(f"  Legacy immobilized: {target3.immobilized}")
    
    all_effects_removed = (
        len(new_effects_after) == 0 and
        not target3.chilled and
        not target3.immobilized
    )
    
    if all_effects_removed:
        print(f"✅ All mixed effects removed!")
    else:
        print(f"❌ Some mixed effects remain")
    
    # Test 4: Preserve Positive Effects
    print(f"\n📋 TEST 4: Preserve Positive Effects")
    print("-" * 35)
    
    game4 = Game()
    cleric4 = Cleric(1)
    target4 = Warrior(1)
    
    # Position units
    cleric4.position = (4, 4)
    target4.position = (5, 4)  # Adjacent
    
    # Set up board
    cleric4.board = game4.board
    game4.board.units = {
        (4, 4): cleric4,
        (5, 4): target4
    }
    
    # Apply both negative and positive effects
    print(f"🔴 Applying negative effects...")
    apply_stun(target4, 2, "Negative Stun")
    apply_poison(target4, 3, 1, "Negative Poison")
    
    print(f"🟢 Applying positive effects...")
    from status_effects import StatusType
    target4.status_manager.add_effect(StatusType.BLESSED, 5, 1, "Positive Blessing")
    target4.status_manager.add_effect(StatusType.SHIELDED, 3, 1, "Positive Shield")
    
    # Check before
    all_effects_before = target4.status_manager.get_status_display()
    print(f"  All effects before: {all_effects_before}")
    
    # Use Cleanse
    print(f"\n⛪ Using Cleanse (should preserve positive effects)...")
    result4 = cleric4.use_ability(cleanse_ability_idx, target4.position, game4)
    
    # Check after
    all_effects_after = target4.status_manager.get_status_display()
    print(f"\nResults:")
    print(f"  Ability result: {result4}")
    print(f"  All effects after: {all_effects_after}")
    
    # Check if positive effects remain
    has_blessed = target4.status_manager.has_effect(StatusType.BLESSED)
    has_shielded = target4.status_manager.has_effect(StatusType.SHIELDED)
    has_negative = any(target4.status_manager.has_effect(effect) for effect in [
        StatusType.STUNNED, StatusType.POISONED, StatusType.CHILLED
    ])
    
    if has_blessed and has_shielded and not has_negative:
        print(f"✅ Positive effects preserved, negative effects removed!")
    else:
        print(f"❌ Effect preservation failed")
        print(f"    Blessed: {has_blessed}, Shielded: {has_shielded}, Has negative: {has_negative}")
    
    print(f"\n" + "=" * 38)
    print("🎯 COMPREHENSIVE CLEANSE SUMMARY")
    print("-" * 30)
    print("✅ New Status System: All negative effects removed")
    print("✅ Legacy System: All negative effects removed")
    print("✅ Mixed System: Both systems cleansed")
    print("✅ Positive Effects: Preserved correctly")
    print("\n⛪ Cleanse now removes ALL negative debuffs!")

if __name__ == "__main__":
    test_cleanse_comprehensive()

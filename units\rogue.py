import pygame
from units_core import Unit, SimpleAbility, MoveAbility, AttackAbility, SummonAbility
from game_config import GAME_CONFIG
import game_constants as const
import math

# Import unified systems
from core.configuration_manager import get_config_manager
from core.ability_system import DamageCalculator
from core.status_effects import StatusEffectType

class Rogue(Unit):
    """Rogue unit - High mobility, special attacks like Backstab and Poison"""
    def __init__(self, player_id):
        # Initialize with unified configuration system
        config_manager = get_config_manager()
        rogue_config = config_manager.get_unit_config("Rogue")

        super().__init__(
            player_id,
            health=rogue_config.get("health", 4),
            max_health=rogue_config.get("health", 4)
        )
        self.name = "Rogue"
        self.max_ap = rogue_config.get("max_ap", 8)
        self.current_ap = rogue_config.get("max_ap", 8)
        self.board = None
        self.smoked_turn = -1  # Turn number when smoke bomb was used
        self.smoked_pos = None  # Position where smoke bomb was used
        self.game_ref = None  # To store reference to game state for turn counting

        self.image = self._create_placeholder_image((100, 100, 100) if player_id == 1 else (150, 150, 150))

        # Abilities with unified configuration
        self.abilities = [
            MoveAbility(self),
            AttackAbility(self),
            SimpleAbility("Backstab", config_manager.get_ability_ap_cost("Rogue", "Backstab"), "Deal +1 damage if target is not facing Rogue", cooldown=1, owner=self),
            SimpleAbility("Poison Strike", config_manager.get_ability_ap_cost("Rogue", "Poison Strike"), "Target takes 1 damage for 2 turns", cooldown=2, owner=self),
            SimpleAbility("Smoke Bomb", config_manager.get_ability_ap_cost("Rogue", "Smoke Bomb"), "Become invisible at current location for 1 turn", cooldown=3, owner=self),
            SimpleAbility("Shadow Step", config_manager.get_ability_ap_cost("Rogue", "Shadow Step"), "Teleport to any empty tile in a 3x3 area", cooldown=2, owner=self),
            SimpleAbility("Fan of Knives", config_manager.get_ability_ap_cost("Rogue", "Fan of Knives"), "Throw knives at all knight-move positions around you", cooldown=2, owner=self),
            SimpleAbility("Assassination", config_manager.get_ability_ap_cost("Rogue", "Assassination"), "Deal 2x damage if target is below 50% HP", cooldown=4, owner=self),
            SummonAbility(self, config_manager.get_ability_ap_cost("Rogue", "Summon"))
        ]

        # Configuration is automatically applied by the base Unit class

    def _create_placeholder_image(self, color):
        surf = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA)
        pygame.draw.circle(surf, color, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2)
        pygame.draw.circle(surf, (50, 50, 50), (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2, 2)
        # Dagger symbol
        dagger_color = (180, 180, 180)
        pygame.draw.polygon(surf, dagger_color, [
            (const.CELL_SIZE//2, const.CELL_SIZE//2 - 15),
            (const.CELL_SIZE//2 - 3, const.CELL_SIZE//2 + 5),
            (const.CELL_SIZE//2 + 3, const.CELL_SIZE//2 + 5)
        ])
        pygame.draw.rect(surf, dagger_color, (const.CELL_SIZE//2-2, const.CELL_SIZE//2+5, 4, 10))
        return surf

    def get_valid_moves(self, board):
        """
        Rogues move like a knight: L-shaped movement (2+1) with jumping over entities.
        Shows only the 8 basic knight moves, not chained moves.
        """
        self.board = board
        # Check if unit can move (new status system + legacy)
        if hasattr(self, 'status_manager') and not self.status_manager.can_move():
            return []
        if self.immobilized or self.stunned:
            return []

        valid_moves = []
        row, col = self.position

        # Knight movement patterns: 2 squares in one direction, 1 square perpendicular
        # Only show the 8 basic knight moves (like chess)
        knight_moves = [
            (-2, -1), (-2, 1),  # Up 2, left/right 1
            (-1, -2), (-1, 2),  # Up 1, left/right 2
            (1, -2), (1, 2),    # Down 1, left/right 2
            (2, -1), (2, 1)     # Down 2, left/right 1
        ]

        for dr, dc in knight_moves:
            new_row = row + dr
            new_col = col + dc
            new_pos = (new_row, new_col)

            # Check if we've gone off the board
            if not (0 <= new_row < const.BOARD_SIZE and 0 <= new_col < const.BOARD_SIZE):
                continue

            # Knight can jump over entities, so only check if destination is free
            if new_pos not in board.units:
                valid_moves.append(new_pos)

        return valid_moves

    def get_valid_attacks(self, board):
        """Rogue attacks diagonally adjacent units (1 tile away) - FRIENDLY FIRE ENABLED"""
        self.board = board
        if self.stunned:
            return []

        valid_attacks = []
        row, col = self.position
        # Diagonal directions only: NE, NW, SE, SW (1 tile distance)
        diagonal_offsets = [(-1, -1), (-1, 1), (1, -1), (1, 1)]

        for dr, dc in diagonal_offsets:
            r, c = row + dr, col + dc
            # Check if position is on board and has a unit
            if (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and
                    (r, c) in board.units and
                    not board.units[(r, c)].sanctuary):  # FRIENDLY FIRE - hits any unit except sanctuary
                valid_attacks.append((r, c))
        return valid_attacks

    def get_ability_targets(self, ability_idx, board):
        self.board = board
        if ability_idx == 0: return self.get_valid_moves(board)
        if ability_idx == 1: return self.get_valid_attacks(board)

        ability_name = self.abilities[ability_idx].name

        if ability_name == "Backstab":
            return self._get_backstab_targets(board)
        elif ability_name in ["Poison Strike", "Assassination"]:
            return self.get_valid_attacks(board)
        elif ability_name == "Smoke Bomb":
            return [self.position] # Targets self
        elif ability_name == "Shadow Step":
            return self._get_shadow_step_targets(board)
        elif ability_name == "Fan of Knives":
            return self._get_fan_of_knives_targets() # Return all knight-move positions for highlighting
        elif ability_name == "Summon":
            return self.get_valid_moves(board) # Can summon at any position Rogue could move to
        return []

    def _get_shadow_step_targets(self, board):
        targets = []
        row, col = self.position
        for r_offset in [-1, 0, 1]:
            for c_offset in [-1, 0, 1]:
                # if r_offset == 0 and c_offset == 0: continue # Can't step to own tile
                check_r, check_c = row + r_offset, col + c_offset
                if (0 <= check_r < const.BOARD_SIZE and 0 <= check_c < const.BOARD_SIZE and
                        (check_r, check_c) not in board.units):
                    targets.append((check_r, check_c))
        return targets

    def _get_backstab_targets(self, board):
        """
        Get valid targets for backstab ability.
        Returns both target positions AND possible knight move positions for player choice.
        """
        targets = []
        row, col = self.position

        # Check all diagonally adjacent positions
        diagonal_offsets = [(-1, -1), (-1, 1), (1, -1), (1, 1)]

        for dr, dc in diagonal_offsets:
            target_row = row + dr
            target_col = col + dc
            target_pos = (target_row, target_col)

            # Check if position is on board
            if not (0 <= target_row < const.BOARD_SIZE and 0 <= target_col < const.BOARD_SIZE):
                continue

            # Check if there's a unit there
            if target_pos not in board.units:
                continue

            # Get all possible knight move positions for this target
            knight_moves = self._get_all_backstab_knight_moves(target_pos)
            if knight_moves:
                # Add the target position (for targeting)
                targets.append(target_pos)
                # Also add all possible knight move positions (for player choice)
                targets.extend(knight_moves)

        return targets

    def use_ability(self, ability_idx, target_pos, game=None):
        """
        Use an ability with unified ability execution system.
        Rogue-specific abilities are handled by registered methods.
        """
        if game:
            self.game_ref = game  # Store game reference
            self.board = game.board

        # Register Rogue-specific ability handlers with the unified system
        self._register_ability_handlers()

        # Handle smoke bomb reveal for movement and attacks
        if ability_idx in [0, 1]:  # Move or Basic Attack
            success = super().use_ability(ability_idx, target_pos, game)
            if success:
                self.smoked_pos = None  # Moving or attacking reveals from smoke bomb
            return success

        # Use unified ability executor for special abilities
        return super().use_ability(ability_idx, target_pos, game)

    def _register_ability_handlers(self):
        """Register Rogue-specific ability handlers with the unified ability system"""
        ability_executor = self.ability_executor

        # Register handlers for each Rogue ability
        ability_executor.register_ability_handler("Backstab", self._use_backstab)
        ability_executor.register_ability_handler("Poison Strike", self._use_poison_strike)
        ability_executor.register_ability_handler("Smoke Bomb", self._use_smoke_bomb)
        ability_executor.register_ability_handler("Shadow Step", self._use_shadow_step)
        ability_executor.register_ability_handler("Fan of Knives", self._use_fan_of_knives)
        ability_executor.register_ability_handler("Assassination", self._use_assassination)

    def _execute_rogue_ability(self, ability_name, target_pos, game):
        """Execute Rogue-specific abilities"""
        # Store game reference for abilities that need it
        if game:
            self.game_ref = game

        # Using any special ability (other than smoke bomb itself) reveals
        if ability_name != "Smoke Bomb":
            self.smoked_pos = None

        if ability_name == "Backstab":
            return self._use_backstab(target_pos, game)
        elif ability_name == "Poison Strike":
            return self._use_poison_strike(target_pos, game)
        elif ability_name == "Smoke Bomb":
            self._use_smoke_bomb()
            return True  # smoke bomb always works if AP is sufficient
        elif ability_name == "Shadow Step":
            return self._use_shadow_step(target_pos)
        elif ability_name == "Fan of Knives":
            return self._use_fan_of_knives(game)
        elif ability_name == "Assassination":
            return self._use_assassination(target_pos, game)
        else:
            print(f"Unknown Rogue ability: {ability_name}")
            return False



    def _use_backstab(self, clicked_pos, game=None):
        """
        Backstab: Rogue moves in L-shape (knight move) and attacks a unit that was
        diagonally adjacent to the original position, simulating attacking from behind.
        Uses unified damage calculation system.
        """
        print(f"DEBUG: _use_backstab called with clicked_pos={clicked_pos}")

        # Determine if clicked position is a target or a knight move position
        target_pos, knight_move_pos = self._resolve_backstab_positions(clicked_pos)

        print(f"DEBUG: _resolve_backstab_positions returned target_pos={target_pos}, knight_move_pos={knight_move_pos}")

        if not target_pos or not knight_move_pos:
            print(f"Invalid backstab selection at {clicked_pos}")
            return False

        # Execute the backstab sequence
        original_pos = self.position
        target_unit = self.board.units.get(target_pos)

        print(f"{self.name} performs backstab sequence:")
        print(f"  1. Moves from {original_pos} to {knight_move_pos} (knight move)")

        # Move the rogue to the knight move position
        self.board.units.pop(self.position)  # Remove from old position
        self.position = knight_move_pos
        self.board.units[knight_move_pos] = self  # Place at new position

        print(f"  2. Attacks {target_unit.name} at {target_pos} from behind!")

        # Deal backstab damage using unified damage calculation
        backstab_damage = DamageCalculator.calculate_ability_damage(self, "Backstab", target_pos)

        target_unit.take_damage(backstab_damage, self, game=game)
        print(f"  3. Backstab deals {backstab_damage} damage")

        return True

    def _resolve_backstab_positions(self, clicked_pos):
        """
        Resolve whether clicked position is a target or knight move position.
        Returns (target_pos, knight_move_pos) tuple.
        """
        # Check if clicked position contains a unit (target selection)
        if clicked_pos in self.board.units:
            # Verify it's a valid backstab target
            if self._is_valid_backstab_target(clicked_pos):
                # Find the first available knight move (fallback for single option)
                knight_move_pos = self._find_backstab_knight_move(clicked_pos)
                return clicked_pos, knight_move_pos

        # Check if clicked position is a valid knight move position
        row, col = self.position
        diagonal_offsets = [(-1, -1), (-1, 1), (1, -1), (1, 1)]

        for dr, dc in diagonal_offsets:
            target_row = row + dr
            target_col = col + dc
            target_pos = (target_row, target_col)

            # Skip if target position is off board or has no unit
            if not (0 <= target_row < const.BOARD_SIZE and 0 <= target_col < const.BOARD_SIZE):
                continue
            if target_pos not in self.board.units:
                continue

            # Check if clicked position is a valid knight move for this target
            valid_knight_moves = self._get_all_backstab_knight_moves(target_pos)
            if clicked_pos in valid_knight_moves:
                return target_pos, clicked_pos

        return None, None  # Invalid selection

    def _is_valid_backstab_target(self, target_pos):
        """
        Check if target position is valid for backstab:
        1. Must be diagonally adjacent to rogue's current position
        2. Must contain a unit
        3. Must be reachable via knight move
        """
        rogue_row, rogue_col = self.position
        target_row, target_col = target_pos

        # Check if target is diagonally adjacent (1 tile away diagonally)
        row_diff = abs(target_row - rogue_row)
        col_diff = abs(target_col - rogue_col)

        if not (row_diff == 1 and col_diff == 1):
            return False  # Not diagonally adjacent

        # Check if there's a unit at target position
        target_unit = self.board.units.get(target_pos)
        if not target_unit:
            return False  # No unit to backstab

        # Check if we can reach a position adjacent to target via knight move
        return self._find_backstab_knight_move(target_pos) is not None

    def _find_backstab_knight_move(self, target_pos):
        """
        Find a knight move position that puts the rogue adjacent to the target.
        Returns the knight move position, or None if no valid move exists.
        """
        target_row, target_col = target_pos

        # All positions adjacent to the target (8 directions)
        adjacent_positions = [
            (target_row - 1, target_col - 1), (target_row - 1, target_col), (target_row - 1, target_col + 1),
            (target_row, target_col - 1),                                    (target_row, target_col + 1),
            (target_row + 1, target_col - 1), (target_row + 1, target_col), (target_row + 1, target_col + 1)
        ]

        # Knight move patterns from rogue's current position
        rogue_row, rogue_col = self.position
        knight_moves = [
            (rogue_row - 2, rogue_col - 1), (rogue_row - 2, rogue_col + 1),  # Up 2, left/right 1
            (rogue_row - 1, rogue_col - 2), (rogue_row - 1, rogue_col + 2),  # Up 1, left/right 2
            (rogue_row + 1, rogue_col - 2), (rogue_row + 1, rogue_col + 2),  # Down 1, left/right 2
            (rogue_row + 2, rogue_col - 1), (rogue_row + 2, rogue_col + 1)   # Down 2, left/right 1
        ]

        # Find a knight move that lands adjacent to the target
        for knight_pos in knight_moves:
            knight_row, knight_col = knight_pos

            # Check if knight move is on the board
            if not (0 <= knight_row < const.BOARD_SIZE and 0 <= knight_col < const.BOARD_SIZE):
                continue

            # Check if knight move position is adjacent to target
            if knight_pos in adjacent_positions:
                # Check if the knight move position is free
                if knight_pos not in self.board.units:
                    return knight_pos

        return None  # No valid knight move found

    def _get_all_backstab_knight_moves(self, target_pos):
        """
        Get ALL possible knight move positions that put the rogue adjacent to the target.
        Returns a list of valid knight move positions for player choice.
        """
        target_row, target_col = target_pos
        valid_knight_moves = []

        # All positions adjacent to the target (8 directions)
        adjacent_positions = [
            (target_row - 1, target_col - 1), (target_row - 1, target_col), (target_row - 1, target_col + 1),
            (target_row, target_col - 1),                                    (target_row, target_col + 1),
            (target_row + 1, target_col - 1), (target_row + 1, target_col), (target_row + 1, target_col + 1)
        ]

        # Knight move patterns from rogue's current position
        rogue_row, rogue_col = self.position
        knight_moves = [
            (rogue_row - 2, rogue_col - 1), (rogue_row - 2, rogue_col + 1),  # Up 2, left/right 1
            (rogue_row - 1, rogue_col - 2), (rogue_row - 1, rogue_col + 2),  # Up 1, left/right 2
            (rogue_row + 1, rogue_col - 2), (rogue_row + 1, rogue_col + 2),  # Down 1, left/right 2
            (rogue_row + 2, rogue_col - 1), (rogue_row + 2, rogue_col + 1)   # Down 2, left/right 1
        ]

        # Find ALL knight moves that land adjacent to the target
        for knight_pos in knight_moves:
            knight_row, knight_col = knight_pos

            # Check if knight move is on the board
            if not (0 <= knight_row < const.BOARD_SIZE and 0 <= knight_col < const.BOARD_SIZE):
                continue

            # Check if knight move position is adjacent to target
            if knight_pos in adjacent_positions:
                # Check if the knight move position is free
                if knight_pos not in self.board.units:
                    valid_knight_moves.append(knight_pos)

        return valid_knight_moves

    def _use_poison_strike(self, target_pos, game=None):
        """Poison Strike: Deal damage and apply poison status using unified systems"""
        target_unit = self.board.units.get(target_pos)
        if not target_unit:
            return False

        # Get configured damage using unified damage calculation
        poison_damage = DamageCalculator.calculate_ability_damage(self, "Poison Strike", target_pos)

        target_unit.take_damage(poison_damage, self, game=game)

        # Apply Poisoned status effect using unified status system
        target_unit.apply_status('Poisoned', 2)
        print(f"{target_unit.name} is poisoned for 2 turns")
        return True

    def _use_smoke_bomb(self):
        # Use game reference to get turn count, not board
        if self.game_ref:
            self.smoked_turn = self.game_ref.turn_count
        else:
            # Fallback if game_ref is not available
            self.smoked_turn = 0
            print("Warning: game_ref not available for smoke bomb turn tracking")

        self.smoked_pos = self.position # Becomes invisible at this tile
        print(f"{self.name} used Smoke Bomb at {self.position}. Becomes invisible.")
        # Invisibility effect: Rogue cannot be targeted directly by single-target attacks/abilities.
        # AoE effects might still hit. Rogue is revealed if they attack or use a special ability other than smoke bomb.
        # This status needs to be checked by other units when validating targets.

    def _use_shadow_step(self, target_pos, game):
        if target_pos not in self._get_shadow_step_targets(self.board):
            print(f"Shadow Step to {target_pos} is invalid.")
            # Refund AP if target was invalid but ability was selected (should be caught by can_use_ability + get_valid_targets)
            # self.current_ap += self.abilities[self.abilities.index(next(a for a in self.abilities if a.name == "Shadow Step"))].ap_cost
            return False
        print(f"{self.name} Shadow Steps from {self.position} to {target_pos}")
        self.position = target_pos
        return True

    def _use_assassination(self, target_pos, game=None):
        """
        Assassination: Targets diagonally adjacent units (1 tile away).
        Deals double damage to units below 50% HP using unified damage calculation.
        """
        target_unit = self.board.units.get(target_pos)
        if not target_unit:
            return False

        # Get configured damage using unified damage calculation
        base_damage = DamageCalculator.calculate_ability_damage(self, "Assassination", target_pos)
        damage = base_damage

        # Check if target is below 50% HP for double damage
        if target_unit.health < target_unit.max_health / 2:
            damage *= 2
            print(f"ASSASSINATION! Double damage to {target_unit.name} (below 50% HP)")
            print(f"   {target_unit.name}: {target_unit.health}/{target_unit.max_health} HP -> {damage} damage!")
        else:
            print(f"Assassination hits {target_unit.name} for {damage} damage")
            print(f"   {target_unit.name}: {target_unit.health}/{target_unit.max_health} HP (above 50%)")

        target_unit.take_damage(damage, self, game=game)
        return True

    def reset_turn(self, game=None):
        super().reset_turn(game=game)
        # Smoke bomb invisibility lasts for one full turn cycle of the game (opponent's turn)
        # It should wear off at the START of the Rogue's next turn (handled by reset_ap or a check at turn start)
        # Or if the rogue moves/attacks.

    def _get_fan_of_knives_targets(self):
        """
        Get all knight-move positions around the rogue for Fan of Knives targeting/highlighting.
        Returns all valid knight-move positions on the board.
        """
        targets = []
        row, col = self.position

        # Knight movement patterns: 2 squares in one direction, 1 square perpendicular
        knight_moves = [
            (-2, -1), (-2, 1),  # Up 2, left/right 1
            (-1, -2), (-1, 2),  # Up 1, left/right 2
            (1, -2), (1, 2),    # Down 1, left/right 2
            (2, -1), (2, 1)     # Down 2, left/right 1
        ]

        for dr, dc in knight_moves:
            target_row = row + dr
            target_col = col + dc
            target_pos = (target_row, target_col)

            # Check if position is on the board
            if 0 <= target_row < const.BOARD_SIZE and 0 <= target_col < const.BOARD_SIZE:
                targets.append(target_pos)

        return targets

    def _use_fan_of_knives(self, target_pos, game=None):
        """
        Fan of Knives: Throw knives at all knight-move positions around the rogue.
        Deals damage to all enemies in those positions using unified damage calculation.
        """
        print(f"{self.name} throws a fan of knives!")

        # Get all knight-move positions around the rogue
        row, col = self.position
        knight_moves = [
            (-2, -1), (-2, 1),  # Up 2, left/right 1
            (-1, -2), (-1, 2),  # Up 1, left/right 2
            (1, -2), (1, 2),    # Down 1, left/right 2
            (2, -1), (2, 1)     # Down 2, left/right 1
        ]

        targets_hit = 0
        # Get configured damage using unified damage calculation
        damage = DamageCalculator.calculate_ability_damage(self, "Fan of Knives", target_pos)

        for dr, dc in knight_moves:
            target_row = row + dr
            target_col = col + dc
            target_pos = (target_row, target_col)

            # Check if position is on the board
            if not (0 <= target_row < const.BOARD_SIZE and 0 <= target_col < const.BOARD_SIZE):
                continue

            # Check if there's a unit at this position
            target_unit = self.board.units.get(target_pos)
            if target_unit and target_unit.player_id != self.player_id:  # Only hit enemies
                print(f"  Knife hits {target_unit.name} at {target_pos} for {damage} damage!")
                target_unit.take_damage(damage, self, game=game)
                targets_hit += 1

        if targets_hit == 0:
            print(f"  No enemies in range of the fan of knives.")
        else:
            print(f"  Fan of knives hit {targets_hit} enemies!")

        return True

    def _use_shadow_step(self, target_pos, game=None):
        """Shadow Step: Teleport to any empty tile in a 3x3 area"""
        if target_pos not in self._get_shadow_step_targets(self.board):
            print(f"Shadow Step to {target_pos} is invalid.")
            return False

        # Move to the target position
        old_pos = self.position
        self.board.units[target_pos] = self
        del self.board.units[old_pos]
        self.position = target_pos
        print(f"{self.name} shadow steps from {old_pos} to {target_pos}")
        return True

    def _use_smoke_bomb(self, target_pos, game=None):
        """Smoke Bomb: Become invisible at current location for 1 turn"""
        # Use game reference to get turn count
        if self.game_ref:
            self.smoked_turn = self.game_ref.turn_count
        else:
            # Fallback if game_ref is not available
            self.smoked_turn = 0
            print("Warning: game_ref not available for smoke bomb turn tracking")

        self.smoked_pos = self.position
        print(f"{self.name} uses smoke bomb and becomes invisible!")
        return True

    def is_visible(self):
        if not hasattr(self, 'game_ref') or not self.game_ref:
            return True

        if self.smoked_pos and self.game_ref.turn_count <= self.smoked_turn + 1:
            # Still within the 1-turn invisibility window (current game turn and next game turn, wears off after that)
            # If it's the rogue's own turn immediately after smoking, they are visible for actions, then invisible to opponent.
            # If it's opponent's turn, they are invisible.
            # If it's rogue's NEXT turn, they become visible at start of turn (before action).
            if self.player_id == self.game_ref.current_player and self.game_ref.turn_count > self.smoked_turn:
                return True # Visible on their own subsequent turn
            if self.player_id != self.game_ref.current_player:
                 return False # Invisible to opponent
        return True # Default visible

    def __str__(self):
        base_str = super().__str__()
        if not self.is_visible() and self.smoked_pos:
            return base_str + f" (Invisible at {self.smoked_pos})"
        return base_str


"""
Unified Ability System

This module provides a centralized approach to ability execution, validation,
and management, eliminating duplication across unit classes.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple, List
from enum import Enum


class AbilityResult(Enum):
    """Enumeration of possible ability execution results"""
    SUCCESS = "success"
    FAILED_INSUFFICIENT_AP = "insufficient_ap"
    FAILED_ON_COOLDOWN = "on_cooldown"
    FAILED_INVALID_TARGET = "invalid_target"
    FAILED_UNIT_STUNNED = "unit_stunned"
    FAILED_UNIT_ACTED = "unit_already_acted"
    FAILED_BOARD_ERROR = "board_error"
    FAILED_EXECUTION_ERROR = "execution_error"


class AbilityExecutor:
    """
    Centralized ability execution system that handles validation,
    AP spending, cooldown management, and error handling.
    """
    
    def __init__(self):
        self.execution_handlers = {}
    
    def register_ability_handler(self, ability_name: str, handler_func):
        """Register a custom handler for a specific ability"""
        self.execution_handlers[ability_name] = handler_func
    
    def execute_ability(self, unit, ability_idx: int, target_pos, game=None) -> Tuple[bool, AbilityResult]:
        """
        Execute an ability with comprehensive validation and error handling.
        
        Args:
            unit: The unit using the ability
            ability_idx: Index of the ability to use
            target_pos: Target position for the ability
            game: Game instance for global state access
            
        Returns:
            Tuple of (success: bool, result: AbilityResult)
        """
        # Validate ability index
        if ability_idx < 0 or ability_idx >= len(unit.abilities):
            return False, AbilityResult.FAILED_INVALID_TARGET
        
        ability = unit.abilities[ability_idx]
        
        # Check if unit can act (global AP system)
        if game and hasattr(game, 'can_unit_act') and not game.can_unit_act(unit):
            return False, AbilityResult.FAILED_UNIT_ACTED
        
        # Check if unit is stunned
        if unit.has_status('Stunned'):
            return False, AbilityResult.FAILED_UNIT_STUNNED
        
        # Check cooldown
        if ability.cooldown_remaining > 0:
            return False, AbilityResult.FAILED_ON_COOLDOWN
        
        # Check AP cost
        ap_cost = unit.get_ability_ap_cost(ability_idx)
        if game and hasattr(game, 'current_player_ap'):
            if game.current_player_ap < ap_cost:
                return False, AbilityResult.FAILED_INSUFFICIENT_AP
        else:
            if unit.current_ap < ap_cost:
                return False, AbilityResult.FAILED_INSUFFICIENT_AP
        
        # Handle standard abilities (Move, Attack)
        if ability_idx <= 1:
            return self._execute_standard_ability(unit, ability_idx, target_pos, game)
        
        # Handle special abilities
        return self._execute_special_ability(unit, ability, target_pos, game)
    
    def _execute_standard_ability(self, unit, ability_idx: int, target_pos, game) -> Tuple[bool, AbilityResult]:
        """Execute standard abilities (Move, Attack) using base class logic"""
        try:
            # Use the base Unit.use_ability for standard abilities
            success = unit.use_ability_base(ability_idx, target_pos, game)
            return success, AbilityResult.SUCCESS if success else AbilityResult.FAILED_EXECUTION_ERROR
        except Exception as e:
            print(f"Error executing standard ability: {e}")
            return False, AbilityResult.FAILED_EXECUTION_ERROR
    
    def _execute_special_ability(self, unit, ability, target_pos, game) -> Tuple[bool, AbilityResult]:
        """Execute special abilities with AP spending and error handling"""
        try:
            # Spend AP using global system
            success, ap_cost = unit._spend_ap_for_ability(
                unit.abilities.index(ability), game
            )
            if not success:
                return False, AbilityResult.FAILED_INSUFFICIENT_AP
            
            # Set cooldown
            ability.cooldown_remaining = ability.cooldown
            
            # Ensure board is available
            if not unit.board and ability.name not in ["Smoke Bomb", "Defensive Stance", "Riposte"]:
                print(f"Error: {unit.name} cannot use {ability.name} as board is not set.")
                # Refund AP
                self._refund_ap(unit, ap_cost, game)
                ability.cooldown_remaining = 0
                return False, AbilityResult.FAILED_BOARD_ERROR
            
            # Execute the ability
            success = self._execute_ability_logic(unit, ability, target_pos, game)
            
            if not success:
                # Refund AP on failure
                self._refund_ap(unit, ap_cost, game)
                ability.cooldown_remaining = 0
                return False, AbilityResult.FAILED_EXECUTION_ERROR
            
            print(f"{unit.name} uses {ability.name} on {target_pos}")
            return True, AbilityResult.SUCCESS
            
        except Exception as e:
            print(f"Error executing special ability {ability.name}: {e}")
            return False, AbilityResult.FAILED_EXECUTION_ERROR
    
    def _execute_ability_logic(self, unit, ability, target_pos, game) -> bool:
        """Execute the specific logic for an ability"""
        ability_name = ability.name
        
        # Check for registered custom handler
        if ability_name in self.execution_handlers:
            return self.execution_handlers[ability_name](unit, target_pos, game)
        
        # Check for unit-specific method
        method_name = f"_use_{ability_name.lower().replace(' ', '_')}"
        if hasattr(unit, method_name):
            method = getattr(unit, method_name)
            # Handle different method signatures
            try:
                return method(target_pos, game)
            except TypeError:
                try:
                    return method(target_pos)
                except TypeError:
                    return method()
        
        # Handle Summon ability specially
        if ability_name == "Summon":
            return ability.execute(target_pos, game)
        
        # Default: ability not implemented
        print(f"Warning: No implementation found for ability {ability_name}")
        return False
    
    def _refund_ap(self, unit, ap_cost: int, game):
        """Refund AP when an ability fails"""
        if game and hasattr(game, 'current_player_ap'):
            game.current_player_ap += ap_cost
            if hasattr(unit, 'has_acted_this_turn'):
                unit.has_acted_this_turn = False
            if hasattr(game, 'units_acted_this_turn'):
                game.units_acted_this_turn.discard(unit)
        else:
            unit.current_ap += ap_cost


class DamageCalculator:
    """Centralized damage calculation system"""
    
    @staticmethod
    def calculate_ability_damage(unit, ability_name: str, target_pos=None) -> float:
        """
        Calculate damage for an ability using the unified configuration system.
        
        Args:
            unit: The unit using the ability
            ability_name: Name of the ability
            target_pos: Target position (for position-dependent damage)
            
        Returns:
            Calculated damage value
        """
        from core.configuration_manager import get_config_manager
        
        config_manager = get_config_manager()
        unit_class_name = unit.__class__.__name__
        
        return config_manager.get_ability_damage(unit_class_name, ability_name)
    
    @staticmethod
    def calculate_basic_attack_damage(unit, target_pos=None) -> float:
        """Calculate basic attack damage for a unit"""
        return DamageCalculator.calculate_ability_damage(unit, "Attack", target_pos)


# Global ability executor instance
ability_executor = AbilityExecutor()


def get_ability_executor() -> AbilityExecutor:
    """Get the global ability executor instance"""
    return ability_executor


def execute_unit_ability(unit, ability_idx: int, target_pos, game=None) -> Tuple[bool, AbilityResult]:
    """
    Convenience function to execute a unit ability.
    This replaces the duplicated ability execution logic across unit classes.
    """
    return ability_executor.execute_ability(unit, ability_idx, target_pos, game)

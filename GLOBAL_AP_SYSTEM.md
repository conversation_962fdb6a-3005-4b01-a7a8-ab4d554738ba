# 🎯 Global AP System Documentation

## 📋 **Overview**

The game uses a **Global Action Point (AP) System** where each player has a shared pool of AP that all their units draw from, rather than individual AP per unit. This creates strategic resource management and prevents action spam.

---

## ⚡ **Core Mechanics**

### **🔄 AP Progression**
- **Turn 1:** Player 1 gets 1 AP, Player 2 gets 2 AP
- **Turn 2:** Player 1 gets 2 AP, Player 2 gets 3 AP  
- **Turn 3:** Player 1 gets 3 AP, Player 2 gets 4 AP
- **Turn 4+:** Both players get equal AP (4, 5, 6... up to 10 max)

### **🎯 Player 2 Advantage**
- **Early Game Bonus:** Player 2 gets +1 AP for turns 1-3
- **Balances first-player advantage** in tactical games
- **Equal footing** from turn 4 onwards

### **📊 AP Scaling**
```
Turn | P1 AP | P2 AP | Difference
-----|-------|-------|------------
  1  |   1   |   2   |    +1
  2  |   2   |   3   |    +1  
  3  |   3   |   4   |    +1
  4  |   4   |   4   |     0
  5  |   5   |   5   |     0
 ... |  ... |  ... |    ...
 10+ |  10   |  10   |     0
```

---

## 🎮 **Action Rules**

### **🚫 One Action Per Unit Per Turn**
- Each unit can perform **only ONE action** per turn
- Actions include: Move, Basic Attack, or Special Ability
- **Cannot** move then attack, or use multiple abilities
- **Cannot** act again until next turn

### **💰 AP Costs**
- **Move:** Usually 1 AP
- **Basic Attack:** Usually 1-2 AP
- **Special Abilities:** 2-5 AP depending on power
- **Status Effects:** Chilled increases all costs by +1 AP

### **🔄 AP Management**
- **Shared Pool:** All units draw from same player AP pool
- **Strategic Choices:** Use AP on few powerful actions vs. many small actions
- **Resource Planning:** Save AP for crucial moments vs. spend early

---

## 🔧 **Implementation Details**

### **📁 Key Files**
- `game_state.py` - Contains global AP management methods
- `units_core.py` - Unit action validation and AP deduction
- `game_logic.py` - Turn progression and AP reset

### **🎯 Core Methods**

#### **Game State (game_state.py)**
```python
def can_unit_act(self, unit):
    """Check if unit can perform any action this turn"""
    
def spend_ap(self, ap_cost, unit):
    """Deduct AP and mark unit as acted"""
    
def reset_turn(self):
    """Reset AP and clear acted units for new turn"""
```

#### **Unit Core (units_core.py)**
```python
def can_use_ability(self, ability_idx, game=None):
    """Check if unit can use ability (AP, cooldown, status)"""
    
def use_ability(self, ability_idx, target_pos, game=None):
    """Use ability with global AP system integration"""
    
def _spend_ap_for_ability(self, ability_idx, game=None):
    """Helper method for AP deduction and validation"""
```

---

## 🧪 **Testing & Verification**

### **✅ Verified Working**
- **AP Progression:** Correct scaling 1→10 with P2 bonus
- **One Action Rule:** Units cannot act twice per turn
- **Global Deduction:** AP properly deducted from player pool
- **Turn Reset:** AP and action tracking reset each turn
- **Status Integration:** Chilled increases AP costs correctly

### **🧪 Test Files**
- `test_global_ap_implementation.py` - Comprehensive AP system testing
- `test_complete_system.py` - Integration testing with movement/abilities
- `test_movement_fixes.py` - Movement system with AP integration

---

## 🎯 **Strategic Implications**

### **🤔 Player Decisions**
- **Resource Allocation:** Which units to activate each turn
- **Action Efficiency:** High-cost abilities vs. multiple small actions
- **Turn Planning:** Save AP for next turn vs. spend all now
- **Unit Coordination:** Combine unit actions for maximum effect

### **⚖️ Balance Benefits**
- **Prevents Spam:** Cannot use all abilities every turn
- **Strategic Depth:** Meaningful choices about resource usage
- **Turn Variety:** Different AP amounts create varied turn dynamics
- **Comeback Potential:** Lower AP early game allows tactical recovery

---

## 🔄 **Future Considerations**

### **🎛️ Configuration Integration**
- **AP Scaling:** Could be configurable (1→8, 1→12, etc.)
- **P2 Bonus:** Could be adjustable or removable
- **Ability Costs:** Should be fully configurable via balance sliders

### **🎭 Class Balance**
- **High-Cost Classes:** Mage abilities cost more AP
- **Low-Cost Classes:** Pawn/basic units cost less AP
- **Utility Balance:** Support abilities vs. damage abilities

### **🚀 Expansion Possibilities**
- **AP Overflow:** Unused AP carries to next turn (limited)
- **AP Abilities:** Abilities that generate or steal AP
- **Dynamic Costs:** AP costs that change based on game state

---

## 📊 **Current Status**

### **✅ Fully Implemented**
- Global AP pool management
- Progressive AP scaling (1→10)
- One action per unit enforcement
- Player 2 early game bonus
- Status effect integration (Chilled)
- Turn reset and tracking

### **🔧 Integration Status**
- **Core Units:** Hunter, Warrior, Rogue, Cleric, Mage ✅
- **New Units:** Warlock, Paladin, Druid, Bard (needs updating)
- **Summon Abilities:** 4/5 classes working ✅
- **Movement System:** Fully integrated ✅

### **⚠️ Known Issues**
- Some unit classes may still use old individual AP system
- Configuration changes may not affect AP costs properly
- Mage summon ability needs AP integration debugging

---

**The Global AP System is the core strategic foundation of the game, creating meaningful resource management and preventing action spam while maintaining tactical depth.** 🎮⚔️

#!/usr/bin/env python3
"""
New Ability-Based Configuration System
Each ability gets its own button with sliders for damage, AP cost, and cooldown
"""

import pygame
import json
from dataclasses import dataclass
from typing import Dict, List, Optional

@dataclass
class AbilityConfig:
    """Configuration for a single ability"""
    name: str
    unit_class: str
    damage: float
    ap_cost: int
    cooldown: int
    min_damage: float = 0
    max_damage: float = 10
    min_ap: int = 0
    max_ap: int = 10
    min_cooldown: int = 0
    max_cooldown: int = 10

@dataclass
class ClassConfig:
    """Configuration for a unit class"""
    name: str
    hp: int
    movement_range: int
    max_ap: int
    min_hp: int = 1
    max_hp: int = 20
    min_movement: int = 1
    max_movement: int = 5
    min_max_ap: int = 1
    max_max_ap: int = 10

class AbilityConfigSystem:
    """System for configuring individual abilities and classes with sliders"""

    def __init__(self):
        self.abilities: Dict[str, AbilityConfig] = {}
        self.classes: Dict[str, ClassConfig] = {}
        self.selected_ability: Optional[str] = None
        self.config_mode: str = "abilities"  # "abilities" or "class"
        self.font = None
        self.small_font = None

        # UI layout
        self.button_width = 150
        self.button_height = 35
        self.slider_width = 180
        self.slider_height = 20
        self.margin = 8

        # Colors
        self.bg_color = (40, 40, 40)
        self.button_color = (70, 70, 70)
        self.selected_button_color = (100, 150, 200)
        self.mode_button_color = (80, 120, 80)
        self.slider_track_color = (100, 100, 100)
        self.slider_handle_color = (200, 200, 200)
        self.text_color = (255, 255, 255)
        self.highlight_color = (100, 255, 100)

        self._initialize_abilities()
        self._initialize_classes()
    
    def _initialize_abilities(self):
        """Initialize all unit abilities with default values"""
        
        # Warrior abilities
        self.add_ability("Warrior Move", "Warrior", 0, 1, 0, 0, 0, 0, 3, 0, 0)
        self.add_ability("Warrior Attack", "Warrior", 2, 2, 0, 1, 5, 1, 4, 0, 3)
        self.add_ability("Charge", "Warrior", 3, 3, 2, 1, 6, 2, 5, 0, 5)
        self.add_ability("Shield Bash", "Warrior", 2, 2, 1, 1, 4, 1, 4, 0, 3)
        self.add_ability("Whirlwind", "Warrior", 4, 4, 3, 2, 7, 3, 6, 1, 5)
        
        # Mage abilities
        self.add_ability("Mage Move", "Mage", 0, 1, 0, 0, 0, 0, 3, 0, 0)
        self.add_ability("Mage Attack", "Mage", 1, 1, 0, 1, 3, 1, 3, 0, 2)
        self.add_ability("Fireball", "Mage", 3, 4, 2, 2, 6, 3, 6, 1, 4)
        self.add_ability("Ice Spike", "Mage", 2, 3, 1, 1, 5, 2, 5, 0, 3)
        self.add_ability("Teleport", "Mage", 0, 2, 3, 0, 0, 1, 4, 2, 5)
        self.add_ability("Frost Nova", "Mage", 1, 3, 4, 0, 4, 2, 5, 2, 6)
        self.add_ability("Arcane Missile", "Mage", 3, 5, 2, 2, 6, 4, 7, 1, 4)
        
        # Cleric abilities
        self.add_ability("Cleric Move", "Cleric", 0, 1, 0, 0, 0, 0, 3, 0, 0)
        self.add_ability("Cleric Attack", "Cleric", 1, 1, 0, 1, 3, 1, 3, 0, 2)
        self.add_ability("Heal", "Cleric", 3, 2, 1, 1, 6, 1, 4, 0, 3)
        self.add_ability("Mass Heal", "Cleric", 2, 5, 3, 1, 5, 4, 7, 2, 5)
        self.add_ability("Cleanse", "Cleric", 0, 2, 2, 0, 0, 1, 4, 1, 4)
        self.add_ability("Sanctuary", "Cleric", 0, 3, 4, 0, 0, 2, 5, 3, 6)
        self.add_ability("Divine Protection", "Cleric", 0, 2, 3, 0, 0, 1, 4, 2, 5)
        self.add_ability("Holy Smite", "Cleric", 3, 3, 2, 2, 5, 2, 5, 1, 4)
        
        # Rogue abilities
        self.add_ability("Rogue Move", "Rogue", 0, 1, 0, 0, 0, 0, 3, 0, 0)
        self.add_ability("Rogue Attack", "Rogue", 2, 1, 0, 1, 4, 1, 3, 0, 2)
        self.add_ability("Backstab", "Rogue", 4, 2, 2, 2, 7, 1, 4, 1, 4)
        self.add_ability("Fan of Knives", "Rogue", 2, 2, 2, 1, 4, 1, 4, 1, 4)
        self.add_ability("Assassination", "Rogue", 5, 3, 3, 3, 8, 2, 5, 2, 5)
        
        # Hunter abilities
        self.add_ability("Hunter Move", "Hunter", 0, 1, 0, 0, 0, 0, 3, 0, 0)
        self.add_ability("Hunter Attack", "Hunter", 2, 2, 0, 1, 4, 1, 4, 0, 3)
        self.add_ability("Multishot", "Hunter", 2, 3, 1, 1, 4, 2, 5, 0, 3)
        self.add_ability("Triple Shot", "Hunter", 3, 4, 2, 2, 5, 3, 6, 1, 4)
        self.add_ability("Piercing Shot", "Hunter", 3, 3, 2, 2, 5, 2, 5, 1, 4)
        self.add_ability("Crippling Shot", "Hunter", 2, 2, 1, 1, 4, 1, 4, 0, 3)

    def _initialize_classes(self):
        """Initialize class configurations with default values"""
        self.classes["Warrior"] = ClassConfig("Warrior", 12, 2, 4, 8, 20, 1, 4, 3, 8)
        self.classes["Mage"] = ClassConfig("Mage", 8, 2, 4, 5, 15, 1, 3, 3, 8)
        self.classes["Cleric"] = ClassConfig("Cleric", 10, 1, 4, 6, 18, 1, 3, 3, 8)
        self.classes["Rogue"] = ClassConfig("Rogue", 9, 3, 4, 6, 16, 2, 5, 3, 8)
        self.classes["Hunter"] = ClassConfig("Hunter", 10, 3, 4, 7, 17, 2, 5, 3, 8)
    
    def add_ability(self, name: str, unit_class: str, damage: float, ap_cost: int, cooldown: int,
                   min_damage: float, max_damage: float, min_ap: int, max_ap: int, 
                   min_cooldown: int, max_cooldown: int):
        """Add an ability configuration"""
        self.abilities[name] = AbilityConfig(
            name=name,
            unit_class=unit_class,
            damage=damage,
            ap_cost=ap_cost,
            cooldown=cooldown,
            min_damage=min_damage,
            max_damage=max_damage,
            min_ap=min_ap,
            max_ap=max_ap,
            min_cooldown=min_cooldown,
            max_cooldown=max_cooldown
        )
    
    def get_abilities_by_class(self, unit_class: str) -> List[AbilityConfig]:
        """Get all abilities for a specific unit class"""
        return [ability for ability in self.abilities.values() 
                if ability.unit_class == unit_class]
    
    def update_ability_value(self, ability_name: str, property_name: str, value: float):
        """Update a specific property of an ability"""
        if ability_name in self.abilities:
            ability = self.abilities[ability_name]
            
            if property_name == "damage":
                ability.damage = max(ability.min_damage, min(ability.max_damage, value))
            elif property_name == "ap_cost":
                ability.ap_cost = max(ability.min_ap, min(ability.max_ap, int(value)))
            elif property_name == "cooldown":
                ability.cooldown = max(ability.min_cooldown, min(ability.max_cooldown, int(value)))
    
    def render_ui(self, screen: pygame.Surface, x: int, y: int, selected_unit_class: str):
        """Render the ability configuration UI"""
        if not self.font:
            self.font = pygame.font.Font(None, 24)
            self.small_font = pygame.font.Font(None, 18)

        current_y = y

        # Title
        title = f"{selected_unit_class} Configuration"
        title_surf = self.font.render(title, True, self.text_color)
        screen.blit(title_surf, (x, current_y))
        current_y += 35

        # Mode selection buttons
        mode_buttons = {
            "Class Stats": pygame.Rect(x, current_y, 100, 30),
            "Abilities": pygame.Rect(x + 110, current_y, 100, 30)
        }

        for mode_name, button_rect in mode_buttons.items():
            mode_key = "class" if mode_name == "Class Stats" else "abilities"
            color = self.selected_button_color if self.config_mode == mode_key else self.mode_button_color

            pygame.draw.rect(screen, color, button_rect)
            pygame.draw.rect(screen, self.text_color, button_rect, 1)

            text_surf = self.small_font.render(mode_name, True, self.text_color)
            text_rect = text_surf.get_rect(center=button_rect.center)
            screen.blit(text_surf, text_rect)

        current_y += 40
        
        if self.config_mode == "class":
            self._render_class_config(screen, x, current_y, selected_unit_class)
        else:
            self._render_ability_config(screen, x, current_y, selected_unit_class)

    def _render_class_config(self, screen: pygame.Surface, x: int, y: int, selected_unit_class: str):
        """Render class-level configuration"""
        if selected_unit_class not in self.classes:
            return

        class_config = self.classes[selected_unit_class]
        slider_x = x + 200
        current_y = y

        # Class stats sliders
        self._render_slider(screen, slider_x, current_y,
                          f"HP: {class_config.hp}",
                          class_config.hp, class_config.min_hp, class_config.max_hp)
        current_y += 50

        self._render_slider(screen, slider_x, current_y,
                          f"Movement: {class_config.movement_range}",
                          class_config.movement_range, class_config.min_movement, class_config.max_movement)
        current_y += 50

        self._render_slider(screen, slider_x, current_y,
                          f"Max AP: {class_config.max_ap}",
                          class_config.max_ap, class_config.min_max_ap, class_config.max_max_ap)
        current_y += 50

        # Apply button
        apply_rect = pygame.Rect(slider_x, current_y + 20, 100, 30)
        pygame.draw.rect(screen, (50, 150, 50), apply_rect)
        pygame.draw.rect(screen, self.text_color, apply_rect, 2)

        apply_text = self.small_font.render("Apply", True, self.text_color)
        apply_text_rect = apply_text.get_rect(center=apply_rect.center)
        screen.blit(apply_text, apply_text_rect)

    def _render_ability_config(self, screen: pygame.Surface, x: int, y: int, selected_unit_class: str):
        """Render ability configuration"""
        # Get abilities for selected class
        abilities = self.get_abilities_by_class(selected_unit_class)

        # Ability buttons (left side)
        button_x = x
        button_y = y

        for i, ability in enumerate(abilities):
            # Ability button
            button_rect = pygame.Rect(button_x, button_y, self.button_width, self.button_height)

            # Button color based on selection
            if self.selected_ability == ability.name:
                button_color = self.selected_button_color
            else:
                button_color = self.button_color

            pygame.draw.rect(screen, button_color, button_rect)
            pygame.draw.rect(screen, self.text_color, button_rect, 1)

            # Button text
            text_surf = self.small_font.render(ability.name, True, self.text_color)
            text_rect = text_surf.get_rect(center=button_rect.center)
            screen.blit(text_surf, text_rect)

            button_y += self.button_height + self.margin
        
        # Sliders (right side) - only show if ability is selected
        if self.selected_ability and self.selected_ability in self.abilities:
            ability = self.abilities[self.selected_ability]
            slider_x = button_x + self.button_width + 30
            slider_y = y

            # Damage slider
            if ability.max_damage > 0:  # Only show if ability can do damage
                self._render_slider(screen, slider_x, slider_y,
                                  f"Damage: {ability.damage:.1f}",
                                  ability.damage, ability.min_damage, ability.max_damage)
                slider_y += 50

            # AP Cost slider
            self._render_slider(screen, slider_x, slider_y,
                              f"AP Cost: {ability.ap_cost}",
                              ability.ap_cost, ability.min_ap, ability.max_ap)
            slider_y += 50

            # Cooldown slider
            if ability.max_cooldown > 0:  # Only show if ability has cooldown
                self._render_slider(screen, slider_x, slider_y,
                                  f"Cooldown: {ability.cooldown}",
                                  ability.cooldown, ability.min_cooldown, ability.max_cooldown)
                slider_y += 50

            # Apply button
            apply_rect = pygame.Rect(slider_x, slider_y + 20, 100, 30)
            pygame.draw.rect(screen, (50, 150, 50), apply_rect)
            pygame.draw.rect(screen, self.text_color, apply_rect, 2)

            apply_text = self.small_font.render("Apply", True, self.text_color)
            apply_text_rect = apply_text.get_rect(center=apply_rect.center)
            screen.blit(apply_text, apply_text_rect)
    
    def _render_slider(self, screen: pygame.Surface, x: int, y: int,
                      label: str, value: float, min_val: float, max_val: float):
        """Render a single slider"""
        # Label
        label_surf = self.small_font.render(label, True, self.text_color)
        screen.blit(label_surf, (x, y))

        # Slider track
        track_rect = pygame.Rect(x, y + 25, self.slider_width, 8)
        pygame.draw.rect(screen, self.slider_track_color, track_rect)
        pygame.draw.rect(screen, self.text_color, track_rect, 1)

        # Slider handle
        if max_val > min_val:
            value_ratio = (value - min_val) / (max_val - min_val)
            handle_x = track_rect.x + value_ratio * (track_rect.width - 10)
            handle_rect = pygame.Rect(handle_x, track_rect.y - 3, 10, 14)
            pygame.draw.rect(screen, self.slider_handle_color, handle_rect)
            pygame.draw.rect(screen, self.text_color, handle_rect, 1)
    
    def handle_click(self, pos: tuple, selected_unit_class: str, ui_start_y: int = 140) -> bool:
        """Handle mouse clicks on the UI"""
        mouse_x, mouse_y = pos

        # Check mode buttons
        mode_buttons = {
            "class": pygame.Rect(50, ui_start_y - 40, 100, 30),
            "abilities": pygame.Rect(160, ui_start_y - 40, 100, 30)
        }

        for mode_key, button_rect in mode_buttons.items():
            if button_rect.collidepoint(mouse_x, mouse_y):
                self.config_mode = mode_key
                self.selected_ability = None  # Reset selection when switching modes
                return True

        if self.config_mode == "abilities":
            return self._handle_ability_click(pos, selected_unit_class, ui_start_y)
        else:
            return self._handle_class_click(pos, selected_unit_class, ui_start_y)

    def _handle_ability_click(self, pos: tuple, selected_unit_class: str, ui_start_y: int) -> bool:
        """Handle clicks for ability configuration"""
        mouse_x, mouse_y = pos

        # Check ability buttons
        abilities = self.get_abilities_by_class(selected_unit_class)
        button_y = ui_start_y  # Starting Y position for buttons
        
        for ability in abilities:
            button_rect = pygame.Rect(50, button_y, self.button_width, self.button_height)
            
            if button_rect.collidepoint(mouse_x, mouse_y):
                self.selected_ability = ability.name
                return True
            
            button_y += self.button_height + self.margin
        
        # Check sliders (if ability is selected)
        if self.selected_ability and self.selected_ability in self.abilities:
            ability = self.abilities[self.selected_ability]
            slider_x = 50 + self.button_width + 30
            slider_y = ui_start_y
            
            # Check damage slider
            if ability.max_damage > 0:
                if self._handle_slider_click(mouse_x, mouse_y, slider_x, slider_y + 25,
                                           ability.damage, ability.min_damage, ability.max_damage):
                    new_value = self._calculate_slider_value(mouse_x, slider_x,
                                                           ability.min_damage, ability.max_damage)
                    self.update_ability_value(ability.name, "damage", new_value)
                    return True
                slider_y += 50

            # Check AP cost slider
            if self._handle_slider_click(mouse_x, mouse_y, slider_x, slider_y + 25,
                                       ability.ap_cost, ability.min_ap, ability.max_ap):
                new_value = self._calculate_slider_value(mouse_x, slider_x,
                                                       ability.min_ap, ability.max_ap)
                self.update_ability_value(ability.name, "ap_cost", new_value)
                return True
            slider_y += 50

            # Check cooldown slider
            if ability.max_cooldown > 0:
                if self._handle_slider_click(mouse_x, mouse_y, slider_x, slider_y + 25,
                                           ability.cooldown, ability.min_cooldown, ability.max_cooldown):
                    new_value = self._calculate_slider_value(mouse_x, slider_x,
                                                           ability.min_cooldown, ability.max_cooldown)
                    self.update_ability_value(ability.name, "cooldown", new_value)
                    return True

        return False

    def _handle_class_click(self, pos: tuple, selected_unit_class: str, ui_start_y: int) -> bool:
        """Handle clicks for class configuration"""
        mouse_x, mouse_y = pos

        if selected_unit_class not in self.classes:
            return False

        class_config = self.classes[selected_unit_class]
        slider_x = 250
        slider_y = ui_start_y

        # Check HP slider
        if self._handle_slider_click(mouse_x, mouse_y, slider_x, slider_y + 25,
                                   class_config.hp, class_config.min_hp, class_config.max_hp):
            new_value = self._calculate_slider_value(mouse_x, slider_x,
                                                   class_config.min_hp, class_config.max_hp)
            class_config.hp = int(new_value)
            return True
        slider_y += 50

        # Check movement slider
        if self._handle_slider_click(mouse_x, mouse_y, slider_x, slider_y + 25,
                                   class_config.movement_range, class_config.min_movement, class_config.max_movement):
            new_value = self._calculate_slider_value(mouse_x, slider_x,
                                                   class_config.min_movement, class_config.max_movement)
            class_config.movement_range = int(new_value)
            return True
        slider_y += 50

        # Check max AP slider
        if self._handle_slider_click(mouse_x, mouse_y, slider_x, slider_y + 25,
                                   class_config.max_ap, class_config.min_max_ap, class_config.max_max_ap):
            new_value = self._calculate_slider_value(mouse_x, slider_x,
                                                   class_config.min_max_ap, class_config.max_max_ap)
            class_config.max_ap = int(new_value)
            return True

        return False
    
    def _handle_slider_click(self, mouse_x: int, mouse_y: int, slider_x: int, slider_y: int,
                           value: float, min_val: float, max_val: float) -> bool:
        """Check if a slider was clicked"""
        track_rect = pygame.Rect(slider_x, slider_y, self.slider_width, 8)
        return track_rect.collidepoint(mouse_x, mouse_y)
    
    def _calculate_slider_value(self, mouse_x: int, slider_x: int, 
                              min_val: float, max_val: float) -> float:
        """Calculate slider value from mouse position"""
        click_ratio = max(0, min(1, (mouse_x - slider_x) / self.slider_width))
        return min_val + click_ratio * (max_val - min_val)
    
    def save_config(self, filename: str = "ability_config.json"):
        """Save current configuration to file"""
        config_data = {}
        for name, ability in self.abilities.items():
            config_data[name] = {
                "damage": ability.damage,
                "ap_cost": ability.ap_cost,
                "cooldown": ability.cooldown
            }
        
        try:
            with open(filename, 'w') as f:
                json.dump(config_data, f, indent=2)
            return True
        except Exception as e:
            print(f"Error saving config: {e}")
            return False
    
    def load_config(self, filename: str = "ability_config.json"):
        """Load configuration from file"""
        try:
            with open(filename, 'r') as f:
                config_data = json.load(f)
            
            for name, values in config_data.items():
                if name in self.abilities:
                    ability = self.abilities[name]
                    ability.damage = values.get("damage", ability.damage)
                    ability.ap_cost = values.get("ap_cost", ability.ap_cost)
                    ability.cooldown = values.get("cooldown", ability.cooldown)
            
            return True
        except Exception as e:
            print(f"Error loading config: {e}")
            return False

def test_ability_config_system():
    """Test the ability configuration system"""
    pygame.init()
    
    print("TESTING ABILITY CONFIGURATION SYSTEM")
    print("=" * 40)
    
    config_system = AbilityConfigSystem()
    
    # Test ability retrieval
    warrior_abilities = config_system.get_abilities_by_class("Warrior")
    mage_abilities = config_system.get_abilities_by_class("Mage")
    
    print(f"Warrior abilities: {len(warrior_abilities)}")
    for ability in warrior_abilities:
        print(f"  {ability.name}: {ability.damage} dmg, {ability.ap_cost} AP, {ability.cooldown} CD")
    
    print(f"\nMage abilities: {len(mage_abilities)}")
    for ability in mage_abilities:
        print(f"  {ability.name}: {ability.damage} dmg, {ability.ap_cost} AP, {ability.cooldown} CD")
    
    # Test value updates
    print(f"\nTesting value updates:")
    original_fireball = config_system.abilities["Fireball"]
    print(f"Original Fireball: {original_fireball.damage} dmg, {original_fireball.ap_cost} AP")
    
    config_system.update_ability_value("Fireball", "damage", 5.0)
    config_system.update_ability_value("Fireball", "ap_cost", 3)
    
    updated_fireball = config_system.abilities["Fireball"]
    print(f"Updated Fireball: {updated_fireball.damage} dmg, {updated_fireball.ap_cost} AP")
    
    # Test save/load
    print(f"\nTesting save/load:")
    save_success = config_system.save_config("test_config.json")
    print(f"Save success: {save_success}")
    
    print("\n✅ Ability configuration system working!")
    print("Ready to integrate with game UI.")

if __name__ == "__main__":
    test_ability_config_system()

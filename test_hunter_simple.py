#!/usr/bin/env python3
"""
Simple test for Hunter abilities
"""

import pygame
from game_state import Game
from units.hunter import Hunter
from units.warrior import Warrior

def test_hunter_simple():
    """Simple test for Hunter abilities"""
    pygame.init()
    
    print("🏹 SIMPLE HUNTER TEST 🏹")
    print("=" * 30)
    
    # Create Hunter and check abilities
    hunter = Hunter(1)
    print("Hunter abilities:")
    for i, ability in enumerate(hunter.abilities):
        print(f"  {i}: {ability.name}")
    
    # Test basic setup
    game = Game()
    enemy = Warrior(2)
    
    hunter.position = (4, 4)
    enemy.position = (3, 5)
    
    hunter.board = game.board
    game.board.units[(4, 4)] = hunter
    game.board.units[(3, 5)] = enemy
    
    print(f"\nHunter at: {hunter.position}")
    print(f"Enemy at: {enemy.position}")
    print(f"Enemy HP: {enemy.health}")
    
    # Test Multishot (index 4)
    print(f"\n🎯 Testing Multishot (index 4)...")
    targets = hunter.get_ability_targets(4, game.board)
    print(f"Multishot targets: {targets}")
    
    if targets:
        result = hunter.use_ability(4, targets[0], game)
        print(f"✅ Multishot result: {result}")
    
    print("\n✅ Simple test completed!")

if __name__ == "__main__":
    test_hunter_simple()

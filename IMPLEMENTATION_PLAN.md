# Phased Implementation Plan (Python/Pygame Adaptation)

**Phase 1: Core Foundation**
*   Set up Pygame project structure.
*   Define core data structures as Python classes or data containers (e.g., `UnitData`, `SkillData`, `PassiveData`, `StatusEffectData`, potentially loading from JSON/CSV).
*   Implement Grid logic (`GameBoard` class) and basic visual representation (drawing grid lines).
*   Implement Turn Manager (`TurnManager` class) and AP System (`APSystem` class).
*   Basic UI for player/AP display (using Pygame drawing functions and custom UI element classes).

**Phase 2: Placing Phase**
*   Implement game state logic for unit placement (e.g., a 'PLACING' state in the `Game` class).
*   Create basic UI elements (custom Pygame classes) for selecting unit types.
*   Handle player input (mouse clicks) to place unit representations (Pygame sprites or drawn shapes) onto the 2D grid.
*   Implement logic to transition to the main game state ('PLAYING').

**Phase 3: Core Movement**
*   Implement unit selection (detecting clicks on unit sprites).
*   Implement `MovementSystem` logic (Python class/module) calculating valid destinations based on unit rules and grid state. Handle specific movement patterns.
*   Visualize valid moves (e.g., highlighting grid cells).
*   Handle movement execution (updating unit's position on the grid and screen) and AP deduction.

**Phase 4: Basic Attacks**
*   Implement `AttackSystem` logic (Python class/module) calculating valid targets based on unit rules (Range, Pattern, LoS - using 2D grid checks).
*   Implement `DamageSystem` logic (applying HP changes, checking for defeat).
*   Handle attack input, target selection visualization, AP cost, damage application, and unit defeat (removing unit, updating game state).

**Phase 5: Core Systems Backend Implementation**
*   Implement systems for managing Status Effects (`StatusEffectSystem` class: application, duration, effect querying), Knockback (`KnockbackSystem` class: movement, collision checks), Cooldowns (`CooldownSystem` class: data tracking, turn updates), and Passives (`PassiveSystem` class: integration points).

**Phase 6: Skill Framework & Targeting Primitives**
*   Build the UI framework (e.g., clickable icons/buttons) for displaying and selecting a unit's active skills, checking AP/Cooldowns visually.
*   Create reusable Python functions within a `TargetingSystem` for calculating targets/AoE based on defined primitives (Single, Line, CustomDiagonalArea, AdjacentSweep, Self, ManualSequence) on the 2D grid.
*   Implement robust AoE/target highlighting. Handle path/direction selection input.

**Phase 7: Implement Specific Skills (Large Phase)**
*   Write Python functions implementing the specific effect logic for all 15 defined Special Skills.
*   Connect these functions to the Skill Framework, use the `TargetingSystem`, and call the core backend systems (Damage, Status, Knockback, Cooldown, etc.).

**Phase 8: King & Win Condition**
*   Ensure King unit uses its defined stats/actions.
*   Implement the win condition check within the unit defeat logic. Update game state and display winner UI.

**Phase 9: Customization Logic Backend (Future Prototype Step)**
*   Define Python data structures for `CustomStatsData`, `MasterSkillPool`, `PlayerLoadoutData`.
*   Implement saving/loading using Python's file I/O (e.g., JSON files).
*   Integrate loading logic into game/unit setup.

**Phase 10: Customization UI Frontend (Future Prototype Step)**
*   Build main menu screens using custom Pygame UI classes for stat and skill loadout customization.
*   Connect UI interactions to the backend customization logic.

**Phase 11: Testing & Iteration**
*   Focus on manual hotseat testing, identifying bugs in Python logic, verifying mechanics, and initial balancing. 
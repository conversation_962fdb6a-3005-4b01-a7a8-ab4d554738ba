# Status Effects System
# Comprehensive system for managing unit status effects with proper turn-based duration

from enum import Enum
from dataclasses import dataclass
from typing import Dict, List, Optional

class StatusType(Enum):
    """Enumeration of all possible status effects"""
    STUNNED = "stunned"           # Cannot move or use abilities
    CRIPPLED = "crippled"         # Cannot move but can use abilities
    CHILLED = "chilled"           # Abilities cost +1 AP
    POISONED = "poisoned"         # Takes damage at start of turn
    FROZEN = "frozen"             # Cannot act, takes +50% damage from fire
    SLOWED = "slowed"             # Movement range reduced by half
    BURNING = "burning"           # Takes fire damage at start of turn
    BLESSED = "blessed"           # Heals at start of turn
    SHIELDED = "shielded"         # Takes 50% less damage
    INVISIBLE = "invisible"       # Cannot be targeted by single-target abilities
    MARKED = "marked"             # Takes +1 damage from all sources
    REGENERATING = "regenerating" # Heals at start of turn
    WEAKENED = "weakened"         # Deals 50% less damage
    ENRAGED = "enraged"           # Deals +1 damage but takes +1 damage

@dataclass
class StatusEffect:
    """Represents a single status effect on a unit"""
    status_type: StatusType
    duration: int  # Number of the unit's owner's turns remaining
    intensity: int = 1  # Strength of the effect (e.g., damage per turn)
    source: Optional[str] = None  # What caused this effect (for display)
    
    def __str__(self):
        if self.duration > 0:
            return f"{self.status_type.value.title()} ({self.duration}t)"
        return self.status_type.value.title()

class StatusEffectManager:
    """Manages all status effects for a unit"""
    
    def __init__(self, unit):
        self.unit = unit
        self.effects: Dict[StatusType, StatusEffect] = {}
    
    def add_effect(self, status_type: StatusType, duration: int, intensity: int = 1, source: str = None):
        """Add or refresh a status effect"""
        if status_type in self.effects:
            # If effect already exists, take the longer duration
            existing = self.effects[status_type]
            existing.duration = max(existing.duration, duration)
            existing.intensity = max(existing.intensity, intensity)
        else:
            self.effects[status_type] = StatusEffect(status_type, duration, intensity, source)
        
        print(f"{self.unit.name} is now {status_type.value} for {duration} turns")
    
    def remove_effect(self, status_type: StatusType):
        """Remove a specific status effect"""
        if status_type in self.effects:
            del self.effects[status_type]
            print(f"{self.unit.name} is no longer {status_type.value}")
    
    def has_effect(self, status_type: StatusType) -> bool:
        """Check if unit has a specific status effect"""
        return status_type in self.effects and self.effects[status_type].duration > 0
    
    def get_effect(self, status_type: StatusType) -> Optional[StatusEffect]:
        """Get a specific status effect"""
        return self.effects.get(status_type)
    
    def get_all_effects(self) -> List[StatusEffect]:
        """Get all active status effects"""
        return [effect for effect in self.effects.values() if effect.duration > 0]
    
    def tick_turn_start(self, game=None):
        """Process status effects at the start of the unit's turn"""
        effects_to_remove = []
        
        for status_type, effect in self.effects.items():
            if effect.duration <= 0:
                continue
                
            # Apply start-of-turn effects
            if status_type == StatusType.POISONED:
                damage = effect.intensity
                print(f"{self.unit.name} takes {damage} poison damage")
                self.unit.take_damage(damage, attacker=None, game=game)
                
            elif status_type == StatusType.BURNING:
                damage = effect.intensity
                print(f"{self.unit.name} takes {damage} fire damage from burning")
                self.unit.take_damage(damage, attacker=None, game=game)
                
            elif status_type == StatusType.BLESSED or status_type == StatusType.REGENERATING:
                heal_amount = effect.intensity
                old_health = self.unit.health
                self.unit.health = min(self.unit.max_health, self.unit.health + heal_amount)
                actual_heal = self.unit.health - old_health
                if actual_heal > 0:
                    print(f"{self.unit.name} heals {actual_heal} HP from {status_type.value}")
            
            # Tick down duration
            effect.duration -= 1
            if effect.duration <= 0:
                effects_to_remove.append(status_type)
        
        # Remove expired effects
        for status_type in effects_to_remove:
            self.remove_effect(status_type)
    
    def can_move(self) -> bool:
        """Check if unit can move based on status effects"""
        return not (self.has_effect(StatusType.STUNNED) or
                   self.has_effect(StatusType.CRIPPLED) or
                   self.has_effect(StatusType.FROZEN))
    
    def can_use_abilities(self) -> bool:
        """Check if unit can use abilities based on status effects"""
        return not (self.has_effect(StatusType.STUNNED) or 
                   self.has_effect(StatusType.FROZEN))
    
    def can_be_targeted(self) -> bool:
        """Check if unit can be targeted by single-target abilities"""
        return not self.has_effect(StatusType.INVISIBLE)
    
    def get_ap_cost_modifier(self) -> int:
        """Get additional AP cost from status effects"""
        modifier = 0
        if self.has_effect(StatusType.CHILLED):
            modifier += 1
        return modifier
    
    def get_damage_taken_modifier(self) -> float:
        """Get damage taken multiplier from status effects"""
        multiplier = 1.0
        
        if self.has_effect(StatusType.SHIELDED):
            multiplier *= 0.5  # 50% less damage
            
        if self.has_effect(StatusType.MARKED):
            multiplier += 0.5  # +50% damage taken
            
        if self.has_effect(StatusType.ENRAGED):
            multiplier += 0.5  # +50% damage taken
            
        if self.has_effect(StatusType.FROZEN):
            # Frozen units take extra damage from fire attacks
            # This would need to be checked by the attacker
            pass
            
        return multiplier
    
    def get_damage_dealt_modifier(self) -> float:
        """Get damage dealt multiplier from status effects"""
        multiplier = 1.0
        
        if self.has_effect(StatusType.WEAKENED):
            multiplier *= 0.5  # 50% less damage
            
        if self.has_effect(StatusType.ENRAGED):
            multiplier += 0.5  # +50% damage dealt
            
        return multiplier
    
    def get_movement_range_modifier(self) -> float:
        """Get movement range multiplier from status effects"""
        multiplier = 1.0
        
        if self.has_effect(StatusType.SLOWED):
            multiplier *= 0.5  # Half movement range
            
        return multiplier
    
    def get_status_display(self) -> List[str]:
        """Get list of status effect strings for UI display"""
        return [str(effect) for effect in self.get_all_effects()]

# Convenience functions for common status effects
def apply_stun(unit, duration: int, source: str = None):
    """Apply stun effect to a unit"""
    unit.status_manager.add_effect(StatusType.STUNNED, duration, source=source)

def apply_cripple(unit, duration: int, source: str = None):
    """Apply cripple effect to a unit"""
    unit.status_manager.add_effect(StatusType.CRIPPLED, duration, source=source)

def apply_chill(unit, duration: int, source: str = None):
    """Apply chill effect to a unit"""
    unit.status_manager.add_effect(StatusType.CHILLED, duration, source=source)

def apply_poison(unit, duration: int, damage_per_turn: int = 1, source: str = None):
    """Apply poison effect to a unit"""
    unit.status_manager.add_effect(StatusType.POISONED, duration, damage_per_turn, source=source)

def apply_freeze(unit, duration: int, source: str = None):
    """Apply freeze effect to a unit"""
    unit.status_manager.add_effect(StatusType.FROZEN, duration, source=source)

def apply_slow(unit, duration: int, source: str = None):
    """Apply slow effect to a unit"""
    unit.status_manager.add_effect(StatusType.SLOWED, duration, source=source)

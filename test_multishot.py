#!/usr/bin/env python3
"""
Test script for the new Multishot ability
"""

import pygame
from game_state import Game
from units.hunter import Hunter
from units.warrior import Warrior
from units.pawn import Pawn

def test_multishot_ability():
    """Test the new Multishot ability"""
    pygame.init()
    
    print("🏹 MULTISHOT ABILITY TEST 🏹")
    print("=" * 40)
    
    # Test 1: Multishot North (fires N, NE, NW)
    print("\n📋 TEST 1: Multishot North Direction")
    print("-" * 35)
    
    game = Game()
    hunter = Hunter(1)
    enemies = [Warrior(2) for _ in range(3)]
    
    # Set up <PERSON> in center with enemies in N, NE, NW positions
    hunter.position = (4, 4)
    enemy_positions = [(3, 4), (3, 5), (3, 3)]  # N, NE, NW from hunter
    
    hunter.board = game.board
    game.board.units[(4, 4)] = hunter
    
    for i, enemy in enumerate(enemies):
        enemy.position = enemy_positions[i]
        game.board.units[enemy_positions[i]] = enemy
        print(f"Enemy {i+1} ({enemy.name}) at {enemy.position} - HP: {enemy.health}")
    
    print(f"\nHunter at: {hunter.position}")
    print(f"Enemies at: {enemy_positions} (N, NE, NW)")
    
    # Get Multishot targets (should be orthogonal directions)
    targets = hunter.get_ability_targets(5, game.board)  # Multishot is index 5
    print(f"Available Multishot targets: {targets}")
    
    # Execute Multishot towards North
    target_north = (3, 4)  # North direction
    print(f"\n🎯 Executing Multishot towards North {target_north}...")
    result = hunter.use_ability(5, target_north, game)
    print(f"✅ Multishot result: {result}")
    
    # Check enemy health after Multishot
    print(f"\nEnemy health after Multishot North:")
    for i, pos in enumerate(enemy_positions):
        if pos in game.board.units:
            enemy = game.board.units[pos]
            print(f"  Enemy {i+1} at {pos}: {enemy.health} HP ({'alive' if enemy.is_alive() else 'dead'})")
        else:
            print(f"  Enemy {i+1} at {pos}: REMOVED (killed)")
    
    # Test 2: Multishot East (fires E, NE, SE)
    print("\n\n📋 TEST 2: Multishot East Direction")
    print("-" * 35)
    
    game2 = Game()
    hunter2 = Hunter(1)
    enemies2 = [Pawn(2) for _ in range(3)]  # Use Pawns for easier killing
    
    # Set up Hunter in center with enemies in E, NE, SE positions
    hunter2.position = (4, 4)
    enemy_positions2 = [(4, 5), (3, 5), (5, 5)]  # E, NE, SE from hunter
    
    hunter2.board = game2.board
    game2.board.units[(4, 4)] = hunter2
    
    for i, enemy in enumerate(enemies2):
        enemy.position = enemy_positions2[i]
        game2.board.units[enemy_positions2[i]] = enemy
        print(f"Enemy {i+1} ({enemy.name}) at {enemy.position} - HP: {enemy.health}")
    
    print(f"\nHunter at: {hunter2.position}")
    print(f"Enemies at: {enemy_positions2} (E, NE, SE)")
    
    # Execute Multishot towards East
    target_east = (4, 5)  # East direction
    print(f"\n🎯 Executing Multishot towards East {target_east}...")
    result2 = hunter2.use_ability(5, target_east, game2)
    print(f"✅ Multishot result: {result2}")
    
    # Check enemy health after Multishot
    print(f"\nEnemy health after Multishot East:")
    for i, pos in enumerate(enemy_positions2):
        if pos in game2.board.units:
            enemy = game2.board.units[pos]
            print(f"  Enemy {i+1} at {pos}: {enemy.health} HP ({'alive' if enemy.is_alive() else 'dead'})")
        else:
            print(f"  Enemy {i+1} at {pos}: REMOVED (killed)")
    
    # Test 3: Multishot with some enemies missing
    print("\n\n📋 TEST 3: Multishot with Partial Coverage")
    print("-" * 40)
    
    game3 = Game()
    hunter3 = Hunter(1)
    enemy3 = Warrior(2)  # Only one enemy
    
    # Set up Hunter with only one enemy in South direction
    hunter3.position = (4, 4)
    enemy3.position = (5, 4)  # South from hunter
    
    hunter3.board = game3.board
    game3.board.units[(4, 4)] = hunter3
    game3.board.units[(5, 4)] = enemy3
    
    print(f"Hunter at: {hunter3.position}")
    print(f"Single enemy at: {enemy3.position} (South)")
    print(f"Enemy HP: {enemy3.health}")
    
    # Execute Multishot towards South (should fire S, SE, SW)
    target_south = (5, 4)  # South direction
    print(f"\n🎯 Executing Multishot towards South {target_south}...")
    print("Expected: Fires South (hits enemy), Southeast (misses), Southwest (misses)")
    result3 = hunter3.use_ability(5, target_south, game3)
    print(f"✅ Multishot result: {result3}")
    
    print(f"\nEnemy health after Multishot South:")
    if (5, 4) in game3.board.units:
        enemy = game3.board.units[(5, 4)]
        print(f"  Enemy at (5, 4): {enemy.health} HP ({'alive' if enemy.is_alive() else 'dead'})")
    else:
        print(f"  Enemy at (5, 4): REMOVED (killed)")
    
    print("\n" + "=" * 40)
    print("🎉 MULTISHOT TESTS COMPLETED!")
    print("\n✅ Features Tested:")
    print("  • Orthogonal targeting (N, S, E, W)")
    print("  • 3-arrow spread (1 orthogonal + 2 diagonals)")
    print("  • Multiple enemy hits")
    print("  • Partial coverage scenarios")
    
    print("\n🎮 Multishot ability is working correctly!")

if __name__ == "__main__":
    test_multishot_ability()

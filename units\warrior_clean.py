import pygame
from units_core import Unit, SimpleAbility, MoveAbility, AttackAbility, SummonAbility
from game_config import GAME_CONFIG
import game_constants as const
import math

# Import unified systems
from core.configuration_manager import get_config_manager
from core.ability_system import DamageCalculator
from core.status_effects import StatusEffectType

class Warrior(Unit):
    """Warrior unit that moves and attacks orthogonally like a chess rook"""
    def __init__(self, player_id):
        # Initialize with unified configuration system
        config_manager = get_config_manager()
        warrior_config = config_manager.get_unit_config("Warrior")

        super().__init__(
            player_id,
            health=warrior_config.get("health", 7),
            max_health=warrior_config.get("health", 7)
        )
        self.name = "Warrior"
        self.max_ap = warrior_config.get("max_ap", 6)
        self.current_ap = warrior_config.get("max_ap", 6)
        self.board = None  # Will be set later

        # Warrior-specific state variables
        self.defensive_stance_active = False
        self.defensive_stance_until = 0  # Track when to deactivate
        self.riposte_active = False

        # Load image (placeholder for now)
        self.image = self._create_placeholder_image((180, 40, 40) if player_id == 2 else (0, 80, 180))

        # Abilities with unified configuration
        self.abilities = [
            MoveAbility(self),
            AttackAbility(self),
            SimpleAbility("Cleave Attack", config_manager.get_ability_ap_cost("Warrior", "Cleave Attack"), "Hit target and adjacent enemies", cooldown=1, owner=self),
            SimpleAbility("Shield Bash", config_manager.get_ability_ap_cost("Warrior", "Shield Bash"), "Deal damage and stun the target", cooldown=2, owner=self),
            SimpleAbility("Charge", config_manager.get_ability_ap_cost("Warrior", "Charge"), "Move up to 3 tiles and attack", cooldown=2, owner=self),
            SimpleAbility("Defensive Stance", config_manager.get_ability_ap_cost("Warrior", "Defensive Stance"), "Take 50% less damage until your next turn", cooldown=3, owner=self),
            SimpleAbility("Riposte", config_manager.get_ability_ap_cost("Warrior", "Riposte"), "Counter the next attack with 1 damage", cooldown=2, owner=self),
            SummonAbility(self, config_manager.get_ability_ap_cost("Warrior", "Summon"))
        ]

        # Configuration is automatically applied by the base Unit class
    
    def _create_placeholder_image(self, color):
        """Create a placeholder image with a sword symbol"""
        surf = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA)
        
        # Main circle
        pygame.draw.circle(surf, color, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2)
        pygame.draw.circle(surf, const.WHITE, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 2, 2)
        
        # Draw sword symbol
        sword_color = (220, 220, 220)
        # Handle
        pygame.draw.rect(surf, sword_color, (const.CELL_SIZE//2-3, const.CELL_SIZE//2+5, 6, 15))
        # Guard
        pygame.draw.rect(surf, sword_color, (const.CELL_SIZE//2-10, const.CELL_SIZE//2+5, 20, 4))
        # Blade
        pygame.draw.polygon(surf, sword_color, [
            (const.CELL_SIZE//2-3, const.CELL_SIZE//2+5),
            (const.CELL_SIZE//2+3, const.CELL_SIZE//2+5),
            (const.CELL_SIZE//2, const.CELL_SIZE//2-15)
        ])
        
        return surf
    
    def get_valid_moves(self, board):
        """Warriors move orthogonally like a rook in chess"""
        self.board = board
        valid_moves = []
        
        # Check if unit is immobilized or stunned
        if self.has_status('Immobilized') or self.has_status('Stunned'):
            return []
        
        # Get current position
        row, col = self.position
        
        # Check in all four orthogonal directions (up, right, down, left)
        directions = [(-1, 0), (0, 1), (1, 0), (0, -1)]
        
        for dr, dc in directions:
            # Check each step in this direction
            for dist in range(1, const.BOARD_SIZE):
                new_row, new_col = row + dr * dist, col + dc * dist
                
                # Check if we've gone off the board
                if not (0 <= new_row < const.BOARD_SIZE and 0 <= new_col < const.BOARD_SIZE):
                    break
                
                if new_row == row and new_col == col:
                    continue
                
                if (new_row, new_col) in board.units:
                    break  # Position occupied
                
                valid_moves.append((new_row, new_col))
        
        return valid_moves
    
    def get_valid_attacks(self, board):
        """Warriors can only attack adjacent enemies (melee range)"""
        self.board = board
        valid_attacks = []
        
        # Get current position
        row, col = self.position
        
        # Check only adjacent positions (up, right, down, left)
        adjacent_positions = [
            (row-1, col), (row+1, col), 
            (row, col-1), (row, col+1)
        ]
        
        for new_row, new_col in adjacent_positions:
            # Check if position is within board boundaries
            if 0 <= new_row < const.BOARD_SIZE and 0 <= new_col < const.BOARD_SIZE:
                # If there's a unit at this position
                if (new_row, new_col) in board.units:
                    target_unit = board.units[(new_row, new_col)]
                    # If it's an enemy unit without sanctuary, we can attack it
                    if target_unit.player_id != self.player_id and not target_unit.sanctuary:
                        valid_attacks.append((new_row, new_col))
        
        return valid_attacks
    
    def get_ability_targets(self, ability_idx, board):
        """Get valid targets for a specific ability"""
        self.board = board
        
        # Basic move ability (index 0 assumed by convention)
        if ability_idx == 0 and self.abilities[ability_idx].name == "Move":
            return self.get_valid_moves(board)
        
        # Basic attack ability (index 1 assumed by convention)
        elif ability_idx == 1 and self.abilities[ability_idx].name == "Basic Attack":
            return self.get_valid_attacks(board)
        
        # Ensure ability_idx is valid for other abilities
        if not (0 <= ability_idx < len(self.abilities)):
            return []
            
        ability_name = self.abilities[ability_idx].name

        if ability_name == "Cleave Attack":
            return self.get_valid_attacks(board) # Uses basic attack targets
        
        elif ability_name == "Shield Bash":
            return self.get_valid_attacks(board) # Uses basic attack targets
        
        elif ability_name == "Charge":
            return self._get_charge_targets(board)
        
        elif ability_name == "Defensive Stance":
            return [self.position] # Targets self
        
        elif ability_name == "Riposte":
            return [self.position] # Targets self

        elif ability_name == "Summon":
            return self.get_valid_moves(board) # Can summon at any position Warrior could move to

        return [] # Default empty list if ability not handled
    
    def _get_charge_targets(self, board):
        """Get valid targets for charge ability
        Returns a list of tuples (move_pos, attack_pos, attack_dir)
        Charge: move up to 3 tiles and attack target 1 tile beyond move_pos.
        Target means the enemy unit to be attacked.
        The return for targeting should be the *enemy position*.
        The use_ability will handle the move then attack.
        So, we need to identify enemies reachable by a move + 1 tile attack.
        """
        valid_enemy_targets = []
        # Max move for charge is 3 tiles
        # Check all possible move endpoints within 3 tiles (rook movement)
        possible_moves = []
        row, col = self.position
        directions = [(-1, 0), (0, 1), (1, 0), (0, -1)]
        for dr, dc in directions:
            for dist in range(1, 4): # 1, 2, 3 tiles away
                move_r, move_c = row + dr * dist, col + dc * dist
                if not (0 <= move_r < const.BOARD_SIZE and 0 <= move_c < const.BOARD_SIZE):
                    break # Off board
                if (move_r, move_c) in board.units:
                    break # Path blocked
                possible_moves.append(((move_r, move_c), (dr,dc))) # Store move and direction
        
        for (move_r, move_c), (dr, dc) in possible_moves:
            attack_r, attack_c = move_r + dr, move_c + dc # Tile to attack is 1 beyond move_pos
            if (0 <= attack_r < const.BOARD_SIZE and 0 <= attack_c < const.BOARD_SIZE and 
                (attack_r, attack_c) in board.units and
                board.units[(attack_r, attack_c)].player_id != self.player_id and
                not board.units[(attack_r, attack_c)].sanctuary):
                valid_enemy_targets.append((attack_r, attack_c)) # This is the enemy to target

        return list(set(valid_enemy_targets))

    def use_ability(self, ability_idx, target_pos, game=None):
        """
        Use an ability with unified ability execution system.
        Warrior-specific abilities are handled by registered methods.
        """
        # Ensure self.board is set if game object is provided
        if game:
            self.board = game.board

        # Register Warrior-specific ability handlers with the unified system
        self._register_ability_handlers()

        # Use unified ability executor (this handles all validation, AP spending, etc.)
        return super().use_ability(ability_idx, target_pos, game)

    def _register_ability_handlers(self):
        """Register Warrior-specific ability handlers with the unified ability system"""
        ability_executor = self.ability_executor

        # Register handlers for each Warrior ability
        ability_executor.register_ability_handler("Cleave Attack", self._use_cleave_attack)
        ability_executor.register_ability_handler("Shield Bash", self._use_shield_bash)
        ability_executor.register_ability_handler("Charge", self._use_charge)
        ability_executor.register_ability_handler("Defensive Stance", self._use_defensive_stance)
        ability_executor.register_ability_handler("Riposte", self._use_riposte)

    def get_attack_damage(self, target_pos):
        """Calculate Warrior basic attack damage using unified damage calculation"""
        return DamageCalculator.calculate_basic_attack_damage(self, target_pos)

    def _use_cleave_attack(self, target_pos, game=None):
        """Execute Cleave Attack ability - damages target and adjacent units using unified damage calculation"""
        target_row, target_col = target_pos
        hits = 0

        # Get configured damage using unified damage calculation
        ability_damage = DamageCalculator.calculate_ability_damage(self, "Cleave Attack", target_pos)

        target_unit = self.board.units.get(target_pos)
        if target_unit and target_unit.player_id != self.player_id:
            print(f"Cleave hits main target {target_unit.name} at {target_pos}")
            target_unit.take_damage(ability_damage, self)
            hits += 1
        elif not target_unit:
            print(f"Cleave targets empty tile {target_pos}, checking adjacent for cleave.")

        # Determine "facing" for cleave (direction from warrior to target_pos)
        d_row = target_row - self.position[0]
        d_col = target_col - self.position[1]

        # Normalize (crude, assumes adjacent target)
        if d_row != 0: d_row //= abs(d_row)
        if d_col != 0: d_col //= abs(d_col)

        # Cleave targets are perpendicular to the attack direction
        cleave_positions = []
        if d_col == 0: # Vertical attack, cleave horizontally
            cleave_positions.append((target_row, target_col - 1))
            cleave_positions.append((target_row, target_col + 1))
        elif d_row == 0: # Horizontal attack, cleave vertically
            cleave_positions.append((target_row - 1, target_col))
            cleave_positions.append((target_row + 1, target_col))
        else: # Diagonal attack - cleave to the two tiles that form a 'T' with the warrior and target
            cleave_positions.append((target_row - d_row, target_col)) # one side
            cleave_positions.append((target_row, target_col - d_col)) # other side

        for r, c in cleave_positions:
            if 0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE:
                cleave_unit = self.board.units.get((r, c))
                if cleave_unit and cleave_unit.player_id != self.player_id and not cleave_unit.sanctuary:
                    print(f"Cleave hits secondary target {cleave_unit.name} at {(r,c)}")
                    cleave_unit.take_damage(ability_damage, self)
                    hits += 1

        print(f"Cleave Attack hit {hits} total targets")
        return True

    def _use_shield_bash(self, target_pos, game=None):
        """Shield Bash: Damage and stun target using unified systems"""
        target_unit = self.board.units.get(target_pos)
        if not (target_unit and target_unit.player_id != self.player_id):
            return False

        # Get configured damage using unified damage calculation
        ability_damage = DamageCalculator.calculate_ability_damage(self, "Shield Bash", target_pos)

        print(f"Shield Bash hits {target_unit.name} at {target_pos}")
        target_unit.take_damage(ability_damage, self, game=game)

        # Apply stun effect using unified status system
        target_unit.apply_status('Stunned', 2)  # Stun for 2 turns
        print(f"{target_unit.name} stunned for 2 turns")
        return True

    def _use_charge(self, target_pos, game=None):
        """Charge: Move up to 3 tiles toward target and attack using unified damage calculation"""
        target_unit = self.board.units.get(target_pos)
        if not (target_unit and target_unit.player_id != self.player_id):
            return False

        # Find the best position to charge to (adjacent to target)
        d_row = target_pos[0] - self.position[0]
        d_col = target_pos[1] - self.position[1]

        # Normalize direction vector
        norm_dr = d_row // abs(d_row) if d_row != 0 else 0
        norm_dc = d_col // abs(d_col) if d_col != 0 else 0

        # Find suitable move position (adjacent to enemy, in line of charge)
        move_pos_candidate = (target_pos[0] - norm_dr, target_pos[1] - norm_dc)

        # Check if the charge path is clear and within range
        charge_distance = abs(move_pos_candidate[0] - self.position[0]) + abs(move_pos_candidate[1] - self.position[1])
        if (charge_distance <= 3 and
            0 <= move_pos_candidate[0] < const.BOARD_SIZE and
            0 <= move_pos_candidate[1] < const.BOARD_SIZE and
            move_pos_candidate not in self.board.units):

            # Move to the charge position
            old_pos = self.position
            self.board.units[move_pos_candidate] = self
            del self.board.units[old_pos]
            self.position = move_pos_candidate
            print(f"{self.name} charges from {old_pos} to {move_pos_candidate}")

            # Attack the target with configured damage
            charge_damage = DamageCalculator.calculate_ability_damage(self, "Charge", target_pos)
            target_unit.take_damage(charge_damage, self, game=game)
            print(f"Charge attack hits {target_unit.name} for {charge_damage} damage")
            return True

        print(f"Charge failed - path blocked or out of range")
        return False

    def _use_defensive_stance(self, target_pos, game=None):
        """Defensive Stance: Reduce damage taken until next turn"""
        self.defensive_stance_active = True
        if game and hasattr(game, 'turn_count'):
            self.defensive_stance_until = game.turn_count + 1
        print(f"{self.name} enters Defensive Stance - damage reduced by 50%")
        return True

    def _use_riposte(self, target_pos, game=None):
        """Riposte: Prepare to counter the next attack"""
        self.riposte_active = True
        print(f"{self.name} prepares to Riposte")
        return True

    def take_damage(self, amount, attacker=None, game=None):
        """Override take_damage to handle Defensive Stance and Riposte using unified damage calculation"""
        original_amount = amount
        if self.defensive_stance_active:
            amount //= 2
            print(f"{self.name}'s Defensive Stance reduces damage from {original_amount} to {amount}.")

        # Handle Riposte: If active and attacker exists and is in melee range
        if self.riposte_active and attacker and attacker.is_alive():
            dist_sq = (self.position[0] - attacker.position[0])**2 + (self.position[1] - attacker.position[1])**2
            if dist_sq <= 2: # Assuming melee range is 1 (dist_sq 1 or 2 for diagonals)
                # Get configured damage for Riposte using unified damage calculation
                riposte_damage = DamageCalculator.calculate_ability_damage(self, "Riposte")

                print(f"{self.name} Ripostes against {attacker.name} for {riposte_damage} damage!")
                attacker.take_damage(riposte_damage, self, game=game)
                self.riposte_active = False # Riposte triggers once

        super().take_damage(amount, attacker, game=game)
        if not self.is_alive():
            print(f"{self.name} has been defeated!")
        return self.health

    def reset_ap(self, game=None):
        """Override reset_ap to handle Warrior-specific state cleanup"""
        super().reset_ap(game=game)
        # Defensive stance wears off at the START of the next turn for this unit
        if game and self.defensive_stance_active and game.turn_count >= self.defensive_stance_until:
             self.defensive_stance_active = False
             print(f"{self.name}'s Defensive Stance wears off.")
        # Riposte also wears off if not used
        if self.riposte_active: # If it didn't trigger, it wears off now
            self.riposte_active = False
            print(f"{self.name}'s Riposte preparation wears off.")

# Game states
STATE_SETUP = 0
STATE_PLAYING = 1
STATE_GAME_OVER = 2

# Screen Dimensions
WINDOW_WIDTH = 1280
WINDOW_HEIGHT = 720
FPS = 60

# Board Dimensions & Properties
BOARD_SIZE = 9       # Number of cells per side
CELL_SIZE = 70       # Pixel size of a cell
BOARD_PADDING = 50   # Padding around the board grid
HIGHLIGHT_ALPHA = 128 # Alpha for highlight surfaces

# Colors - General UI
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
DARK_GRAY = (30, 30, 30)
LIGHT_GRAY = (200, 200, 200)

# Colors - Player Specific
PLAYER1_COLOR = (0, 100, 200)    # Blue
PLAYER2_COLOR = (200, 60, 60)    # Red
RED = (200, 0, 0) # General Red for UI elements like buttons or warnings

# Colors - Game Elements & Feedback
GREEN = (60, 180, 75)
YELLOW = (255, 200, 0)
ORANGE = (255, 165, 0)
PURPLE = (128, 0, 128)

# Colors - Board Cells
DARK_CELL = (60, 60, 70)
LIGHT_CELL = (150, 150, 160)

# Colors - Highlights (RGBA format for alpha)
MOVE_HIGHLIGHT_COLOR = (0, 255, 0, HIGHLIGHT_ALPHA)       # Green for move
ATTACK_HIGHLIGHT_COLOR = (255, 0, 0, HIGHLIGHT_ALPHA)     # Red for attack
SELECTED_HIGHLIGHT_COLOR = (255, 255, 0, HIGHLIGHT_ALPHA) # Yellow for selected unit
ABILITY_TARGET_HIGHLIGHT_COLOR = (0, 150, 255, HIGHLIGHT_ALPHA) # Light blue for ability targets
DIRECTIONAL_PREVIEW_COLOR = (*YELLOW, HIGHLIGHT_ALPHA) # Yellow for directional previews

# Fonts (Paths or names - consider moving font loading logic if complex)
# For now, these are just names; actual font objects are created in game_state or main_menu
FONT_DEFAULT_NAME = None # Pygame default font
FONT_SIZE_TITLE = 48
FONT_SIZE_UI = 32
FONT_SIZE_SMALL = 24
FONT_SIZE_TINY = 18

# UI Button Colors (example, can be expanded)
BUTTON_COLOR = DARK_GRAY
BUTTON_TEXT_COLOR = LIGHT_GRAY
BUTTON_BORDER_COLOR = LIGHT_GRAY
BUTTON_HOVER_COLOR = (70, 70, 80)
BUTTON_DISABLED_COLOR = (50, 50, 50)
BUTTON_DISABLED_TEXT_COLOR = (100, 100, 100)
SELECTED_ABILITY_BUTTON_COLOR = PLAYER1_COLOR # Or a distinct selection color 
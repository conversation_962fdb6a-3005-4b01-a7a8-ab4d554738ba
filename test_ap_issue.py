#!/usr/bin/env python3
"""
Test script to check AP and ability usage issues
"""

import pygame
from game_state import Game
from units.hunter import Hunter
from units.king import <PERSON>

def test_ap_issue():
    """Test AP usage and ability execution"""
    pygame.init()
    
    print("⚡ AP AND ABILITY USAGE TEST ⚡")
    print("=" * 40)
    
    game = Game()
    
    # Test Hunter AP usage
    print("\n📋 TEST 1: Hunter AP Usage")
    print("-" * 25)
    
    hunter = Hunter(1)
    hunter.position = (4, 4)
    hunter.board = game.board
    game.board.units = {(4, 4): hunter}
    
    print(f"Hunter initial AP: {hunter.current_ap}/{hunter.max_ap}")
    
    # Test move ability
    print(f"\nTesting Move ability (index 0):")
    print(f"  Can use: {hunter.can_use_ability(0)}")
    print(f"  AP cost: {hunter.abilities[0].ap_cost}")
    
    # Get move targets
    move_targets = hunter.get_ability_targets(0, game.board)
    print(f"  Available targets: {len(move_targets)}")
    
    if move_targets:
        target = move_targets[0]
        print(f"  Attempting to move to {target}")
        
        # Try to use move ability
        try:
            result = hunter.use_ability(0, target, game)
            print(f"  Move result: {result}")
            print(f"  Hunter AP after move: {hunter.current_ap}/{hunter.max_ap}")
            print(f"  Hunter position after move: {hunter.position}")
        except Exception as e:
            print(f"  ❌ Error using move: {e}")
            import traceback
            traceback.print_exc()
    
    # Test basic attack
    print(f"\nTesting Basic Attack (index 1):")
    print(f"  Can use: {hunter.can_use_ability(1)}")
    print(f"  AP cost: {hunter.abilities[1].ap_cost}")
    
    attack_targets = hunter.get_ability_targets(1, game.board)
    print(f"  Available targets: {len(attack_targets)}")
    
    # Test King AP usage
    print("\n\n📋 TEST 2: King AP Usage")
    print("-" * 20)
    
    king = King(1)
    king.position = (5, 5)
    king.board = game.board
    game.board.units[(5, 5)] = king
    
    print(f"King initial AP: {king.current_ap}/{king.max_ap}")
    
    # Test move ability
    print(f"\nTesting King Move ability (index 0):")
    print(f"  Can use: {king.can_use_ability(0)}")
    print(f"  AP cost: {king.abilities[0].ap_cost}")
    
    king_move_targets = king.get_ability_targets(0, game.board)
    print(f"  Available targets: {len(king_move_targets)}")
    
    if king_move_targets:
        target = king_move_targets[0]
        print(f"  Attempting to move to {target}")
        
        try:
            result = king.use_ability(0, target, game)
            print(f"  Move result: {result}")
            print(f"  King AP after move: {king.current_ap}/{king.max_ap}")
            print(f"  King position after move: {king.position}")
        except Exception as e:
            print(f"  ❌ Error using move: {e}")
            import traceback
            traceback.print_exc()
    
    # Test AP reset
    print("\n\n📋 TEST 3: AP Reset Test")
    print("-" * 20)
    
    # Drain Hunter AP
    hunter.current_ap = 0
    print(f"Hunter AP drained: {hunter.current_ap}/{hunter.max_ap}")
    print(f"Can use move: {hunter.can_use_ability(0)}")
    
    # Reset AP
    hunter.reset_ap(game)
    print(f"Hunter AP after reset: {hunter.current_ap}/{hunter.max_ap}")
    print(f"Can use move after reset: {hunter.can_use_ability(0)}")
    
    # Test passive effects on AP
    print(f"\nHunter has passive manager: {hasattr(hunter, 'passive_manager')}")
    if hasattr(hunter, 'passive_manager'):
        print(f"Hunter passives: {len(hunter.passive_manager.passive_abilities)}")
        for passive in hunter.passive_manager.passive_abilities:
            print(f"  • {passive.name}")
    
    # Test ability costs
    print("\n\n📋 TEST 4: Ability Cost Analysis")
    print("-" * 30)
    
    print("Hunter abilities and costs:")
    for i, ability in enumerate(hunter.abilities):
        print(f"  {i}: {ability.name} - Cost: {ability.ap_cost}")
    
    print("\nKing abilities and costs:")
    for i, ability in enumerate(king.abilities):
        print(f"  {i}: {ability.name} - Cost: {ability.ap_cost}")
    
    # Test if there's an issue with the game state
    print("\n\n📋 TEST 5: Game State Check")
    print("-" * 25)
    
    print(f"Game board units: {len(game.board.units)}")
    for pos, unit in game.board.units.items():
        print(f"  {pos}: {unit.name} (Player {unit.player_id})")
    
    print(f"Game current player: {getattr(game, 'current_player', 'Not set')}")
    print(f"Game turn: {getattr(game, 'turn', 'Not set')}")
    
    print("\n" + "=" * 40)
    print("🔍 AP DIAGNOSIS COMPLETE")
    print("\nIf units can't perform actions, check:")
    print("  • AP availability (current vs cost)")
    print("  • Ability targeting (valid targets)")
    print("  • Game state (current player, turn)")
    print("  • Passive system interference")
    print("  • UI event handling")

if __name__ == "__main__":
    test_ap_issue()

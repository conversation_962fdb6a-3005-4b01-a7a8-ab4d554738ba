# Detailed To-Do List by Phase (Python/Pygame Adaptation)

**Preamble:**
*   Emphasize creating modular code using Python classes, functions, and modules.
*   The detailed Unit/Skill/Passive definitions (Phase 0 from GAME_DESIGN.md) must be adapted/created as Python data structures or loaded from files to be used as context for relevant tasks.
*   Skill Slot Pool Definitions are pending for future phases.

**Phase 1: Core Foundation**

*   `[ ]` **Project Setup:** Organize project directory structure (e.g., folders for `assets`, `src`, `tests`). Initialize Pygame.
*   `[ ]` **Main Game Loop:** Create the main game script (`main.py` or `game.py`) with the primary Pygame event loop, screen setup, and clock.
*   `[ ]` **Grid Implementation:**
    *   `[ ]` Enhance `GameBoard.py`: Define grid dimensions (9x9), tile size. Store grid data (e.g., what's on each tile).
    *   `[ ]` Add methods for converting grid coordinates to screen coordinates and vice-versa.
    *   `[ ]` Implement `draw_grid(screen)` method in `GameBoard` to visually render the grid lines.
*   `[ ]` **Data Structures - Core:**
    *   `[ ]` Create `UnitData.py` (or similar): Define a base class or data structure for shared unit properties (ID, owner, type, max_hp, current_hp, movement_params, basic_attack_params, skill_ids, passive_id).
    *   `[ ]` Create initial data for each unit type (Warrior, Mage, etc.) perhaps as instances of `UnitData` or in JSON/dictionary format.
    *   `[ ]` Create `SkillData.py`: Define class/structure for skill properties (ID, name, description, AP cost, range, targeting_type, targeting_params, cooldown, effect_logic_ref). Create data for initial skills.
    *   `[ ]` Create `PassiveData.py` and `StatusEffectData.py` similarly.
*   `[ ]` **Turn Management:**
    *   `[ ]` Create `TurnManager.py`: Class to manage `current_player`, `turn_number`.
    *   `[ ]` Implement `end_turn()` method: Switches player, signals AP reset, updates cooldowns/status effects.
*   `[x]` **Global AP System:** ✅ IMPLEMENTED
    *   `[x]` Progressive AP scaling (1→10 per player) with P2 early game bonus
    *   `[x]` One action per unit per turn enforcement
    *   `[x]` Global AP pool management with proper deduction
*   `[ ]` **Basic UI (HUD):**
    *   `[ ]` Create `ui_elements.py` (or similar) for reusable UI components (e.g., `Label`, `Button` classes).
    *   `[ ]` Create `HUD.py`: Class to manage and draw HUD elements (current player, AP).
    *   `[ ]` Integrate HUD into the main game loop to display information. Update display based on `TurnManager` and `APSystem`.

---

## 🔧 **CRITICAL ISSUES TO FIX**

### **❌ Configuration System Effectiveness**
*   `[ ]` **CRITICAL: Fix Configuration Not Affecting Gameplay**
    *   `[ ]` Investigate why HP changes in config don't affect new units
    *   `[ ]` Investigate why AP cost changes in config don't affect new units
    *   `[ ]` Fix unit initialization to properly read from GAME_CONFIG
    *   `[ ]` Ensure balance sliders actually change gameplay stats
    *   `[ ]` Add comprehensive test for configuration effectiveness
    *   `[ ]` Verify all unit classes properly use configuration values

### **🔧 Minor Issues**
*   `[ ]` **Mage Summon Ability:** Fix execution failure (AP system working, execution broken)
*   `[ ]` **Remaining Unit Classes:** Update all unit classes to use global AP system consistently

---

## ✅ **COMPLETED MAJOR SYSTEMS**

### **Movement Systems** ✅
*   `[x]` Rogue knight movement (L-shaped) with jumping
*   `[x]` Mage blink movement (2 tiles orthogonal)
*   `[x]` Cleric orthogonal-only movement (1 tile N/S/E/W)
*   `[x]` All movement patterns working correctly

### **AP System** ✅
*   `[x]` Global AP system (1→10 progressive scaling)
*   `[x]` One action per unit per turn enforcement
*   `[x]` Player 2 early game bonus (+1 AP turns 1-3)
*   `[x]` Proper AP deduction and tracking

### **Summon Abilities** ✅ (80%)
*   `[x]` Hunter: Diagonal summon (8 positions)
*   `[x]` Warrior: Orthogonal summon (16 positions)
*   `[x]` Rogue: Knight summon (8 positions)
*   `[x]` Cleric: Orthogonal summon (4 positions)
*   `[ ]` Mage: Adjacent summon (needs debugging)

### **Game Stability** ✅
*   `[x]` Game launches without crashes
*   `[x]` No AttributeError issues
*   `[x]` Status effect compatibility system
*   `[x]` Turn progression working correctly

---
*(Subsequent phases will be detailed here as we progress)*
---
#!/usr/bin/env python3
"""
Debug test for Arcane Missiles damage issue
"""

import pygame
from game_state import Game
from units.mage import Mage
from units.warrior import <PERSON>

def test_arcane_missiles_debug():
    """Debug Arcane Missiles damage"""
    pygame.init()
    
    print("🔍 DEBUGGING ARCANE MISSILES DAMAGE 🔍")
    print("=" * 40)
    
    game = Game()
    mage = Mage(1)
    target = Warrior(2)
    
    # Position units
    mage.position = (4, 4)
    target.position = (3, 4)  # 1 tile north
    
    # Set up board
    mage.board = game.board
    game.board.units = {
        (4, 4): mage,
        (3, 4): target
    }
    
    print(f"Setup:")
    print(f"  Mage at {mage.position}")
    print(f"  Target at {target.position}")
    print(f"  Target HP before: {target.health}")
    
    # Find Arcane Missile ability
    arcane_ability_idx = None
    for i, ability in enumerate(mage.abilities):
        if ability.name == "Arcane Missile":
            arcane_ability_idx = i
            break
    
    if arcane_ability_idx is None:
        print("❌ Arcane Missile ability not found!")
        return
    
    print(f"  Arcane Missile ability index: {arcane_ability_idx}")
    print(f"  Mage AP before: {mage.current_ap}")
    
    # Use Arcane Missiles
    north_target = (3, 4)
    print(f"\n🚀 Using Arcane Missiles...")
    result = mage.use_ability(arcane_ability_idx, north_target, game)
    
    print(f"\nResults:")
    print(f"  Ability result: {result}")
    print(f"  Target HP after: {target.health}")
    print(f"  Mage AP after: {mage.current_ap}")
    print(f"  Target still alive: {target.is_alive()}")
    
    if target.health < 7:  # Warrior starts with 7 HP
        print(f"✅ Damage applied: {7 - target.health} damage dealt")
    else:
        print(f"❌ No damage applied!")

if __name__ == "__main__":
    test_arcane_missiles_debug()

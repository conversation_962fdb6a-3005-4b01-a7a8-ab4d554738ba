#!/usr/bin/env python3
"""
Test script for Mage blink movement:
1. Exactly 2 tiles in orthogonal directions only
2. Can pass through entities (no line of sight required)
3. Destination must be unoccupied
4. Should show exactly 4 movement options (or fewer if blocked/off-board)
"""

import pygame
from game_state import Game
from units.mage import Mage
from units.warrior import <PERSON>
from units.hunter import <PERSON>

def test_mage_blink_movement():
    """Test Mage's special blink movement pattern"""
    pygame.init()
    
    print("🔮 TESTING MAGE BLINK MOVEMENT 🔮")
    print("=" * 40)
    
    # Test 1: Basic Blink Movement (Clear Board)
    print("📋 TEST 1: Basic Blink Movement (Clear Board)")
    print("-" * 45)
    
    game = Game()
    mage = Mage(1)
    mage.position = (4, 4)  # Center of board
    mage.board = game.board
    game.board.units = {(4, 4): mage}
    
    print(f"Mage at center position: {mage.position}")
    
    # Get movement options
    moves = mage.get_valid_moves(game.board)
    print(f"Available moves: {moves}")
    print(f"Number of moves: {len(moves)}")
    
    # Expected moves: exactly 2 tiles in each orthogonal direction
    expected_moves = [
        (2, 4),  # North (up 2)
        (6, 4),  # South (down 2)
        (4, 2),  # West (left 2)
        (4, 6)   # East (right 2)
    ]
    
    print(f"\n🔍 Checking expected moves:")
    all_correct = True
    for expected in expected_moves:
        if expected in moves:
            print(f"  ✅ {expected} (2 tiles away)")
        else:
            print(f"  ❌ {expected} (missing)")
            all_correct = False
    
    # Check for unexpected moves
    unexpected_moves = [move for move in moves if move not in expected_moves]
    if unexpected_moves:
        print(f"  ⚠️  Unexpected moves: {unexpected_moves}")
        all_correct = False
    
    if all_correct and len(moves) == 4:
        print(f"✅ Perfect blink movement - 4 orthogonal positions!")
    else:
        print(f"❌ Blink movement issues detected")
    
    # Test 2: Blink Through Entities
    print(f"\n📋 TEST 2: Blink Through Entities")
    print("-" * 35)
    
    game2 = Game()
    mage2 = Mage(1)
    blocker1 = Warrior(2)  # Block path north
    blocker2 = Hunter(2)   # Block path south
    
    # Position mage and blockers
    mage2.position = (4, 4)
    blocker1.position = (3, 4)  # 1 tile north of mage
    blocker2.position = (5, 4)  # 1 tile south of mage
    
    # Set up board
    mage2.board = game2.board
    game2.board.units = {
        (4, 4): mage2,
        (3, 4): blocker1,
        (5, 4): blocker2
    }
    
    print(f"Setup:")
    print(f"  Mage at {mage2.position}")
    print(f"  Blocker (Warrior) at {blocker1.position} (1 tile north)")
    print(f"  Blocker (Hunter) at {blocker2.position} (1 tile south)")
    
    # Get movement options
    moves2 = mage2.get_valid_moves(game2.board)
    print(f"\nAvailable moves: {moves2}")
    
    # Should still be able to blink through blockers to 2-tile destinations
    expected_through_blockers = [
        (2, 4),  # North (through blocker at 3,4)
        (6, 4),  # South (through blocker at 5,4)
        (4, 2),  # West (clear path)
        (4, 6)   # East (clear path)
    ]
    
    print(f"\n🔍 Checking blink-through capability:")
    for expected in expected_through_blockers:
        if expected in moves2:
            print(f"  ✅ {expected} (can blink through entities)")
        else:
            print(f"  ❌ {expected} (blocked - should be able to blink through)")
    
    if len(moves2) == 4:
        print(f"✅ Blink through entities working perfectly!")
    else:
        print(f"❌ Blink through entities not working correctly")
    
    # Test 3: Blocked Destinations
    print(f"\n📋 TEST 3: Blocked Destinations")
    print("-" * 32)
    
    game3 = Game()
    mage3 = Mage(1)
    destination_blocker = Warrior(2)  # Block destination
    
    # Position mage and destination blocker
    mage3.position = (4, 4)
    destination_blocker.position = (2, 4)  # 2 tiles north (destination)
    
    # Set up board
    mage3.board = game3.board
    game3.board.units = {
        (4, 4): mage3,
        (2, 4): destination_blocker
    }
    
    print(f"Setup:")
    print(f"  Mage at {mage3.position}")
    print(f"  Destination blocker at {destination_blocker.position} (2 tiles north)")
    
    # Get movement options
    moves3 = mage3.get_valid_moves(game3.board)
    print(f"\nAvailable moves: {moves3}")
    
    # Should have 3 moves (north blocked, south/east/west available)
    expected_with_blocked_dest = [
        (6, 4),  # South (available)
        (4, 2),  # West (available)
        (4, 6)   # East (available)
        # (2, 4) should NOT be available (occupied)
    ]
    
    print(f"\n🔍 Checking blocked destination:")
    if (2, 4) not in moves3:
        print(f"  ✅ (2, 4) correctly blocked (destination occupied)")
    else:
        print(f"  ❌ (2, 4) incorrectly available (should be blocked)")
    
    available_count = len(moves3)
    if available_count == 3:
        print(f"✅ Correct number of moves with blocked destination: {available_count}")
    else:
        print(f"❌ Incorrect number of moves: {available_count} (should be 3)")
    
    # Test 4: Board Edge Limitations
    print(f"\n📋 TEST 4: Board Edge Limitations")
    print("-" * 35)
    
    game4 = Game()
    mage4 = Mage(1)
    mage4.position = (1, 1)  # Near top-left corner
    mage4.board = game4.board
    game4.board.units = {(1, 1): mage4}
    
    print(f"Mage near corner at: {mage4.position}")
    
    # Get movement options
    moves4 = mage4.get_valid_moves(game4.board)
    print(f"Available moves: {moves4}")
    
    # From (1,1), only south (3,1) and east (1,3) should be available
    # North would go to (-1,1) - off board
    # West would go to (1,-1) - off board
    expected_corner_moves = [
        (3, 1),  # South (down 2)
        (1, 3)   # East (right 2)
        # North and West go off-board
    ]
    
    print(f"\n🔍 Checking board edge handling:")
    for expected in expected_corner_moves:
        if expected in moves4:
            print(f"  ✅ {expected} (valid edge move)")
        else:
            print(f"  ❌ {expected} (missing valid edge move)")
    
    # Check no off-board moves
    off_board_moves = [move for move in moves4 if move[0] < 0 or move[1] < 0 or move[0] >= 9 or move[1] >= 9]
    if not off_board_moves:
        print(f"  ✅ No off-board moves detected")
    else:
        print(f"  ❌ Off-board moves found: {off_board_moves}")
    
    if len(moves4) == 2:
        print(f"✅ Correct edge behavior: {len(moves4)} moves available")
    else:
        print(f"❌ Incorrect edge behavior: {len(moves4)} moves (should be 2)")
    
    print(f"\n" + "=" * 40)
    print("🎯 MAGE BLINK MOVEMENT SUMMARY")
    print("-" * 30)
    print("✅ Pattern: Exactly 2 tiles in orthogonal directions")
    print("✅ Blink: Can pass through entities")
    print("✅ Destinations: Must be unoccupied")
    print("✅ Directions: North, South, East, West only")
    print("✅ Board Edges: Properly handled")
    print("\n🔮 Mage movement is now a tactical blink ability!")

if __name__ == "__main__":
    test_mage_blink_movement()

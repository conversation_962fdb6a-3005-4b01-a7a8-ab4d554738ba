#!/usr/bin/env python3
"""
Simple verification that the balanced AP system is working correctly
"""

import pygame
from game_state import Game
from units.warrior import Warrior
from units.mage import Mage

def verify_implementation():
    """Verify the balanced AP system implementation"""
    pygame.init()
    
    print("VERIFYING BALANCED AP SYSTEM IMPLEMENTATION")
    print("=" * 45)
    
    # Test 1: Basic System Check
    print("1. SYSTEM INITIALIZATION")
    print("-" * 25)
    
    game = Game()
    
    # Check if all components are present
    checks = [
        ("Global AP system", hasattr(game, 'current_player_ap')),
        ("AP scaling", hasattr(game, 'base_ap') and hasattr(game, 'ap_increment')),
        ("Balance sliders", hasattr(game, 'balance_sliders')),
        ("Turn tracking", hasattr(game, 'units_acted_this_turn')),
        ("Player 2 bonus", hasattr(game, 'player2_early_bonus'))
    ]
    
    for check_name, result in checks:
        status = "✓" if result else "✗"
        print(f"  {status} {check_name}")
    
    all_passed = all(result for _, result in checks)
    print(f"\nSystem initialization: {'SUCCESS' if all_passed else 'FAILED'}")
    
    # Test 2: AP Calculation
    print(f"\n2. AP CALCULATION")
    print("-" * 17)
    
    # Test key AP values
    ap_tests = [
        (1, 1, 1, "Turn 1 Player 1"),
        (1, 2, 2, "Turn 1 Player 2 (bonus)"),
        (3, 1, 3, "Turn 3 Player 1"),
        (3, 2, 4, "Turn 3 Player 2 (bonus)"),
        (5, 1, 5, "Turn 5 Player 1"),
        (5, 2, 5, "Turn 5 Player 2 (no bonus)"),
        (10, 1, 10, "Turn 10 Player 1 (max)"),
        (15, 1, 10, "Turn 15 Player 1 (capped)")
    ]
    
    for turn, player, expected, description in ap_tests:
        actual = game.calculate_turn_ap(turn, player)
        status = "✓" if actual == expected else "✗"
        print(f"  {status} {description}: {actual} AP (expected {expected})")
    
    # Test 3: Unit Action System
    print(f"\n3. UNIT ACTION SYSTEM")
    print("-" * 21)
    
    # Create test units
    warrior = Warrior(1)
    mage = Mage(1)
    
    warrior.position = (4, 4)
    mage.position = (4, 5)
    
    game.board.units = {(4, 4): warrior, (4, 5): mage}
    warrior.board = game.board
    mage.board = game.board
    
    # Start turn with enough AP
    game.start_player_turn(1)
    game.current_player_ap = 5  # Set enough for testing
    
    print(f"  Initial AP: {game.current_player_ap}")
    print(f"  Warrior acted: {warrior.has_acted_this_turn}")
    print(f"  Mage acted: {mage.has_acted_this_turn}")
    
    # Test warrior action
    warrior_success = warrior.use_ability(0, (4, 3), game)
    print(f"  ✓ Warrior move: {warrior_success} (AP: {game.current_player_ap})")
    
    # Test warrior second action (should fail)
    warrior_second = warrior.use_ability(1, (3, 3), game)
    print(f"  ✓ Warrior second action blocked: {not warrior_second}")
    
    # Test mage action (should work)
    mage_success = mage.use_ability(0, (4, 6), game)
    print(f"  ✓ Mage move: {mage_success} (AP: {game.current_player_ap})")
    
    # Test 4: Balance Sliders
    print(f"\n4. BALANCE SLIDERS")
    print("-" * 17)
    
    # Check slider categories
    categories = game.balance_sliders.categories
    print(f"  Categories: {len(categories)} ({', '.join(categories)})")
    
    # Test slider modification
    original_hp = game.balance_sliders.sliders["warrior_hp"].current_value
    game.balance_sliders.update_slider("warrior_hp", 15)
    new_hp = game.balance_sliders.sliders["warrior_hp"].current_value
    
    print(f"  ✓ Slider update: {original_hp} → {new_hp}")
    
    # Test applying changes
    changes = game.apply_balance_changes()
    print(f"  ✓ Apply changes: {len(changes)} changes applied")
    
    # Test 5: First Player Advantage Analysis
    print(f"\n5. FIRST PLAYER ADVANTAGE ANALYSIS")
    print("-" * 35)
    
    # Calculate advantage over first 10 turns
    total_p1 = sum(game.calculate_turn_ap(t, 1) for t in range(1, 11))
    total_p2 = sum(game.calculate_turn_ap(t, 2) for t in range(1, 11))
    advantage = total_p2 - total_p1
    percentage = (advantage / total_p1) * 100
    
    print(f"  Player 1 total (10 turns): {total_p1} AP")
    print(f"  Player 2 total (10 turns): {total_p2} AP")
    print(f"  Player 2 advantage: +{advantage} AP ({percentage:.1f}%)")
    
    # Check if advantage is reasonable (should be small)
    reasonable = 3 <= advantage <= 6  # 3-6 AP advantage is reasonable
    status = "✓" if reasonable else "✗"
    print(f"  {status} Advantage is reasonable: {reasonable}")
    
    # Test 6: Game Integration
    print(f"\n6. GAME INTEGRATION")
    print("-" * 19)
    
    # Test hotkey flags
    ui_toggle = hasattr(game, 'show_balance_ui')
    print(f"  ✓ Balance UI toggle: {ui_toggle}")
    
    # Test preset system
    presets = len(game.balance_sliders.presets)
    print(f"  ✓ Balance presets: {presets} available")
    
    # Test progression preview
    progression = game.get_ap_progression_preview(5)
    print(f"  ✓ AP progression preview: {len(progression)} turns")
    
    print(f"\n" + "=" * 45)
    print("VERIFICATION SUMMARY")
    print("-" * 20)
    
    # Overall assessment
    ap_calculation_passed = all(game.calculate_turn_ap(turn, player) == expected
                               for turn, player, expected, _ in ap_tests)

    core_features = [
        all_passed,  # System initialization
        ap_calculation_passed,  # AP calculation
        warrior_success and not warrior_second and mage_success,  # Unit actions
        len(categories) >= 5,  # Balance sliders
        reasonable,  # First player advantage
        ui_toggle and presets >= 3  # Game integration
    ]

    print(f"  AP calculation passed: {ap_calculation_passed}")
    print(f"  Unit actions passed: {warrior_success and not warrior_second and mage_success}")
    print(f"  Balance sliders passed: {len(categories) >= 5}")
    print(f"  First player advantage passed: {reasonable}")
    print(f"  Game integration passed: {ui_toggle and presets >= 3}")
    
    passed = sum(core_features)
    total = len(core_features)
    
    print(f"Core features working: {passed}/{total}")
    
    if passed == total:
        print("🎉 SUCCESS: Balanced AP system fully implemented!")
        print("")
        print("✓ Global AP scaling (1→10, +1 per turn)")
        print("✓ First player advantage mitigation (+1 AP for P2 early)")
        print("✓ One action per unit enforcement")
        print("✓ Balance slider system (20 sliders, 6 categories)")
        print("✓ Real-time balance adjustments")
        print("✓ Hotkey controls (F1-F5)")
        print("")
        print("🎮 READY FOR GAMEPLAY TESTING!")
        print("Launch the game and use F1 to access balance sliders.")
    else:
        print("❌ ISSUES DETECTED: Some features need attention")
        print("Check the failed tests above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = verify_implementation()
    exit(0 if success else 1)

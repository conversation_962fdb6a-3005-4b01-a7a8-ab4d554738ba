#!/usr/bin/env python3
"""
Test script for Cone of Cold T-pattern:
1. Creates T-shaped pattern in orthogonal directions
2. Affects exactly 4 tiles: 1 tile + 3 tiles in T-shape
3. Only targets orthogonal directions (N, S, E, W)
4. Friendly fire enabled
"""

import pygame
from game_state import Game
from units.mage import Mage
from units.warrior import Warrior
from units.hunter import Hunter

def test_cone_of_cold_t_pattern():
    """Test Mage's Cone of Cold T-pattern ability"""
    pygame.init()
    
    print("❄️ TESTING CONE OF COLD T-PATTERN ❄️")
    print("=" * 45)
    
    # Test 1: North Direction T-Pattern
    print("📋 TEST 1: North Direction T-Pattern")
    print("-" * 38)
    
    game = Game()
    mage = Mage(1)
    
    # Create targets in T-pattern positions for North direction
    target1 = Warrior(2)  # 1 tile north
    target2 = Warrior(2)  # 2 tiles north (center of T)
    target3 = Hunter(2)   # 2 tiles north, 1 west (left of T)
    target4 = Hunter(2)   # 2 tiles north, 1 east (right of T)
    
    # Position mage and targets
    mage.position = (4, 4)
    target1.position = (3, 4)  # North (1 tile)
    target2.position = (2, 4)  # North (2 tiles, center)
    target3.position = (2, 3)  # North (2 tiles, left)
    target4.position = (2, 5)  # North (2 tiles, right)
    
    # Set up board
    mage.board = game.board
    game.board.units = {
        (4, 4): mage,
        (3, 4): target1,
        (2, 4): target2,
        (2, 3): target3,
        (2, 5): target4
    }
    
    print(f"Setup (North T-pattern):")
    print(f"  Mage at {mage.position}")
    print(f"  Target 1 at {target1.position} (1 tile north)")
    print(f"  Target 2 at {target2.position} (2 tiles north, center)")
    print(f"  Target 3 at {target3.position} (2 tiles north, left)")
    print(f"  Target 4 at {target4.position} (2 tiles north, right)")
    
    # Find Cone of Cold ability
    cone_ability_idx = None
    for i, ability in enumerate(mage.abilities):
        if ability.name == "Cone of Cold":
            cone_ability_idx = i
            break
    
    if cone_ability_idx is None:
        print("❌ Cone of Cold ability not found!")
        return
    
    # Get targeting options
    targets = mage.get_ability_targets(cone_ability_idx, game.board)
    print(f"\nCone of Cold targeting options: {targets}")
    
    # Should only show orthogonal directions
    expected_directions = [(3, 4), (5, 4), (4, 3), (4, 5)]  # N, S, W, E
    orthogonal_only = all(pos in expected_directions for pos in targets if pos in expected_directions)
    
    if orthogonal_only and len(targets) <= 4:
        print(f"✅ Orthogonal targeting only: {len(targets)} directions")
    else:
        print(f"❌ Non-orthogonal targeting detected")
    
    # Record original HP
    original_hp = {
        "target1": target1.health,
        "target2": target2.health,
        "target3": target3.health,
        "target4": target4.health
    }
    
    # Use Cone of Cold targeting North
    north_target = (3, 4)  # 1 tile north
    print(f"\n❄️ Using Cone of Cold targeting North...")
    result = mage.use_ability(cone_ability_idx, north_target, game)
    
    print(f"\nResults:")
    print(f"  Ability success: {result}")
    print(f"  Target 1 HP: {original_hp['target1']} → {target1.health}")
    print(f"  Target 2 HP: {original_hp['target2']} → {target2.health}")
    print(f"  Target 3 HP: {original_hp['target3']} → {target3.health}")
    print(f"  Target 4 HP: {original_hp['target4']} → {target4.health}")
    
    # Check if all 4 targets were hit
    targets_hit = 0
    if target1.health < original_hp["target1"]:
        targets_hit += 1
    if target2.health < original_hp["target2"]:
        targets_hit += 1
    if target3.health < original_hp["target3"]:
        targets_hit += 1
    if target4.health < original_hp["target4"]:
        targets_hit += 1
    
    if targets_hit == 4:
        print(f"✅ Perfect T-pattern: All 4 targets hit!")
    else:
        print(f"❌ T-pattern incomplete: Only {targets_hit}/4 targets hit")
    
    # Test 2: East Direction T-Pattern
    print(f"\n📋 TEST 2: East Direction T-Pattern")
    print("-" * 37)
    
    game2 = Game()
    mage2 = Mage(1)
    
    # Create targets in T-pattern positions for East direction
    east_target1 = Warrior(2)  # 1 tile east
    east_target2 = Warrior(2)  # 2 tiles east (center of T)
    east_target3 = Hunter(2)   # 2 tiles east, 1 north (top of T)
    east_target4 = Hunter(2)   # 2 tiles east, 1 south (bottom of T)
    
    # Position mage and targets
    mage2.position = (4, 4)
    east_target1.position = (4, 5)  # East (1 tile)
    east_target2.position = (4, 6)  # East (2 tiles, center)
    east_target3.position = (3, 6)  # East (2 tiles, north)
    east_target4.position = (5, 6)  # East (2 tiles, south)
    
    # Set up board
    mage2.board = game2.board
    game2.board.units = {
        (4, 4): mage2,
        (4, 5): east_target1,
        (4, 6): east_target2,
        (3, 6): east_target3,
        (5, 6): east_target4
    }
    
    print(f"Setup (East T-pattern):")
    print(f"  Mage at {mage2.position}")
    print(f"  Target 1 at {east_target1.position} (1 tile east)")
    print(f"  Target 2 at {east_target2.position} (2 tiles east, center)")
    print(f"  Target 3 at {east_target3.position} (2 tiles east, north)")
    print(f"  Target 4 at {east_target4.position} (2 tiles east, south)")
    
    # Record original HP
    original_hp2 = {
        "target1": east_target1.health,
        "target2": east_target2.health,
        "target3": east_target3.health,
        "target4": east_target4.health
    }
    
    # Use Cone of Cold targeting East
    east_direction = (4, 5)  # 1 tile east
    print(f"\n❄️ Using Cone of Cold targeting East...")
    result2 = mage2.use_ability(cone_ability_idx, east_direction, game2)
    
    print(f"\nResults:")
    print(f"  Ability success: {result2}")
    print(f"  Target 1 HP: {original_hp2['target1']} → {east_target1.health}")
    print(f"  Target 2 HP: {original_hp2['target2']} → {east_target2.health}")
    print(f"  Target 3 HP: {original_hp2['target3']} → {east_target3.health}")
    print(f"  Target 4 HP: {original_hp2['target4']} → {east_target4.health}")
    
    # Check if all 4 targets were hit
    targets_hit2 = 0
    if east_target1.health < original_hp2["target1"]:
        targets_hit2 += 1
    if east_target2.health < original_hp2["target2"]:
        targets_hit2 += 1
    if east_target3.health < original_hp2["target3"]:
        targets_hit2 += 1
    if east_target4.health < original_hp2["target4"]:
        targets_hit2 += 1
    
    if targets_hit2 == 4:
        print(f"✅ Perfect T-pattern: All 4 targets hit!")
    else:
        print(f"❌ T-pattern incomplete: Only {targets_hit2}/4 targets hit")
    
    # Test 3: Friendly Fire
    print(f"\n📋 TEST 3: Friendly Fire T-Pattern")
    print("-" * 35)
    
    game3 = Game()
    mage3 = Mage(1)
    ally1 = Warrior(1)  # Same player
    ally2 = Hunter(1)   # Same player
    enemy1 = Warrior(2) # Different player
    enemy2 = Hunter(2)  # Different player
    
    # Position for South T-pattern
    mage3.position = (4, 4)
    ally1.position = (5, 4)   # 1 tile south
    ally2.position = (6, 4)   # 2 tiles south, center
    enemy1.position = (6, 3)  # 2 tiles south, west
    enemy2.position = (6, 5)  # 2 tiles south, east
    
    # Set up board
    mage3.board = game3.board
    game3.board.units = {
        (4, 4): mage3,
        (5, 4): ally1,
        (6, 4): ally2,
        (6, 3): enemy1,
        (6, 5): enemy2
    }
    
    print(f"Setup (Friendly Fire Test):")
    print(f"  Mage (Player 1) at {mage3.position}")
    print(f"  Ally 1 (Player 1) at {ally1.position}")
    print(f"  Ally 2 (Player 1) at {ally2.position}")
    print(f"  Enemy 1 (Player 2) at {enemy1.position}")
    print(f"  Enemy 2 (Player 2) at {enemy2.position}")
    
    # Record original HP
    original_hp3 = {
        "ally1": ally1.health,
        "ally2": ally2.health,
        "enemy1": enemy1.health,
        "enemy2": enemy2.health
    }
    
    # Use Cone of Cold targeting South
    south_direction = (5, 4)  # 1 tile south
    print(f"\n❄️ Using Cone of Cold targeting South (friendly fire test)...")
    result3 = mage3.use_ability(cone_ability_idx, south_direction, game3)
    
    print(f"\nResults:")
    print(f"  Ally 1 HP: {original_hp3['ally1']} → {ally1.health}")
    print(f"  Ally 2 HP: {original_hp3['ally2']} → {ally2.health}")
    print(f"  Enemy 1 HP: {original_hp3['enemy1']} → {enemy1.health}")
    print(f"  Enemy 2 HP: {original_hp3['enemy2']} → {enemy2.health}")
    
    # Check friendly fire
    allies_hit = (ally1.health < original_hp3["ally1"]) + (ally2.health < original_hp3["ally2"])
    enemies_hit = (enemy1.health < original_hp3["enemy1"]) + (enemy2.health < original_hp3["enemy2"])
    
    if allies_hit > 0:
        print(f"✅ Friendly fire working: {allies_hit} allies hit")
    else:
        print(f"❌ Friendly fire not working: No allies hit")
    
    if enemies_hit > 0:
        print(f"✅ Enemy damage working: {enemies_hit} enemies hit")
    else:
        print(f"❌ Enemy damage not working: No enemies hit")
    
    print(f"\n" + "=" * 45)
    print("🎯 CONE OF COLD T-PATTERN SUMMARY")
    print("-" * 35)
    print("✅ Pattern: T-shaped (1 tile + 3 tiles)")
    print("✅ Directions: Orthogonal only (N, S, E, W)")
    print("✅ Targeting: 4 directions maximum")
    print("✅ Damage: 1 damage + Chill effect")
    print("✅ Friendly Fire: Hits allies and enemies")
    print("\n❄️ Cone of Cold is now a precise T-pattern ability!")

if __name__ == "__main__":
    test_cone_of_cold_t_pattern()

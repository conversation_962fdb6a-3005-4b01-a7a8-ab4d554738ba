"""
Unified Configuration Management System

This module provides a centralized approach to configuration loading and management,
eliminating duplication across unit classes and providing consistent configuration access.
"""

import json
import os
from typing import Dict, Any, Optional, Union
from game_config import GAME_CONFIG


class ConfigurationManager:
    """
    Centralized configuration manager that handles both static (game_config.py) 
    and dynamic (JSON) configuration loading with fallback mechanisms.
    """
    
    def __init__(self):
        self.static_config = GAME_CONFIG
        self.dynamic_config = {}
        self.config_loader = None
        self._load_dynamic_config()
        self._initialize_config_loader()
    
    def _load_dynamic_config(self):
        """Load dynamic configuration from JSON file if available"""
        try:
            config_path = "game_balance_config.json"
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    self.dynamic_config = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"Warning: Could not load dynamic config: {e}")
            self.dynamic_config = {}
    
    def _initialize_config_loader(self):
        """Initialize ConfigLoader if available"""
        try:
            from config_loader import ConfigLoader
            self.config_loader = ConfigLoader()
        except ImportError:
            self.config_loader = None
    
    def get_unit_config(self, unit_class_name: str) -> Dict[str, Any]:
        """
        Get complete configuration for a unit class.
        
        Args:
            unit_class_name: Name of the unit class (e.g., "Hunter", "Warrior")
            
        Returns:
            Dictionary containing all configuration values for the unit
        """
        config_key = f"{unit_class_name.lower()}_config"
        
        # Start with static config
        config = self.static_config.get(config_key, {}).copy()
        
        # Override with dynamic config if available
        if unit_class_name in self.dynamic_config:
            config.update(self.dynamic_config[unit_class_name])
        
        return config
    
    def get_unit_property(self, unit_class_name: str, property_name: str, default: Any = None) -> Any:
        """
        Get a specific property for a unit class with fallback chain.
        
        Args:
            unit_class_name: Name of the unit class
            property_name: Name of the property to retrieve
            default: Default value if property not found
            
        Returns:
            The property value from the most specific configuration source
        """
        # Try dynamic config first (highest priority)
        if unit_class_name in self.dynamic_config:
            if property_name in self.dynamic_config[unit_class_name]:
                return self.dynamic_config[unit_class_name][property_name]
        
        # Try static config
        config_key = f"{unit_class_name.lower()}_config"
        if config_key in self.static_config:
            if property_name in self.static_config[config_key]:
                return self.static_config[config_key][property_name]
        
        # Try global settings
        if property_name in self.static_config.get("global_settings", {}):
            return self.static_config["global_settings"][property_name]
        
        return default
    
    def get_ability_damage(self, unit_class_name: str, ability_name: str) -> float:
        """
        Get damage value for a specific ability using ConfigLoader if available,
        with fallback to static configuration.
        
        Args:
            unit_class_name: Name of the unit class
            ability_name: Name of the ability
            
        Returns:
            Damage value for the ability
        """
        # Try ConfigLoader first (most dynamic)
        if self.config_loader:
            try:
                damage = self.config_loader.get_ability_damage(unit_class_name, ability_name)
                if damage > 0:
                    return damage
            except Exception as e:
                print(f"Warning: ConfigLoader failed for {unit_class_name}.{ability_name}: {e}")
        
        # Try specific ability damage configuration
        damage_key = f"{ability_name.lower().replace(' ', '_')}_damage"
        damage = self.get_unit_property(unit_class_name, damage_key)
        if damage is not None:
            return damage
        
        # Fallback to default ability damage
        return self.get_unit_property(unit_class_name, "default_ability_damage", 1)
    
    def get_ability_ap_cost(self, unit_class_name: str, ability_name: str) -> int:
        """
        Get AP cost for a specific ability.
        
        Args:
            unit_class_name: Name of the unit class
            ability_name: Name of the ability
            
        Returns:
            AP cost for the ability
        """
        cost_key = f"{ability_name.lower().replace(' ', '_')}_ap_cost"
        return self.get_unit_property(unit_class_name, cost_key, 2)
    
    def apply_configuration_to_unit(self, unit):
        """
        Apply configuration to a unit instance, replacing the duplicated
        apply_config_to_unit() calls across unit classes.
        
        Args:
            unit: Unit instance to configure
        """
        unit_class_name = unit.__class__.__name__
        config = self.get_unit_config(unit_class_name)
        
        # Apply basic properties
        if "health" in config:
            unit.health = config["health"]
            unit.max_health = config["health"]
        
        if "max_ap" in config:
            unit.max_ap = config["max_ap"]
            unit.current_ap = config["max_ap"]
        
        # Apply ability-specific configurations
        for ability in unit.abilities:
            if hasattr(ability, 'ap_cost'):
                ability.ap_cost = self.get_ability_ap_cost(unit_class_name, ability.name)
    
    def reload_configuration(self):
        """Reload dynamic configuration from file"""
        self._load_dynamic_config()
        if self.config_loader:
            try:
                self.config_loader.reload()
            except Exception as e:
                print(f"Warning: Could not reload ConfigLoader: {e}")


# Global configuration manager instance
config_manager = ConfigurationManager()


def get_config_manager() -> ConfigurationManager:
    """Get the global configuration manager instance"""
    return config_manager


def apply_config_to_unit(unit):
    """
    Convenience function to apply configuration to a unit.
    This replaces the duplicated try/catch blocks across unit classes.
    """
    try:
        config_manager.apply_configuration_to_unit(unit)
    except Exception as e:
        print(f"Warning: Could not apply configuration to {unit.__class__.__name__}: {e}")

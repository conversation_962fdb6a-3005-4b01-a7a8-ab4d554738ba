#!/usr/bin/env python3
"""
Test specific Hunter ability issues:
1. Spread Shot doesn't deal damage to orthogonal unit
2. Ricochet Shot doesn't ricochet a second time
3. Crippling Shot effect is infinite (should be 1 turn)
"""

import pygame
pygame.init()

from game_state import Game
from units.hunter import Hunter
from units.warrior import Warrior

def test_spread_shot_orthogonal_damage():
    """Test if Spread Shot deals damage to orthogonal units"""
    print("🎯 TEST 1: SPREAD SHOT ORTHOGONAL DAMAGE")
    print("=" * 50)
    
    game = Game()
    hunter = Hunter(1)
    target = Warrior(2)
    
    # Set up positions - hunter shoots north, target is north of hunter
    hunter.position = (4, 4)
    target.position = (3, 4)  # Directly north (orthogonal)
    
    hunter.board = game.board
    game.board.add_unit(hunter, 4, 4)
    game.board.add_unit(target, 3, 4)
    
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Hunter at: {hunter.position}")
    print(f"Target at: {target.position} (orthogonal - north)")
    print(f"Target HP before: {target.health}")
    
    # Use Spread Shot targeting north
    success = hunter.use_ability(5, target.position, game)  # Spread Shot
    
    print(f"Spread Shot success: {success}")
    print(f"Target HP after: {target.health}")
    
    damage_dealt = 7 - target.health  # Warrior starts with 7 HP
    print(f"Damage dealt: {damage_dealt}")
    
    if damage_dealt > 0:
        print("✅ Spread Shot deals damage to orthogonal units")
    else:
        print("❌ BUG: Spread Shot doesn't deal damage to orthogonal units")
    
    return damage_dealt > 0

def test_ricochet_shot_multiple_bounces():
    """Test if Ricochet Shot ricochets multiple times"""
    print("\n🏹 TEST 2: RICOCHET SHOT MULTIPLE BOUNCES")
    print("=" * 50)
    
    game = Game()
    hunter = Hunter(1)
    target1 = Warrior(2)
    target2 = Warrior(2)
    target3 = Warrior(2)
    
    # Set up positions for multiple ricochets
    hunter.position = (4, 4)
    target1.position = (2, 6)  # First target (NE)
    target2.position = (6, 2)  # Second target (SW) 
    target3.position = (1, 1)  # Third target (NW)
    
    hunter.board = game.board
    game.board.add_unit(hunter, 4, 4)
    game.board.add_unit(target1, 2, 6)
    game.board.add_unit(target2, 6, 2)
    game.board.add_unit(target3, 1, 1)
    
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Hunter at: {hunter.position}")
    print(f"Target 1 at: {target1.position} - HP: {target1.health}")
    print(f"Target 2 at: {target2.position} - HP: {target2.health}")
    print(f"Target 3 at: {target3.position} - HP: {target3.health}")
    
    # Use Ricochet Shot
    success = hunter.use_ability(2, target1.position, game)  # Ricochet Shot
    
    print(f"\nRicochet Shot success: {success}")
    print(f"Target 1 HP after: {target1.health}")
    print(f"Target 2 HP after: {target2.health}")
    print(f"Target 3 HP after: {target3.health}")
    
    targets_hit = 0
    if target1.health < 7: targets_hit += 1
    if target2.health < 7: targets_hit += 1
    if target3.health < 7: targets_hit += 1
    
    print(f"Total targets hit: {targets_hit}")
    
    if targets_hit >= 2:
        print("✅ Ricochet Shot hits multiple targets")
    else:
        print("❌ BUG: Ricochet Shot doesn't ricochet properly")
    
    return targets_hit >= 2

def test_crippling_shot_duration():
    """Test if Crippling Shot effect has correct duration"""
    print("\n🦵 TEST 3: CRIPPLING SHOT DURATION")
    print("=" * 50)
    
    game = Game()
    hunter = Hunter(1)
    target = Warrior(2)
    
    # Set up positions
    hunter.position = (4, 4)
    target.position = (3, 5)  # Diagonal from hunter
    
    hunter.board = game.board
    game.board.add_unit(hunter, 4, 4)
    game.board.add_unit(target, 3, 5)
    
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Hunter at: {hunter.position}")
    print(f"Target at: {target.position}")
    print(f"Target crippled before: {target.has_status('Crippled')}")
    
    # Use Crippling Shot
    success = hunter.use_ability(6, target.position, game)  # Crippling Shot
    
    print(f"Crippling Shot success: {success}")
    print(f"Target crippled after: {target.has_status('Crippled')}")
    
    # Check duration
    if target.has_status('Crippled'):
        # Check the status effect duration
        from core.status_effects import StatusEffectType
        if hasattr(target, 'status_effects') and StatusEffectType.CRIPPLED in target.status_effects:
            duration = target.status_effects[StatusEffectType.CRIPPLED].duration
            print(f"Crippled effect duration: {duration}")
            
            if duration == 1:
                print("✅ Crippling Shot has correct duration (1 turn)")
                return True
            else:
                print(f"❌ BUG: Crippling Shot duration is {duration}, should be 1")
                return False
        else:
            print("❌ BUG: Crippled status not found in unified system")
            return False
    else:
        print("❌ BUG: Crippling Shot doesn't apply Crippled status")
        return False

def test_crippling_shot_movement_restriction():
    """Test if Crippled units can't move"""
    print("\n🚫 TEST 4: CRIPPLED MOVEMENT RESTRICTION")
    print("=" * 50)
    
    game = Game()
    hunter = Hunter(1)
    target = Warrior(2)
    
    # Set up positions
    hunter.position = (4, 4)
    target.position = (3, 5)
    
    hunter.board = game.board
    game.board.add_unit(hunter, 4, 4)
    game.board.add_unit(target, 3, 5)
    
    game.current_player = 1
    game.current_player_ap = 10
    
    # Use Crippling Shot
    hunter.use_ability(6, target.position, game)
    
    # Switch to player 2 and try to move the crippled unit
    game.current_player = 2
    game.current_player_ap = 10
    target.has_acted_this_turn = False
    
    print(f"Target crippled: {target.has_status('Crippled')}")
    
    # Check if unit can move
    can_move = target.status_effect_manager.can_unit_move(target)
    print(f"Can crippled unit move: {can_move}")
    
    # Get valid moves
    valid_moves = target.get_valid_moves(game.board)
    print(f"Valid moves for crippled unit: {len(valid_moves)}")
    
    if not can_move and len(valid_moves) == 0:
        print("✅ Crippled units cannot move")
        return True
    else:
        print("❌ BUG: Crippled units can still move")
        return False

def main():
    print("🐛 HUNTER ABILITY ISSUES INVESTIGATION")
    print("=" * 60)
    
    results = []
    
    # Test each issue
    results.append(test_spread_shot_orthogonal_damage())
    results.append(test_ricochet_shot_multiple_bounces())
    results.append(test_crippling_shot_duration())
    results.append(test_crippling_shot_movement_restriction())
    
    print("\n" + "=" * 60)
    print("📊 SUMMARY OF ISSUES:")
    print("-" * 30)
    
    issues = [
        "Spread Shot orthogonal damage",
        "Ricochet Shot multiple bounces", 
        "Crippling Shot duration",
        "Crippled movement restriction"
    ]
    
    for i, (issue, result) in enumerate(zip(issues, results)):
        status = "✅ WORKING" if result else "❌ BROKEN"
        print(f"{i+1}. {issue}: {status}")
    
    working_count = sum(results)
    print(f"\n📈 Overall: {working_count}/{len(results)} issues resolved")
    
    if working_count == len(results):
        print("🎉 All Hunter abilities working correctly!")
    else:
        print("🔧 Some Hunter abilities need fixes")

if __name__ == "__main__":
    main()

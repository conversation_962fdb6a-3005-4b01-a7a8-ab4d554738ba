#!/usr/bin/env python3
"""
Debug configuration application to see what's working and what's not
"""

import pygame
import json
from units.warrior import Warrior
from config_loader import <PERSON>fig<PERSON><PERSON><PERSON>

def debug_config_application():
    """Debug the configuration application process"""
    print("🔍 DEBUGGING CONFIGURATION APPLICATION")
    print("=" * 45)
    
    pygame.init()
    
    # Test 1: Check current configuration file
    print("📋 TEST 1: Current Configuration File")
    print("-" * 37)
    
    try:
        with open("game_balance_config.json", 'r') as f:
            config_data = json.load(f)
        
        warrior_class_data = config_data.get("class_data", {}).get("Warrior", {})
        warrior_ability_data = config_data.get("ability_data", {}).get("Warrior", {})
        
        print(f"Warrior Class Data:")
        print(f"  HP: {warrior_class_data.get('hp', 'NOT SET')}")
        print(f"  Movement: {warrior_class_data.get('movement', 'NOT SET')}")
        
        print(f"\nWarrior Ability Data:")
        for ability_name, ability_stats in warrior_ability_data.items():
            print(f"  {ability_name}:")
            print(f"    AP Cost: {ability_stats.get('ap_cost', 'NOT SET')}")
            print(f"    Damage: {ability_stats.get('damage', 'NOT SET')}")
            print(f"    Cooldown: {ability_stats.get('cooldown', 'NOT SET')}")
        
    except Exception as e:
        print(f"❌ Error reading config file: {e}")
        return False
    
    # Test 2: Create Warrior and check initial values
    print(f"\n📋 TEST 2: Warrior Creation")
    print("-" * 28)
    
    warrior = Warrior(1)
    
    print(f"Warrior after creation:")
    print(f"  Health: {warrior.health}/{warrior.max_health}")
    print(f"  Abilities count: {len(warrior.abilities)}")
    
    for i, ability in enumerate(warrior.abilities):
        print(f"  {i}: {ability.name}")
        print(f"     AP Cost: {ability.ap_cost}")
        print(f"     Cooldown: {ability.cooldown}")
        if hasattr(ability, 'damage'):
            print(f"     Damage: {ability.damage}")
        else:
            print(f"     Damage: NO DAMAGE ATTRIBUTE")
    
    # Test 3: Check ConfigLoader values
    print(f"\n📋 TEST 3: ConfigLoader Values")
    print("-" * 31)
    
    config_loader = ConfigLoader()
    
    print(f"ConfigLoader results:")
    print(f"  Warrior HP: {config_loader.get_class_hp('Warrior')}")
    print(f"  Warrior Movement: {config_loader.get_class_movement('Warrior')}")
    print(f"  Move AP Cost: {config_loader.get_ability_ap_cost('Warrior', 'Move')}")
    print(f"  Attack AP Cost: {config_loader.get_ability_ap_cost('Warrior', 'Attack')}")
    print(f"  Shield Bash AP Cost: {config_loader.get_ability_ap_cost('Warrior', 'Shield Bash')}")
    print(f"  Shield Bash Damage: {config_loader.get_ability_damage('Warrior', 'Shield Bash')}")
    print(f"  Shield Bash Cooldown: {config_loader.get_ability_cooldown('Warrior', 'Shield Bash')}")
    
    # Test 4: Manual configuration application
    print(f"\n📋 TEST 4: Manual Configuration Application")
    print("-" * 42)
    
    print(f"Before manual application:")
    print(f"  Health: {warrior.health}/{warrior.max_health}")
    print(f"  Move AP: {warrior.abilities[0].ap_cost}")
    print(f"  Shield Bash AP: {warrior.abilities[3].ap_cost}")
    
    # Apply configuration manually
    config_loader.apply_configuration_to_unit(warrior)
    
    print(f"\nAfter manual application:")
    print(f"  Health: {warrior.health}/{warrior.max_health}")
    print(f"  Move AP: {warrior.abilities[0].ap_cost}")
    print(f"  Shield Bash AP: {warrior.abilities[3].ap_cost}")
    
    # Test 5: Check what's different
    print(f"\n📋 TEST 5: Identify Issues")
    print("-" * 26)
    
    # Check HP
    expected_hp = config_loader.get_class_hp('Warrior')
    actual_hp = warrior.max_health
    if expected_hp != actual_hp:
        print(f"❌ HP Issue: Expected {expected_hp}, Got {actual_hp}")
    else:
        print(f"✅ HP Working: {actual_hp}")
    
    # Check Move AP
    expected_move_ap = config_loader.get_ability_ap_cost('Warrior', 'Move')
    actual_move_ap = warrior.abilities[0].ap_cost
    if expected_move_ap != actual_move_ap:
        print(f"❌ Move AP Issue: Expected {expected_move_ap}, Got {actual_move_ap}")
        print(f"   Move ability type: {type(warrior.abilities[0])}")
    else:
        print(f"✅ Move AP Working: {actual_move_ap}")
    
    # Check Shield Bash
    expected_sb_ap = config_loader.get_ability_ap_cost('Warrior', 'Shield Bash')
    actual_sb_ap = warrior.abilities[3].ap_cost
    if expected_sb_ap != actual_sb_ap:
        print(f"❌ Shield Bash AP Issue: Expected {expected_sb_ap}, Got {actual_sb_ap}")
    else:
        print(f"✅ Shield Bash AP Working: {actual_sb_ap}")
    
    # Check Shield Bash Damage
    expected_sb_damage = config_loader.get_ability_damage('Warrior', 'Shield Bash')
    if hasattr(warrior.abilities[3], 'damage'):
        actual_sb_damage = warrior.abilities[3].damage
        if expected_sb_damage != actual_sb_damage:
            print(f"❌ Shield Bash Damage Issue: Expected {expected_sb_damage}, Got {actual_sb_damage}")
        else:
            print(f"✅ Shield Bash Damage Working: {actual_sb_damage}")
    else:
        print(f"❌ Shield Bash Damage Issue: No damage attribute on ability")
    
    pygame.quit()
    return True

if __name__ == "__main__":
    debug_config_application()

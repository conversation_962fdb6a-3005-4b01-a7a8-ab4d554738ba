import pygame
# from game_board import <PERSON><PERSON><PERSON>_SIZE, CELL_SIZE, BOARD_PADDING # Not directly used, or available via const or game.board
from units import <PERSON>, Hunter # Specific unit classes for isinstance checks or properties
import game_ui # For updating UI elements like ability buttons
# from game_constants import STATE_GAME_OVER, STATE_PLAYING # Use const.*
import game_constants as const # Import constants

# Constants from game.py that might be used by these functions
# It's often better to pass these via the 'game' object (e.g. game.STATE_GAME_OVER)
# For now, if they are critical and not on 'game', they might need to be passed or imported from a consts file

def process_game_click(game, pos):
    """Handle clicks during the gameplay phase."""
    # Check if end turn button was clicked
    if game.end_turn_button.collidepoint(pos):
        end_turn(game)
        return True # Indicate event was handled

    # Check if escape button was clicked
    if game.escape_button.collidepoint(pos):
        return False  # Signal to exit the game loop (main menu)

    # Check if ability button was clicked
    for button_rect, ability_idx, ability_obj in game.ability_buttons: # Unpack fully
        if button_rect.collidepoint(pos):
            if game.selected_unit and game.selected_unit.can_use_ability(ability_idx, game):
                # Special handling for Move ability (index 0)
                if ability_idx == 0:  
                    unit_to_move = game.selected_unit
                    # Deselecting and reselecting to show moves is a bit clunky.
                    # Better: set a state like 'awaiting_move_target'
                    # For now, keep existing logic pattern of re-triggering select_unit
                    # Clear other selections first
                    game.selected_ability = None
                    game.valid_ability_targets = []
                    game.directional_ability = False
                    game.multi_stage_ability = False
                    # Re-select to show move targets
                    select_unit(game, unit_to_move.position) 
                    return True
                
                game.selected_ability = ability_idx
                game.valid_moves = [] # Clear move highlights when selecting an ability
                game.valid_attacks = [] # Clear attack highlights
                
                current_ability = game.selected_unit.abilities[game.selected_ability]
                game.valid_ability_targets = game.selected_unit.get_ability_targets(game.selected_ability, game.board)
                
                # Reset multi-stage/directional flags before checking the new ability
                game.multi_stage_ability = False
                game.directional_ability = False

                if hasattr(game.selected_unit, 'multi_stage_abilities') and \
                   current_ability.name in game.selected_unit.multi_stage_abilities:
                    game.multi_stage_ability = True
                    game.ability_stage = 0
                    game.ability_targets = []
                
                if hasattr(game.selected_unit, 'directional_abilities') and \
                   current_ability.name in game.selected_unit.directional_abilities:
                    game.directional_ability = True
                return True

    # Check if a board position was clicked
    board_pos = game.board.get_cell_from_pos(pos)
    if board_pos:
        row, col = board_pos
        
        # If an ability is selected and this is a valid target
        if game.selected_ability is not None and game.selected_unit is not None and (row, col) in game.valid_ability_targets:
            old_pos = game.selected_unit.position # Store old position before ability use
            game.selected_unit.board = game.board # Ensure unit has board context
            
            # Simplified multi-stage logic placeholder (original was very specific to Hunter Ricochet)
            # This needs to be generalized or handled per-unit if other multi-stage abilities exist.
            if game.multi_stage_ability: 
                print(f"Multi-stage ability target selected: {(row,col)}. Let unit handle it.")

            success = game.selected_unit.use_ability(game.selected_ability, (row, col), game) # Pass game object
            
            if success:
                new_pos = game.selected_unit.position # Get new position
                # If the ability was a move, or resulted in a position change
                if old_pos != new_pos or game.selected_unit.abilities[game.selected_ability].name == "Move":
                    # Ensure the unit actually moved before calling board.move_unit
                    # (e.g. ability might not always result in a move)
                    # Also, some abilities might move the unit (like Rogue's Shadow Step)
                    # and game.board.units needs to be updated.
                    if game.board.units.get(old_pos) == game.selected_unit: # Check if unit was indeed at old_pos
                         game.board.move_unit(old_pos, new_pos)
                    elif (row,col) == new_pos : # If the target_pos for ability is the new_pos
                         # This case might be for abilities that place the unit at target_pos
                         # game.board.add_unit might be more appropriate if it wasn't on board or moved from off-board
                         # However, use_ability implies it was on board.
                         # We need a robust way to ensure old_pos is valid if unit moved.
                         # For now, if it's a move ability, we assume it moved from old_pos.
                         if game.selected_unit.abilities[game.selected_ability].name == "Move":
                             game.board.move_unit(old_pos, new_pos)


            finish_ability_use(game, success)
            return True

        # If we have a unit selected (and no ability is being targeted)
        if game.selected_unit and game.selected_ability is None:
            # If clicked on a valid move position
            if (row, col) in game.valid_moves: # AP check is implicit in can_use_ability for move (idx 0)
                old_pos = game.selected_unit.position # Store old position
                game.selected_unit.board = game.board
                success = game.selected_unit.use_ability(0, (row, col), game) # Use Move ability (index 0)
                if success:
                    new_pos = game.selected_unit.position # Get new position
                    if old_pos != new_pos: # Ensure it actually moved
                        game.board.move_unit(old_pos, new_pos)
                finish_ability_use(game, success)
                return True

            # If clicked on a valid attack position
            elif (row, col) in game.valid_attacks: # AP check for basic attack (idx 1)
                # This is for *direct click to attack*
                game.selected_unit.board = game.board # Ensure unit has current board reference
                success = game.selected_unit.use_ability(1, (row,col), game) # Pass game object
                # unit.use_ability for Attack handles damage and board.units removal if target dies.
                finish_ability_use(game, success)
                return True
            
            # If clicked on another friendly unit, select it instead
            elif (row, col) in game.board.units and game.board.units[(row, col)].player_id == game.current_player:
                select_unit(game, (row, col))
                return True
            
            # If clicked elsewhere (empty or enemy when not attacking/moving), deselect the unit
            else:
                cancel_selection(game)
                return True
        
        # If no unit is selected, check if we're clicking on one of our units to select it
        elif (row, col) in game.board.units and game.board.units[(row, col)].player_id == game.current_player:
            select_unit(game, (row,col))
            return True
            
    return True # Default return if no other specific action taken by click

def reset_turn(game):
    """Reset at the start of a turn with NEW GLOBAL AP SYSTEM."""
    # Initialize global AP for the current player's turn
    game.start_player_turn(game.current_player)

    # Process status effects and reset unit states
    for unit_pos, unit in list(game.board.units.items()): # Use list to avoid modification issues if unit dies
        if unit.player_id == game.current_player:
            # Process status effects at start of turn (NEW STATUS SYSTEM)
            if hasattr(unit, 'status_manager'):
                unit.status_manager.tick_turn_start(game)
                # Check if unit died from status effects
                if not unit.is_alive():
                    game.board.remove_unit(unit.position)
                    print(f"{unit.name} died from status effects.")
                    if check_game_over(game):
                        return # Game is over, no further turn processing
                    continue # Skip AP reset etc. for this unit as it's gone

            # Apply start-of-turn effects using the new status effect system
            unit.tick_statuses(game=game)
            if not unit.is_alive():
                game.board.remove_unit(unit.position)
                print(f"{unit.name} died from status effects.")
                # Check game over immediately if a unit dies from status effects at turn start
                if check_game_over(game):
                    return # Game is over, no further turn processing
                continue # Skip AP reset etc. for this unit as it's gone

            # NOTE: AP reset is now handled by global AP system in game.start_player_turn()
            # unit.reset_ap(game=game) # REMOVED - using global AP system

            # Clear temporary statuses (but keep AP reset logic for other effects)
            if hasattr(unit, 'reset_turn_effects'):
                unit.reset_turn_effects(game=game)

            # Tick down ability cooldowns for current player's units
            for ability in unit.abilities:
                ability.tick_cooldown()
        else: # For units NOT of the current player
            # Process start-of-their-opponent's-turn effects (e.g. some status expirations)
            # unit.reset_turn() in units_core handles some of this (immobilized, stunned, poison turns)
            # Poison damage is now handled above for the active player.
            
            # Sanctuary and Divine Protection might wear off at start of opponent's turn or start of their own next turn.
            # Unit.reset_turn() in units_core handles some generic end-of-turn clears (like Divine Protection itself).
            # Cleric's sanctuary_until should be checked here or on the unit against game.turn_count.
            # This checks if Sanctuary, applied by another player, wears off at the start of this player's turn.
            if hasattr(unit, 'sanctuary_until') and unit.sanctuary_until == game.turn_count:
                if unit.sanctuary: # Only print if it was active
                    unit.sanctuary = False
                    # unit.sanctuary_until = 0 # reset_turn in unit_core might also do this, or it's fine here
                    print(f"{unit.name}'s Sanctuary (cast by opponent) has worn off.")

    # Deselect unit and clear actions
    cancel_selection(game)
    game_ui.update_ability_buttons(game) # Ensure buttons are cleared/updated

def end_turn(game):
    """End the current player's turn."""
    # Apply end-of-turn effects for the current player's units
    for unit_pos, unit in list(game.board.units.items()):
        if unit.player_id == game.current_player:
            unit.reset_turn(game=game) # Handles expiration of statuses like immobilized, stunned for current player's units
            if not unit.is_alive(): # If status effects made unit die
                game.board.remove_unit(unit_pos)

    if check_game_over(game): # Check if game ended due to end-of-turn effects
        return

    # Switch players
    game.current_player = 3 - game.current_player  # Alternates between 1 and 2

    # Increment turn count only when it's Player 1's turn again
    if game.current_player == 1:
        game.turn_count += 1
        # Tick cooldowns for ALL units at the start of a new game round (P1's turn)
        # This was previously only for current player in reset_turn. Global might be better.
        # For now, reset_turn handles current player's cooldowns at their turn start.

    print(f"--- Player {game.current_player}'s Turn {game.turn_count} --- ")
    reset_turn(game) # Setup for the new player's turn

def check_game_over(game):
    """Check if the game is over (when a player's King unit is defeated)."""
    if game.state == const.STATE_GAME_OVER: # Use const
        return True
        
    kings_found = {1: False, 2: False}
    kings_alive = {1: False, 2: False}

    for unit_pos, unit in game.board.units.items():
        if isinstance(unit, King):
            kings_found[unit.player_id] = True
            if unit.is_alive():
                kings_alive[unit.player_id] = True
    
    # Scenario 1: A King is defeated
    if kings_found[1] and not kings_alive[1]:
        game.winner = 2
        game.state = const.STATE_GAME_OVER # Use const
        print(f"Player 1's King defeated! Player 2 Wins!")
        return True
    if kings_found[2] and not kings_alive[2]:
        game.winner = 1
        game.state = const.STATE_GAME_OVER # Use const
        print(f"Player 2's King defeated! Player 1 Wins!")
        return True

    # Scenario 2: A player has no King on board at all (e.g., if King wasn't placed or removed by other means)
    # This might be redundant if setup requires King and King is only removed by defeat.
    # if not kings_found[1] and game.setup_phase > 2: # game.setup_phase > 2 implies setup is complete for both
    #     game.winner = 2; game.state = STATE_GAME_OVER; return True
    # if not kings_found[2] and game.setup_phase > 2:
    #     game.winner = 1; game.state = STATE_GAME_OVER; return True
        
    return False

def finish_ability_use(game, success):
    """Helper method to clean up after ability use."""
    # Reset selection state related to ability targeting
    game.selected_ability = None
    game.valid_ability_targets = []
    game.multi_stage_ability = False
    game.ability_stage = 0
    game.ability_targets = []
    game.directional_ability = False
    game.highlighted_positions = []
    game.cone_color = None

    # Check for defeated units globally after any ability that might cause deaths
    defeated_units_exist = False
    for unit_pos in list(game.board.units.keys()): # Iterate over a copy of keys
        unit = game.board.units.get(unit_pos) # Get unit safely
        if unit and not unit.is_alive():
            game.board.remove_unit(unit_pos) # remove_unit handles deletion from game.board.units
            print(f"{unit.name} at {unit_pos} was defeated.")
            defeated_units_exist = True
    
    if defeated_units_exist:
        if check_game_over(game):
            # If game over, no further updates needed for selected unit
            game_ui.update_ability_buttons(game) # Update UI to reflect no selection or game over state
            return

    if game.selected_unit: # If unit wasn't defeated or deselected by ability
        if not game.selected_unit.is_alive(): # Double check if selected unit itself died
            cancel_selection(game)
        elif game.selected_unit.current_ap <= 0:
            print(f"{game.selected_unit.name} has no AP remaining. Deselecting.")
            cancel_selection(game)
        else:
            # Update the valid moves and attacks for the selected unit
            game.valid_moves = game.selected_unit.get_valid_moves(game.board)
            game.valid_attacks = game.selected_unit.get_valid_attacks(game.board)
    else: # No selected unit (e.g. it died and was cleared by cancel_selection)
        game.valid_moves = []
        game.valid_attacks = []

    game_ui.update_ability_buttons(game) # Update button states based on new AP, cooldowns, or selection

def cancel_selection(game):
    """Cancel the current unit and ability selection."""
    game.selected_unit = None
    game.valid_moves = []
    game.valid_attacks = []
    game.selected_ability = None
    game.valid_ability_targets = []
    game.directional_ability = False
    game.highlighted_positions = []
    game.cone_color = None
    game.multi_stage_ability = False
    game.ability_stage = 0
    game.ability_targets = []
    game_ui.update_ability_buttons(game) # Update UI to clear ability buttons

def select_unit(game, pos_tuple):
    """Select a unit at the given board position (row, col)."""
    # Deselect any previous ability targetting
    game.selected_ability = None
    game.valid_ability_targets = []
    game.directional_ability = False
    game.multi_stage_ability = False

    unit = game.board.units.get(pos_tuple)
    if unit and unit.player_id == game.current_player:
        game.selected_unit = unit
        game.valid_moves = game.selected_unit.get_valid_moves(game.board)
        game.valid_attacks = game.selected_unit.get_valid_attacks(game.board)
    else: # Clicked on empty or enemy, or not current player's unit
        cancel_selection(game)
    
    game_ui.update_ability_buttons(game)
#!/usr/bin/env python3
"""
Test that all configuration settings are working properly
"""

import pygame
import json
from units.warrior import Warrior
from units.mage import Mage
from menu_screens.new_config_menu import NewConfigMenu

def test_all_config_working():
    """Test that all configuration settings work"""
    print("🎮 TESTING ALL CONFIGURATION SETTINGS")
    print("=" * 42)
    
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    clock = pygame.time.Clock()
    
    # Test 1: Create config menu and modify values
    print("📋 TEST 1: Modify Configuration")
    print("-" * 32)
    
    config_menu = NewConfigMenu(screen, clock)
    
    # Modify Warrior settings
    print("Modifying Warrior settings:")
    config_menu.class_data["Warrior"]["hp"] = 20
    config_menu.class_data["Warrior"]["movement"] = 3
    
    config_menu.ability_data["Warrior"]["Move"]["ap_cost"] = 2
    config_menu.ability_data["Warrior"]["Attack"]["ap_cost"] = 3
    config_menu.ability_data["Warrior"]["Attack"]["damage"] = 4
    config_menu.ability_data["Warrior"]["Shield Bash"]["ap_cost"] = 4
    config_menu.ability_data["Warrior"]["Shield Bash"]["damage"] = 5
    config_menu.ability_data["Warrior"]["Shield Bash"]["cooldown"] = 2
    
    print(f"  HP: 12 → {config_menu.class_data['Warrior']['hp']}")
    print(f"  Movement: 2 → {config_menu.class_data['Warrior']['movement']}")
    print(f"  Move AP: 1 → {config_menu.ability_data['Warrior']['Move']['ap_cost']}")
    print(f"  Attack AP: 2 → {config_menu.ability_data['Warrior']['Attack']['ap_cost']}")
    print(f"  Attack Damage: 2 → {config_menu.ability_data['Warrior']['Attack']['damage']}")
    print(f"  Shield Bash AP: 3 → {config_menu.ability_data['Warrior']['Shield Bash']['ap_cost']}")
    print(f"  Shield Bash Damage: 7.05 → {config_menu.ability_data['Warrior']['Shield Bash']['damage']}")
    print(f"  Shield Bash Cooldown: 3 → {config_menu.ability_data['Warrior']['Shield Bash']['cooldown']}")
    
    # Save configuration
    config_menu._save_configuration()
    print("✅ Configuration saved")
    
    # Test 2: Create new units and verify changes
    print(f"\n📋 TEST 2: Verify Changes Applied")
    print("-" * 35)
    
    # Reload configuration
    from config_loader import reload_configuration
    reload_configuration()
    
    # Create new warrior
    warrior = Warrior(1)
    
    print(f"New Warrior stats:")
    print(f"  HP: {warrior.health}/{warrior.max_health}")
    
    for i, ability in enumerate(warrior.abilities):
        print(f"  {ability.name}:")
        print(f"    AP Cost: {ability.ap_cost}")
        print(f"    Damage: {ability.damage}")
        print(f"    Cooldown: {ability.cooldown}")
    
    # Test 3: Verify specific values
    print(f"\n📋 TEST 3: Verification Results")
    print("-" * 33)
    
    results = []
    
    # Check HP
    if warrior.max_health == 20:
        print("✅ HP: 20 (WORKING)")
        results.append(True)
    else:
        print(f"❌ HP: Expected 20, Got {warrior.max_health}")
        results.append(False)
    
    # Check Move AP
    if warrior.abilities[0].ap_cost == 2:
        print("✅ Move AP: 2 (WORKING)")
        results.append(True)
    else:
        print(f"❌ Move AP: Expected 2, Got {warrior.abilities[0].ap_cost}")
        results.append(False)
    
    # Check Attack AP
    if warrior.abilities[1].ap_cost == 3:
        print("✅ Attack AP: 3 (WORKING)")
        results.append(True)
    else:
        print(f"❌ Attack AP: Expected 3, Got {warrior.abilities[1].ap_cost}")
        results.append(False)
    
    # Check Attack Damage
    if warrior.abilities[1].damage == 4:
        print("✅ Attack Damage: 4 (WORKING)")
        results.append(True)
    else:
        print(f"❌ Attack Damage: Expected 4, Got {warrior.abilities[1].damage}")
        results.append(False)
    
    # Check Shield Bash AP
    if warrior.abilities[3].ap_cost == 4:
        print("✅ Shield Bash AP: 4 (WORKING)")
        results.append(True)
    else:
        print(f"❌ Shield Bash AP: Expected 4, Got {warrior.abilities[3].ap_cost}")
        results.append(False)
    
    # Check Shield Bash Damage
    if warrior.abilities[3].damage == 5:
        print("✅ Shield Bash Damage: 5 (WORKING)")
        results.append(True)
    else:
        print(f"❌ Shield Bash Damage: Expected 5, Got {warrior.abilities[3].damage}")
        results.append(False)
    
    # Check Shield Bash Cooldown
    if warrior.abilities[3].cooldown == 2:
        print("✅ Shield Bash Cooldown: 2 (WORKING)")
        results.append(True)
    else:
        print(f"❌ Shield Bash Cooldown: Expected 2, Got {warrior.abilities[3].cooldown}")
        results.append(False)
    
    pygame.quit()
    
    # Summary
    working_count = sum(results)
    total_count = len(results)
    
    print(f"\n" + "=" * 42)
    print("🎯 CONFIGURATION TEST SUMMARY")
    print("-" * 30)
    print(f"Working: {working_count}/{total_count}")
    
    if working_count == total_count:
        print("🎉 ALL CONFIGURATION SETTINGS WORKING!")
        print("")
        print("✅ HP sliders work")
        print("✅ Movement sliders work")
        print("✅ Move AP cost works")
        print("✅ Attack AP cost works")
        print("✅ Attack damage works")
        print("✅ Ability AP costs work")
        print("✅ Ability damage works")
        print("✅ Ability cooldowns work")
        print("")
        print("🎮 Your configuration system is fully functional!")
        return True
    else:
        print(f"❌ {total_count - working_count} settings not working")
        print("Check the errors above")
        return False

if __name__ == "__main__":
    success = test_all_config_working()
    if success:
        print(f"\n🚀 READY TO PLAY!")
        print("All configuration settings are working correctly!")
    else:
        print(f"\n🔧 NEEDS FIXING!")
        print("Some configuration settings are not working.")
    
    import sys
    sys.exit(0 if success else 1)

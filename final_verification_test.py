#!/usr/bin/env python3
"""
Final verification test for the complete system
"""

import pygame
import sys

def test_game_launch():
    """Test that the game launches without crashing"""
    print("🎮 FINAL VERIFICATION TEST")
    print("=" * 30)
    
    # Test 1: Import Test
    print("📋 TEST 1: Import Test")
    print("-" * 20)
    
    try:
        from game_state import Game
        from units.warrior import Warrior
        from units.mage import Mage
        from units.rogue import Rogue
        from menu_screens.new_config_menu import NewConfigMenu
        from config_loader import ConfigLoader
        print("✅ All imports successful")
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False
    
    # Test 2: Game Creation
    print(f"\n📋 TEST 2: Game Creation")
    print("-" * 24)
    
    pygame.init()
    
    try:
        game = Game()
        print("✅ Game instance created")
    except Exception as e:
        print(f"❌ Game creation failed: {e}")
        return False
    
    # Test 3: Unit Creation with Configuration
    print(f"\n📋 TEST 3: Unit Creation with Configuration")
    print("-" * 43)
    
    try:
        warrior = Warrior(1)
        mage = Mage(1)
        rogue = Rogue(1)
        
        print(f"✅ Units created:")
        print(f"   Warrior: {warrior.health}/{warrior.max_health} HP")
        print(f"   Mage: {mage.health}/{mage.max_health} HP")
        print(f"   Rogue: {rogue.health}/{rogue.max_health} HP")
        
        # Verify configuration was applied
        if warrior.max_health == 15:  # From our config
            print("✅ Configuration applied to units")
        else:
            print(f"❌ Configuration not applied (expected 15, got {warrior.max_health})")
            return False
            
    except Exception as e:
        print(f"❌ Unit creation failed: {e}")
        return False
    
    # Test 4: AP System
    print(f"\n📋 TEST 4: AP System")
    print("-" * 19)
    
    try:
        # Set up units on board
        warrior.position = (4, 4)
        mage.position = (4, 5)
        
        game.board.units = {(4, 4): warrior, (4, 5): mage}
        warrior.board = game.board
        mage.board = game.board
        
        # Start turn
        game.start_player_turn(1)
        game.current_player_ap = 5
        
        # Test warrior action
        move_success = warrior.use_ability(0, (4, 3), game)
        second_action = warrior.use_ability(1, (4, 2), game)
        
        if move_success and not second_action:
            print("✅ One action per turn enforced")
        else:
            print(f"❌ AP system not working (move: {move_success}, second: {second_action})")
            return False
            
    except Exception as e:
        print(f"❌ AP system test failed: {e}")
        return False
    
    # Test 5: Configuration Menu
    print(f"\n📋 TEST 5: Configuration Menu")
    print("-" * 30)
    
    try:
        screen = pygame.display.set_mode((800, 600))
        clock = pygame.time.Clock()
        
        config_menu = NewConfigMenu(screen, clock)
        
        # Test configuration change
        original_hp = config_menu.class_data["Warrior"]["hp"]
        config_menu.class_data["Warrior"]["hp"] = 20
        config_menu._save_configuration()
        
        print(f"✅ Configuration menu working")
        print(f"   Changed Warrior HP: {original_hp} → 20")
        
    except Exception as e:
        print(f"❌ Configuration menu test failed: {e}")
        return False
    
    pygame.quit()
    
    print(f"\n" + "=" * 30)
    print("🎯 FINAL VERIFICATION SUMMARY")
    print("-" * 29)
    print("✅ All imports working")
    print("✅ Game creation working")
    print("✅ Unit creation working")
    print("✅ Configuration applied")
    print("✅ AP system enforced")
    print("✅ Configuration menu working")
    
    print(f"\n🎮 SYSTEM FULLY OPERATIONAL!")
    print("   • One action per turn ✅")
    print("   • Configuration integration ✅")
    print("   • Real-time balance changes ✅")
    print("   • No crashes ✅")
    
    return True

if __name__ == "__main__":
    success = test_game_launch()
    if success:
        print(f"\n🎉 ALL SYSTEMS GO!")
        print("Your game is ready to play!")
        print("")
        print("🚀 TO PLAY:")
        print("   1. Run: python main_menu.py")
        print("   2. Click 'Start Game'")
        print("   3. Enjoy balanced gameplay!")
        print("")
        print("⚙️ TO CONFIGURE:")
        print("   1. Run: python main_menu.py")
        print("   2. Click 'Options'")
        print("   3. Click 'Game Configuration'")
        print("   4. Adjust settings and save!")
    else:
        print(f"\n❌ SYSTEM NOT READY!")
        print("Check the errors above.")
    
    sys.exit(0 if success else 1)

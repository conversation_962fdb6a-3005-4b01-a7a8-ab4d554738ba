#!/usr/bin/env python3
"""
Configuration loader that applies balance settings to units
"""

import json
import os

class ConfigLoader:
    """Loads and applies configuration settings to units"""
    
    def __init__(self):
        self.config_data = None
        self.load_configuration()
    
    def load_configuration(self):
        """Load configuration from file"""
        try:
            if os.path.exists("game_balance_config.json"):
                with open("game_balance_config.json", 'r') as f:
                    self.config_data = json.load(f)
                print("Configuration loaded from game_balance_config.json")
            else:
                print("No configuration file found, using defaults")
                self.config_data = None
        except Exception as e:
            print(f"Error loading configuration: {e}")
            self.config_data = None
    
    def get_class_hp(self, class_name):
        """Get HP for a specific class"""
        if not self.config_data or "class_data" not in self.config_data:
            # Default HP values
            defaults = {
                "Warrior": 12,
                "Mage": 8,
                "Cleric": 10,
                "<PERSON>": 9,
                "<PERSON>": 10
            }
            return defaults.get(class_name, 10)
        
        class_data = self.config_data["class_data"].get(class_name, {})
        return class_data.get("hp", 10)
    
    def get_class_movement(self, class_name):
        """Get movement range for a specific class"""
        if not self.config_data or "class_data" not in self.config_data:
            # Default movement values
            defaults = {
                "Warrior": 2,
                "Mage": 2,
                "Cleric": 1,
                "Rogue": 3,
                "Hunter": 3
            }
            return defaults.get(class_name, 2)
        
        class_data = self.config_data["class_data"].get(class_name, {})
        return class_data.get("movement", 2)
    
    def get_ability_ap_cost(self, class_name, ability_name):
        """Get AP cost for a specific ability"""
        if not self.config_data or "ability_data" not in self.config_data:
            # Default AP costs
            defaults = {
                "Move": 1,
                "Attack": 2,
                "Fireball": 4,
                "Charge": 3,
                "Backstab": 2,
                "Heal": 2
            }
            return defaults.get(ability_name, 2)
        
        class_abilities = self.config_data["ability_data"].get(class_name, {})
        ability_data = class_abilities.get(ability_name, {})
        return ability_data.get("ap_cost", 2)
    
    def get_ability_damage(self, class_name, ability_name):
        """Get damage for a specific ability"""
        if not self.config_data or "ability_data" not in self.config_data:
            # Default damage values
            defaults = {
                "Attack": 2,
                "Fireball": 3,
                "Charge": 3,
                "Backstab": 4,
                "Ice Spike": 2
            }
            return defaults.get(ability_name, 0)
        
        class_abilities = self.config_data["ability_data"].get(class_name, {})
        ability_data = class_abilities.get(ability_name, {})
        return ability_data.get("damage", 0)

    def get_ability_heal_amount(self, class_name, ability_name):
        """Get healing amount for a specific ability"""
        if not self.config_data or "ability_data" not in self.config_data:
            # Default healing values
            defaults = {
                "Heal": 2,
                "Mass Heal": 1
            }
            return defaults.get(ability_name, 1)

        class_abilities = self.config_data["ability_data"].get(class_name, {})
        ability_data = class_abilities.get(ability_name, {})
        return ability_data.get("heal_amount", 1)

    def get_ability_cooldown(self, class_name, ability_name):
        """Get cooldown for a specific ability"""
        if not self.config_data or "ability_data" not in self.config_data:
            # Default cooldown values
            defaults = {
                "Fireball": 2,
                "Charge": 2,
                "Backstab": 2,
                "Ice Spike": 1,
                "Heal": 1
            }
            return defaults.get(ability_name, 0)
        
        class_abilities = self.config_data["ability_data"].get(class_name, {})
        ability_data = class_abilities.get(ability_name, {})
        return ability_data.get("cooldown", 0)
    
    def apply_configuration_to_unit(self, unit):
        """Apply configuration settings to a unit"""
        class_name = unit.__class__.__name__
        
        # Apply HP
        configured_hp = self.get_class_hp(class_name)
        unit.max_health = configured_hp
        unit.health = configured_hp
        
        # Apply movement (if unit has movement ability)
        if hasattr(unit, 'movement_range'):
            unit.movement_range = self.get_class_movement(class_name)
        
        # Apply ability configurations
        for ability in unit.abilities:
            ability_name = ability.name

            # Handle special ability name mappings
            config_ability_name = ability_name
            if ability_name == "Basic Attack":
                config_ability_name = "Attack"

            # Update AP cost
            configured_ap_cost = self.get_ability_ap_cost(class_name, config_ability_name)
            ability.ap_cost = configured_ap_cost

            # Update damage (if ability has damage)
            if hasattr(ability, 'damage'):
                configured_damage = self.get_ability_damage(class_name, config_ability_name)
                # Always apply damage, even if it's 0
                ability.damage = configured_damage

            # Update healing amount (if ability has healing)
            if hasattr(ability, 'heal_amount'):
                configured_heal_amount = self.get_ability_heal_amount(class_name, config_ability_name)
                ability.heal_amount = configured_heal_amount

            # Update cooldown
            configured_cooldown = self.get_ability_cooldown(class_name, config_ability_name)
            ability.cooldown = configured_cooldown
        
        print(f"Applied configuration to {class_name}: HP={configured_hp}")

# Global instance
config_loader = ConfigLoader()

def apply_config_to_unit(unit):
    """Convenience function to apply configuration to a unit"""
    config_loader.apply_configuration_to_unit(unit)

def reload_configuration():
    """Reload configuration from file"""
    config_loader.load_configuration()

def test_config_loader():
    """Test the configuration loader"""
    print("TESTING CONFIGURATION LOADER")
    print("=" * 30)
    
    # Test loading
    loader = ConfigLoader()
    
    # Test class configurations
    print(f"Warrior HP: {loader.get_class_hp('Warrior')}")
    print(f"Mage HP: {loader.get_class_hp('Mage')}")
    print(f"Rogue Movement: {loader.get_class_movement('Rogue')}")
    
    # Test ability configurations
    print(f"Fireball AP Cost: {loader.get_ability_ap_cost('Mage', 'Fireball')}")
    print(f"Fireball Damage: {loader.get_ability_damage('Mage', 'Fireball')}")
    print(f"Fireball Cooldown: {loader.get_ability_cooldown('Mage', 'Fireball')}")
    
    print("Configuration loader test complete!")

if __name__ == "__main__":
    test_config_loader()

#!/usr/bin/env python3
"""
Test the integration of the new config system with the main game
"""

import pygame
import sys
import os

def test_integration():
    """Test that the new config system integrates properly"""
    print("🎮 TESTING NEW CONFIG SYSTEM INTEGRATION")
    print("=" * 45)
    
    # Test 1: Import Test
    print("📋 TEST 1: Import Test")
    print("-" * 20)
    
    try:
        from menu_screens.new_config_menu import NewConfigMenu
        print("✅ NewConfigMenu imported successfully")
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False
    
    try:
        from menu_screens.options_menu import OptionsMenu
        print("✅ OptionsMenu imported successfully")
    except ImportError as e:
        print(f"❌ OptionsMenu import failed: {e}")
        return False
    
    # Test 2: Initialization Test
    print(f"\n📋 TEST 2: Initialization Test")
    print("-" * 28)
    
    pygame.init()
    screen = pygame.display.set_mode((800, 600))
    clock = pygame.time.Clock()
    
    try:
        config_menu = NewConfigMenu(screen, clock)
        print("✅ NewConfigMenu initialized successfully")
        print(f"   Classes available: {list(config_menu.class_data.keys())}")
        print(f"   Default mode: {config_menu.mode}")
        print(f"   Default class: {config_menu.selected_class}")
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return False
    
    # Test 3: Data Structure Test
    print(f"\n📋 TEST 3: Data Structure Test")
    print("-" * 29)
    
    # Check class data
    expected_classes = ["Warrior", "Mage", "Cleric", "Rogue", "Hunter"]
    actual_classes = list(config_menu.class_data.keys())
    
    if set(expected_classes) == set(actual_classes):
        print("✅ All expected classes present")
    else:
        print(f"❌ Class mismatch. Expected: {expected_classes}, Got: {actual_classes}")
        return False
    
    # Check ability data
    warrior_abilities = list(config_menu.ability_data["Warrior"].keys())
    
    if len(warrior_abilities) >= 5:  # At least basic abilities
        print(f"✅ Warrior has {len(warrior_abilities)} abilities")
        print(f"   Abilities: {', '.join(warrior_abilities[:5])}...")
    else:
        print(f"❌ Warrior missing abilities. Got: {warrior_abilities}")
        return False
    
    # Test 4: Configuration Save/Load Test
    print(f"\n📋 TEST 4: Save/Load Test")
    print("-" * 22)
    
    # Modify some values
    original_warrior_hp = config_menu.class_data["Warrior"]["hp"]
    config_menu.class_data["Warrior"]["hp"] = 15
    
    original_fireball_damage = config_menu.ability_data["Mage"]["Fireball"]["damage"]
    config_menu.ability_data["Mage"]["Fireball"]["damage"] = 5
    
    # Save configuration
    config_menu._save_configuration()
    print("✅ Configuration saved")
    
    # Reset values
    config_menu.class_data["Warrior"]["hp"] = original_warrior_hp
    config_menu.ability_data["Mage"]["Fireball"]["damage"] = original_fireball_damage
    
    # Load configuration
    config_menu._load_configuration()
    print("✅ Configuration loaded")
    
    # Check if values were restored
    if config_menu.class_data["Warrior"]["hp"] == 15:
        print("✅ Class data save/load working")
    else:
        print("❌ Class data save/load failed")
        return False
    
    if config_menu.ability_data["Mage"]["Fireball"]["damage"] == 5:
        print("✅ Ability data save/load working")
    else:
        print("❌ Ability data save/load failed")
        return False
    
    # Test 5: UI Component Test
    print(f"\n📋 TEST 5: UI Component Test")
    print("-" * 26)
    
    # Test slider creation
    config_menu.mode = "class"
    config_menu.selected_class = "Warrior"
    config_menu._create_sliders()
    
    if len(config_menu.sliders) == 2:  # HP and Movement
        print("✅ Class mode sliders created correctly")
    else:
        print(f"❌ Class mode slider count wrong. Expected 2, got {len(config_menu.sliders)}")
        return False
    
    # Test ability mode
    config_menu.mode = "abilities"
    config_menu.selected_class = "Mage"  # Switch to Mage for Fireball
    config_menu.selected_ability = "Fireball"
    config_menu._create_sliders()
    
    if len(config_menu.sliders) >= 2:  # At least damage and AP cost
        print("✅ Ability mode sliders created correctly")
    else:
        print(f"❌ Ability mode slider count wrong. Got {len(config_menu.sliders)}")
        return False
    
    pygame.quit()
    
    print(f"\n" + "=" * 45)
    print("🎯 INTEGRATION TEST SUMMARY")
    print("-" * 27)
    print("✅ All tests passed!")
    print("")
    print("🎮 NEW CONFIG SYSTEM READY!")
    print("   • Integrated with main menu")
    print("   • Class and ability configuration")
    print("   • Save/load functionality")
    print("   • Clean, bug-free interface")
    print("")
    print("🚀 HOW TO ACCESS:")
    print("   1. Run main_menu.py")
    print("   2. Click 'Options'")
    print("   3. Click 'Game Configuration'")
    print("   4. Configure classes and abilities!")
    
    return True

if __name__ == "__main__":
    success = test_integration()
    if success:
        print(f"\n🎉 INTEGRATION SUCCESSFUL!")
        print("The new config system is ready to use in your game!")
    else:
        print(f"\n❌ INTEGRATION FAILED!")
        print("Check the errors above and fix before using.")
    
    sys.exit(0 if success else 1)

# Project Goal: 1v1 Tactical Fantasy Prototype (Python/Pygame)

Create a functional prototype of a 1v1, turn-based tactical fantasy game. The prototype will be built using **Python and Pygame**. This version focuses on hotseat play (one player controls both sides) to test core mechanics, unit interactions, ability balance, and game flow on a 9x9 grid.

# Core Design Pillars:

*   **Engine:** Python with Pygame library.
*   **Action Economy:** **Global AP system** per player with progressive scaling (1→10 AP). Player 2 gets +1 AP early game advantage. Each unit can perform **one action per turn** (Move OR Basic Attack OR Skill). Action costs deduct from player's global AP pool.
*   **Unique Units:** Distinct roles via Special Classes (<PERSON>, <PERSON>, Mage), King (Objective), and Fodder.
*   **Complex Abilities:** 5 active Skills per Special class + 1 Passive each. Cooldowns apply.
*   **Tactical Systems:** Status Effects (Categorized: Buff/Debuff/Neutral), Knockback (Variable Amount, Collision Rules), Line of Sight (with exceptions), Friendly Fire rules.
*   **Targeting:** Specific patterns (Path-first for Lines, Custom Diagonal Area, Adjacent Sweep, etc.). Free targeting default.
*   **Loadout System (Slot-Specific Pools):** Players will eventually choose 1 skill for each of the 5 slots from a small, manually curated pool specific to that Class+Slot combination (defined later).
*   **Modularity:** Build systems using Python classes and modules for better organization and future expansion.
*   **Deferred Features:** King Leveling, Summoner Class focus, Map Zones/Variety, Invisibility, Resurrect/MC, full Customization UI/Backend are planned for *later* iterations. Facing/standard High Ground rules omitted initially due to unique movement patterns.

# Phase 0: Setup & Definitions

*   **(Done - Conceptual)** Board: 9x9 Grid concept.
*   **(Done - Implemented)** Core Economy: Global AP system (1→10 progressive scaling) per player. HP scale ~10. One action per unit per turn.
*   **(Done - Implemented)** Turn Structure: Player 1 -> Player 2. Global AP pool per player (progressive 1→10), strict 1 action per unit per turn enforcement.
*   **(Done - Conceptual)** Interaction Rules: LoS needed (mostly), FF possible, Units block movement.
*   **(Pending - Adaptation to Python)** Detailed Unit Definitions: Provide AI with finalized specs for **King, Fodder, Warrior, Archer, Mage** (HP, Movement [1 AP rule, specific patterns], Basic Attacks, 5 Skills [AP cost, range, target, effect, CD], 1 Passive).
*   **(Pending - Future Phase)** Skill Slot Pool Definition: Define *which* skills belong to the pool for Slot 1, 2, 3, 4, 5 for each class (needed before Customization Phase). For now, skills are just assigned to units. 
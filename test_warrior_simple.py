#!/usr/bin/env python3
"""
Simple test for Warrior abilities after fixes.
"""

import pygame
pygame.init()

from game_state import Game
from units.warrior import Warrior
from units.rogue import Rogue

def test_warrior_abilities():
    print("🗡️ TESTING WARRIOR ABILITIES AFTER FIXES")
    print("=" * 50)
    
    # Test 1: Cleave Attack
    print("🔥 TEST 1: Cleave Attack")
    game = Game()
    warrior = Warrior(1)
    target = Rogue(2)
    
    game.board.add_unit(warrior, 4, 4)
    game.board.add_unit(target, 4, 5)
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Target HP before: {target.health}")
    success = warrior.use_ability(2, target.position, game)
    print(f"Cleave Attack success: {success}")
    print(f"Target HP after: {target.health}")
    print()
    
    # Test 2: Shield Bash
    print("🛡️ TEST 2: Shield Bash")
    game = Game()
    warrior = Warrior(1)
    target = Rogue(2)
    
    game.board.add_unit(warrior, 4, 4)
    game.board.add_unit(target, 4, 5)
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Target stunned before: {target.has_status('Stunned')}")
    success = warrior.use_ability(3, target.position, game)
    print(f"Shield Bash success: {success}")
    print(f"Target stunned after: {target.has_status('Stunned')}")
    print()
    
    # Test 3: Defensive Stance
    print("🛡️ TEST 3: Defensive Stance")
    game = Game()
    warrior = Warrior(1)
    
    game.board.add_unit(warrior, 4, 4)
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Defensive stance before: {warrior.defensive_stance_active}")
    success = warrior.use_ability(5, warrior.position, game)
    print(f"Defensive Stance success: {success}")
    print(f"Defensive stance after: {warrior.defensive_stance_active}")
    print()
    
    # Test 4: Riposte
    print("⚔️ TEST 4: Riposte")
    game = Game()
    warrior = Warrior(1)
    
    game.board.add_unit(warrior, 4, 4)
    game.current_player = 1
    game.current_player_ap = 10
    
    print(f"Riposte active before: {warrior.riposte_active}")
    success = warrior.use_ability(6, warrior.position, game)
    print(f"Riposte success: {success}")
    print(f"Riposte active after: {warrior.riposte_active}")
    print()
    
    print("✅ All Warrior abilities tested successfully!")

if __name__ == "__main__":
    test_warrior_abilities()

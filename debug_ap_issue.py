#!/usr/bin/env python3
"""
Debug script to investigate King and <PERSON> AP issues after setup phase
"""

import pygame
from game_state import Game
from units.hunter import <PERSON>
from units.king import <PERSON>
from units.warrior import <PERSON>
from game_config import GAME_CONFIG

def debug_ap_issue():
    """Debug AP issues with <PERSON> and <PERSON> after setup phase"""
    pygame.init()
    
    print("🔍 DEBUGGING KING & HUNTER AP ISSUE 🔍")
    print("=" * 45)
    
    # Create game and units like the real game does
    game = Game()
    
    # Test King AP
    print("\n📋 KING AP ANALYSIS")
    print("-" * 20)
    
    king = King(1)
    print(f"King initial max_ap: {king.max_ap}")
    print(f"King initial current_ap: {king.current_ap}")
    print(f"King config max_ap: {GAME_CONFIG.get('king_config', {}).get('max_ap', 'NOT SET')}")
    
    # Apply filtering like the game does
    game._filter_unit_abilities(king)
    print(f"King max_ap after filtering: {king.max_ap}")
    print(f"King current_ap after filtering: {king.current_ap}")
    
    # Check passives
    print(f"King passives: {len(king.passive_manager.passive_abilities)}")
    for passive in king.passive_manager.passive_abilities:
        print(f"  • {passive.name}")
    
    # Test AP reset (what happens at turn start)
    print(f"\nTesting King AP reset...")
    king.reset_ap(game)
    print(f"King max_ap after reset: {king.max_ap}")
    print(f"King current_ap after reset: {king.current_ap}")
    
    # Test ability costs
    print(f"\nKing ability costs:")
    for i, ability in enumerate(king.abilities):
        print(f"  {i}: {ability.name} - Cost: {ability.ap_cost}")
        print(f"      Can use: {king.can_use_ability(i)}")
    
    # Test Hunter AP
    print("\n\n📋 HUNTER AP ANALYSIS")
    print("-" * 22)
    
    hunter = Hunter(1)
    print(f"Hunter initial max_ap: {hunter.max_ap}")
    print(f"Hunter initial current_ap: {hunter.current_ap}")
    print(f"Hunter config max_ap: {GAME_CONFIG.get('hunter_config', {}).get('max_ap', 'NOT SET')}")
    
    # Apply filtering like the game does
    game._filter_unit_abilities(hunter)
    print(f"Hunter max_ap after filtering: {hunter.max_ap}")
    print(f"Hunter current_ap after filtering: {hunter.current_ap}")
    
    # Check passives
    print(f"Hunter passives: {len(hunter.passive_manager.passive_abilities)}")
    for passive in hunter.passive_manager.passive_abilities:
        print(f"  • {passive.name}")
    
    # Test AP reset
    print(f"\nTesting Hunter AP reset...")
    hunter.reset_ap(game)
    print(f"Hunter max_ap after reset: {hunter.max_ap}")
    print(f"Hunter current_ap after reset: {hunter.current_ap}")
    
    # Test ability costs
    print(f"\nHunter ability costs:")
    for i, ability in enumerate(hunter.abilities):
        print(f"  {i}: {ability.name} - Cost: {ability.ap_cost}")
        print(f"      Can use: {hunter.can_use_ability(i)}")
    
    # Test Warrior for comparison (working unit)
    print("\n\n📋 WARRIOR AP ANALYSIS (CONTROL)")
    print("-" * 35)
    
    warrior = Warrior(1)
    print(f"Warrior initial max_ap: {warrior.max_ap}")
    print(f"Warrior initial current_ap: {warrior.current_ap}")
    
    game._filter_unit_abilities(warrior)
    print(f"Warrior max_ap after filtering: {warrior.max_ap}")
    print(f"Warrior current_ap after filtering: {warrior.current_ap}")
    
    warrior.reset_ap(game)
    print(f"Warrior max_ap after reset: {warrior.max_ap}")
    print(f"Warrior current_ap after reset: {warrior.current_ap}")
    
    print(f"\nWarrior ability costs:")
    for i, ability in enumerate(warrior.abilities):
        print(f"  {i}: {ability.name} - Cost: {ability.ap_cost}")
        print(f"      Can use: {warrior.can_use_ability(i)}")
    
    # Check if there's an issue with the passive system affecting AP
    print("\n\n📋 PASSIVE SYSTEM AP EFFECTS")
    print("-" * 30)
    
    # Test if Extra AP passive is causing issues
    from passive_system import PassiveType, PASSIVE_ABILITIES
    
    if PassiveType.EXTRA_AP in PASSIVE_ABILITIES:
        extra_ap_passive = PASSIVE_ABILITIES[PassiveType.EXTRA_AP]
        print(f"Extra AP passive exists: {extra_ap_passive.name}")
        print(f"Extra AP intensity: {extra_ap_passive.intensity}")
        
        # Test if Mage (who should have extra AP) works
        from units.mage import Mage
        mage = Mage(1)
        print(f"\nMage initial max_ap: {mage.max_ap}")
        game._filter_unit_abilities(mage)
        print(f"Mage max_ap after filtering: {mage.max_ap}")
        mage.reset_ap(game)
        print(f"Mage max_ap after reset: {mage.max_ap}")
        print(f"Mage current_ap after reset: {mage.current_ap}")
    
    # Check if there's a config issue
    print("\n\n📋 CONFIG VERIFICATION")
    print("-" * 20)
    
    print("King config:")
    king_config = GAME_CONFIG.get("king_config", {})
    for key, value in king_config.items():
        print(f"  {key}: {value}")
    
    print("\nHunter config:")
    hunter_config = GAME_CONFIG.get("hunter_config", {})
    for key, value in hunter_config.items():
        print(f"  {key}: {value}")
    
    print("\n" + "=" * 45)
    print("🔍 DIAGNOSIS COMPLETE")
    print("\nPossible issues:")
    print("1. Passive system affecting AP calculation incorrectly")
    print("2. Config values being overridden")
    print("3. AP reset not working properly for these units")
    print("4. Ability costs too high relative to max AP")
    
    print("\n💡 Check if:")
    print("• King/Hunter max_ap is being set correctly")
    print("• Passive effects are modifying AP incorrectly")
    print("• Config values match expected values")
    print("• AP reset is called properly after setup phase")

if __name__ == "__main__":
    debug_ap_issue()

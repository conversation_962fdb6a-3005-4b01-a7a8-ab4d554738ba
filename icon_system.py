import pygame
import math
from pathlib import Path

class IconManager:
    """
    Icon management system for classes and abilities.
    Handles loading, creating, and managing all game icons.
    """
    
    def __init__(self):
        self.icons = {}
        self.icon_size = 64  # Default icon size
        
        # Create icons directory
        self.icons_dir = Path("assets/icons")
        self.icons_dir.mkdir(parents=True, exist_ok=True)
        
        # Load all available icons
        self._load_icons()
        
        # Create missing icons
        self._create_missing_icons()
    
    def _load_icons(self):
        """Load all icon files from the assets/icons directory"""
        
        # Define icon categories
        icon_categories = {
            "classes": [
                "warrior.png", "mage.png", "hunter.png", "rogue.png", 
                "cleric.png", "king.png", "warlock.png", "paladin.png", 
                "druid.png", "bard.png", "pawn.png"
            ],
            "abilities": [
                # Warrior abilities
                "shield_bash.png", "charge.png", "taunt.png", "whirlwind.png",
                "berserker_rage.png", "defensive_stance.png", "intimidate.png",
                
                # Mage abilities  
                "fireball.png", "ice_spike.png", "teleport.png", "magic_missile.png",
                "cone_of_cold.png", "arcane_missiles.png", "frost_armor.png",
                "meteor.png", "time_stop.png", "dispel_magic.png",
                
                # Hunter abilities
                "multishot.png", "triple_shot.png", "piercing_shot.png",
                "crippling_shot.png", "ricochet_shot.png", "knockback_shot.png",
                "explosive_shot.png", "hunters_mark.png", "trap.png", "eagle_eye.png",
                
                # Rogue abilities
                "backstab.png", "poison_strike.png", "stealth.png", "fan_of_knives.png",
                "assassination.png", "smoke_bomb.png", "caltrops.png", "shadow_step.png",
                
                # Cleric abilities
                "heal.png", "mass_heal.png", "cleanse.png", "sanctuary.png",
                "divine_protection.png", "holy_smite.png", "turn_undead.png", "blessing.png",
                
                # King abilities
                "royal_decree.png", "divine_shield.png", "tactical_retreat.png",
                "inspire.png", "royal_execution.png",
                
                # Warlock abilities
                "life_drain.png", "curse.png", "fear.png", "dark_pact.png",
                "soul_burn.png", "shadow_bolt.png",
                
                # Paladin abilities
                "lay_on_hands.png", "divine_smite.png", "blessing.png",
                "consecrate.png", "turn_undead.png", "divine_shield.png",
                
                # Druid abilities
                "wild_shape.png", "entangle.png", "healing_spring.png",
                "thorn_barrier.png", "call_lightning.png", "natures_wrath.png",
                
                # Bard abilities
                "inspire.png", "song_of_healing.png", "discordant_note.png",
                "bardic_knowledge.png", "mass_inspiration.png", "shatter.png",
                
                # Generic abilities
                "move.png", "attack.png"
            ],
            "ui": [
                "health.png", "mana.png", "ap.png", "cooldown.png",
                "damage.png", "armor.png", "speed.png", "range.png"
            ]
        }
        
        # Try to load each icon file
        for category, icon_files in icon_categories.items():
            category_dir = self.icons_dir / category
            category_dir.mkdir(exist_ok=True)
            
            for icon_file in icon_files:
                icon_path = category_dir / icon_file
                icon_key = f"{category}_{icon_file.replace('.png', '')}"
                
                if icon_path.exists():
                    try:
                        icon = pygame.image.load(str(icon_path)).convert_alpha()
                        # Scale to standard size
                        icon = pygame.transform.scale(icon, (self.icon_size, self.icon_size))
                        self.icons[icon_key] = icon
                    except pygame.error as e:
                        print(f"Could not load icon {icon_path}: {e}")
    
    def _create_missing_icons(self):
        """Create procedural icons for missing assets"""
        
        # Class icons
        class_colors = {
            "warrior": (150, 75, 0),      # Brown
            "mage": (0, 100, 200),        # Blue
            "hunter": (0, 150, 0),        # Green
            "rogue": (100, 100, 100),     # Gray
            "cleric": (255, 255, 255),    # White
            "king": (255, 215, 0),        # Gold
            "warlock": (80, 0, 80),       # Purple
            "paladin": (255, 215, 0),     # Gold
            "druid": (34, 139, 34),       # Forest Green
            "bard": (255, 165, 0),        # Orange
            "pawn": (139, 69, 19)         # Saddle Brown
        }
        
        for class_name, color in class_colors.items():
            icon_key = f"classes_{class_name}"
            if icon_key not in self.icons:
                self.icons[icon_key] = self._create_class_icon(class_name, color)
        
        # Ability icons
        ability_icons = {
            # Movement and basic
            "move": (100, 100, 100, "→"),
            "attack": (200, 0, 0, "⚔"),
            
            # Warrior
            "shield_bash": (150, 75, 0, "🛡"),
            "charge": (150, 75, 0, "⚡"),
            "taunt": (150, 75, 0, "💢"),
            "whirlwind": (150, 75, 0, "🌪"),
            "berserker_rage": (200, 0, 0, "😡"),
            "defensive_stance": (100, 100, 100, "🛡"),
            "intimidate": (150, 75, 0, "👁"),
            
            # Mage
            "fireball": (255, 100, 0, "🔥"),
            "ice_spike": (100, 200, 255, "❄"),
            "teleport": (150, 0, 255, "✨"),
            "magic_missile": (0, 100, 200, "✦"),
            "cone_of_cold": (100, 200, 255, "❅"),
            "arcane_missiles": (150, 0, 255, "✦"),
            "frost_armor": (100, 200, 255, "🛡"),
            "meteor": (255, 100, 0, "☄"),
            "time_stop": (150, 0, 255, "⏸"),
            "dispel_magic": (255, 255, 255, "✨"),
            
            # Hunter
            "multishot": (0, 150, 0, "🏹"),
            "triple_shot": (0, 150, 0, "🏹"),
            "piercing_shot": (0, 150, 0, "➤"),
            "crippling_shot": (150, 150, 0, "🏹"),
            "ricochet_shot": (0, 150, 0, "↗"),
            "knockback_shot": (0, 150, 0, "⬅"),
            
            # Healing
            "heal": (0, 255, 0, "✚"),
            "mass_heal": (0, 255, 0, "✚"),
            "lay_on_hands": (255, 215, 0, "✚"),
            "song_of_healing": (255, 165, 0, "♪"),
            "healing_spring": (34, 139, 34, "✚"),
            
            # Dark magic
            "life_drain": (80, 0, 80, "💀"),
            "curse": (80, 0, 80, "👁"),
            "fear": (80, 0, 80, "😱"),
            "shadow_bolt": (80, 0, 80, "⚡"),
            "soul_burn": (80, 0, 80, "🔥"),
            
            # Holy magic
            "divine_smite": (255, 215, 0, "⚡"),
            "divine_shield": (255, 215, 0, "🛡"),
            "blessing": (255, 215, 0, "✨"),
            "consecrate": (255, 215, 0, "✨"),
            "turn_undead": (255, 215, 0, "💀"),
            
            # Nature magic
            "wild_shape": (34, 139, 34, "🐻"),
            "entangle": (34, 139, 34, "🌿"),
            "call_lightning": (34, 139, 34, "⚡"),
            "natures_wrath": (34, 139, 34, "🌿"),
            
            # Bard
            "inspire": (255, 165, 0, "♪"),
            "mass_inspiration": (255, 165, 0, "♫"),
            "discordant_note": (255, 165, 0, "♪"),
            "shatter": (255, 165, 0, "💥"),
        }
        
        for ability_name, (color_r, color_g, color_b, symbol) in ability_icons.items():
            icon_key = f"abilities_{ability_name}"
            if icon_key not in self.icons:
                self.icons[icon_key] = self._create_ability_icon((color_r, color_g, color_b), symbol)
    
    def _create_class_icon(self, class_name, color):
        """Create a procedural class icon"""
        icon = pygame.Surface((self.icon_size, self.icon_size), pygame.SRCALPHA)
        center = self.icon_size // 2
        
        # Draw base circle
        pygame.draw.circle(icon, color, (center, center), center - 4)
        pygame.draw.circle(icon, (255, 255, 255), (center, center), center - 4, 3)
        
        # Add class-specific symbols
        font = pygame.font.Font(None, 36)
        
        symbols = {
            "warrior": "⚔", "mage": "✦", "hunter": "🏹", "rogue": "🗡",
            "cleric": "✚", "king": "♔", "warlock": "💀", "paladin": "✨",
            "druid": "🌿", "bard": "♪", "pawn": "♟"
        }
        
        symbol = symbols.get(class_name, "?")
        text_surf = font.render(symbol, True, (255, 255, 255))
        text_rect = text_surf.get_rect(center=(center, center))
        icon.blit(text_surf, text_rect)
        
        return icon
    
    def _create_ability_icon(self, color, symbol):
        """Create a procedural ability icon"""
        icon = pygame.Surface((self.icon_size, self.icon_size), pygame.SRCALPHA)
        center = self.icon_size // 2
        
        # Draw base shape
        pygame.draw.rect(icon, color, (4, 4, self.icon_size - 8, self.icon_size - 8))
        pygame.draw.rect(icon, (255, 255, 255), (4, 4, self.icon_size - 8, self.icon_size - 8), 2)
        
        # Add symbol
        font = pygame.font.Font(None, 32)
        text_surf = font.render(symbol, True, (255, 255, 255))
        text_rect = text_surf.get_rect(center=(center, center))
        icon.blit(text_surf, text_rect)
        
        return icon
    
    def get_class_icon(self, class_name):
        """Get icon for a class"""
        icon_key = f"classes_{class_name.lower()}"
        return self.icons.get(icon_key, self._create_default_icon())
    
    def get_ability_icon(self, ability_name):
        """Get icon for an ability"""
        # Clean ability name
        clean_name = ability_name.lower().replace(" ", "_").replace("'", "")
        icon_key = f"abilities_{clean_name}"
        return self.icons.get(icon_key, self._create_default_icon())
    
    def get_ui_icon(self, ui_element):
        """Get icon for UI element"""
        icon_key = f"ui_{ui_element.lower()}"
        return self.icons.get(icon_key, self._create_default_icon())
    
    def _create_default_icon(self):
        """Create a default icon for missing assets"""
        icon = pygame.Surface((self.icon_size, self.icon_size), pygame.SRCALPHA)
        center = self.icon_size // 2
        
        # Gray square with question mark
        pygame.draw.rect(icon, (100, 100, 100), (4, 4, self.icon_size - 8, self.icon_size - 8))
        pygame.draw.rect(icon, (255, 255, 255), (4, 4, self.icon_size - 8, self.icon_size - 8), 2)
        
        font = pygame.font.Font(None, 32)
        text_surf = font.render("?", True, (255, 255, 255))
        text_rect = text_surf.get_rect(center=(center, center))
        icon.blit(text_surf, text_rect)
        
        return icon
    
    def create_icon_button(self, icon, x, y, size=None):
        """Create a clickable icon button"""
        if size is None:
            size = self.icon_size
        
        # Scale icon if needed
        if icon.get_width() != size:
            icon = pygame.transform.scale(icon, (size, size))
        
        return IconButton(icon, x, y, size)
    
    def set_icon_size(self, size):
        """Set default icon size"""
        self.icon_size = size

class IconButton:
    """Clickable icon button"""
    def __init__(self, icon, x, y, size):
        self.icon = icon
        self.rect = pygame.Rect(x, y, size, size)
        self.hovered = False
        self.pressed = False
    
    def handle_event(self, event):
        """Handle mouse events"""
        if event.type == pygame.MOUSEMOTION:
            self.hovered = self.rect.collidepoint(event.pos)
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if self.rect.collidepoint(event.pos):
                self.pressed = True
                return True
        elif event.type == pygame.MOUSEBUTTONUP:
            self.pressed = False
        
        return False
    
    def render(self, screen):
        """Render the icon button"""
        # Draw background if hovered
        if self.hovered:
            pygame.draw.rect(screen, (100, 100, 100, 50), self.rect)
        
        # Draw icon
        screen.blit(self.icon, self.rect)
        
        # Draw border
        border_color = (255, 255, 255) if self.hovered else (150, 150, 150)
        pygame.draw.rect(screen, border_color, self.rect, 2)

# Global icon manager instance
icon_manager = IconManager()

# Convenience functions
def get_class_icon(class_name):
    """Get icon for a class"""
    return icon_manager.get_class_icon(class_name)

def get_ability_icon(ability_name):
    """Get icon for an ability"""
    return icon_manager.get_ability_icon(ability_name)

def get_ui_icon(ui_element):
    """Get icon for UI element"""
    return icon_manager.get_ui_icon(ui_element)

def create_icon_button(icon, x, y, size=None):
    """Create a clickable icon button"""
    return icon_manager.create_icon_button(icon, x, y, size)

# Icon setup instructions
ICON_SETUP_INSTRUCTIONS = """
🎨 ICON SYSTEM SETUP INSTRUCTIONS

1. CREATE ICON DIRECTORIES:
   Create these folders in your project:
   - assets/icons/classes/
   - assets/icons/abilities/
   - assets/icons/ui/

2. ADD ICON FILES (OPTIONAL):
   Place 64x64 .png files in appropriate directories:
   - classes/warrior.png, classes/mage.png, etc.
   - abilities/fireball.png, abilities/heal.png, etc.
   - ui/health.png, ui/mana.png, etc.

3. INTEGRATE WITH UI:
   Add this to your UI code:
   ```python
   from icon_system import get_class_icon, get_ability_icon
   
   # Get class icon
   warrior_icon = get_class_icon("Warrior")
   screen.blit(warrior_icon, (x, y))
   
   # Get ability icon
   fireball_icon = get_ability_icon("Fireball")
   screen.blit(fireball_icon, (x, y))
   ```

4. CREATE ICON BUTTONS:
   ```python
   from icon_system import create_icon_button, get_ability_icon
   
   icon = get_ability_icon("Fireball")
   button = create_icon_button(icon, x, y, 48)
   
   # In event loop
   if button.handle_event(event):
       # Button was clicked
       use_fireball()
   
   # In render loop
   button.render(screen)
   ```

5. FREE ICON RESOURCES:
   - game-icons.net (free SVG icons)
   - flaticon.com (free icons with attribution)
   - iconfinder.com (free and paid icons)
   - Create your own with GIMP/Photoshop
"""

if __name__ == "__main__":
    print(ICON_SETUP_INSTRUCTIONS)

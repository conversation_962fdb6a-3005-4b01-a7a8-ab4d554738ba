import pygame
from units_core import Unit, SimpleAbility, MoveAbility, AttackAbility
from game_config import GAME_CONFIG
import game_constants as const
import math

class King(Unit):
    """King unit - Objective unit, powerful but vulnerable. Standard chess King movement."""
    def __init__(self, player_id):
        super().__init__(player_id, health=GAME_CONFIG.get("king_config", {}).get("health", 10), max_health=GAME_CONFIG.get("king_config", {}).get("health", 10))
        self.name = "King"
        self.max_ap = GAME_CONFIG.get("king_config", {}).get("max_ap", 10)
        self.current_ap = GAME_CONFIG.get("king_config", {}).get("max_ap", 10)
        self.board = None
        self.image = self._create_placeholder_image((255, 215, 0) if player_id == 1 else (200, 160, 0)) # Gold-like colors
        self.game_ref = None # To store game reference for turn counting

        self.divine_shield_active_until = 0 # Turn number, specific to King's ability

        self.abilities = [
            MoveAbility(self),
            AttackAbility(self),
            SimpleAbility("Royal Decree", GAME_CONFIG.get("king_config", {}).get("royal_decree_ap_cost", 3), "Stun all adjacent enemy units", cooldown=3, owner=self),
            SimpleAbility("Divine Shield", GAME_CONFIG.get("king_config", {}).get("divine_shield_ap_cost", 3), "King takes 50% damage for 1 turn", cooldown=4, owner=self), # Changed from immune
            SimpleAbility("Tactical Retreat", GAME_CONFIG.get("king_config", {}).get("tactical_retreat_ap_cost", 3), "Move to an adjacent allied unit's position", cooldown=2, owner=self),
            SimpleAbility("Inspire", GAME_CONFIG.get("king_config", {}).get("inspire_ap_cost", 4), "Grant +1 AP to all adjacent allied units", cooldown=3, owner=self),
            SimpleAbility("Royal Execution", GAME_CONFIG.get("king_config", {}).get("royal_execution_ap_cost", 5), "Deal 3 damage to an adjacent enemy unit below 30% HP", cooldown=5, owner=self)
        ]

    def _create_placeholder_image(self, color):
        surf = pygame.Surface((const.CELL_SIZE, const.CELL_SIZE), pygame.SRCALPHA) # Use const.CELL_SIZE
        pygame.draw.circle(surf, color, (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 1) # Use const.CELL_SIZE
        pygame.draw.circle(surf, (255, 255, 255), (const.CELL_SIZE//2, const.CELL_SIZE//2), const.CELL_SIZE//2 - 1, 2) # Use const.CELL_SIZE
        # Crown symbol
        crown_color = (255,255,255)
        points = [
            (const.CELL_SIZE//2 - 10, const.CELL_SIZE//2 + 10),
            (const.CELL_SIZE//2 - 10, const.CELL_SIZE//2 - 5),
            (const.CELL_SIZE//2 - 5, const.CELL_SIZE//2 ),
            (const.CELL_SIZE//2, const.CELL_SIZE//2 - 10),
            (const.CELL_SIZE//2 + 5, const.CELL_SIZE//2),
            (const.CELL_SIZE//2 + 10, const.CELL_SIZE//2 - 5),
            (const.CELL_SIZE//2 + 10, const.CELL_SIZE//2 + 10),
        ] # Use const.CELL_SIZE for all points
        pygame.draw.polygon(surf, crown_color, points)
        pygame.draw.lines(surf, (50,50,50), True, points, 2)
        return surf

    def get_valid_moves(self, board):
        """King moves one square in any direction (orthogonal or diagonal)."""
        self.board = board
        if self.has_status('Immobilized') or self.has_status('Stunned'):
            return []
        
        valid_moves = []
        row, col = self.position
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:
                    continue
                new_row, new_col = row + dr, col + dc
                if (0 <= new_row < const.BOARD_SIZE and 0 <= new_col < const.BOARD_SIZE and # Use const.BOARD_SIZE
                        (new_row, new_col) not in board.units):
                    valid_moves.append((new_row, new_col))
        return valid_moves

    def get_valid_attacks(self, board):
        """King attacks one square in any direction (orthogonal or diagonal)."""
        self.board = board
        if self.has_status('Stunned'):
            return []
        valid_attacks = []
        row, col = self.position
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:
                    continue
                r, c = row + dr, col + dc
                if (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and # Use const.BOARD_SIZE
                        (r,c) in board.units and
                        board.units[(r,c)].player_id != self.player_id and
                        not board.units[(r,c)].sanctuary):
                    valid_attacks.append((r,c))
        return valid_attacks

    def get_ability_targets(self, ability_idx, board): # Renamed for consistency
        self.board = board
        if ability_idx == 0: return self.get_valid_moves(board)
        if ability_idx == 1: return self.get_valid_attacks(board)

        ability_name = self.abilities[ability_idx].name
        if ability_name == "Royal Decree": # Targets self, affects adjacent
            return [self.position]
        elif ability_name == "Divine Shield": # Targets self
            return [self.position]
        elif ability_name == "Tactical Retreat":
            return self._get_tactical_retreat_targets(board)
        elif ability_name == "Inspire": # Targets self, affects adjacent allies
            return [self.position]
        elif ability_name == "Royal Execution":
            return self._get_royal_execution_targets(board)
        return []

    def _get_tactical_retreat_targets(self, board):
        targets = []
        row, col = self.position
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0: continue
                adj_r, adj_c = row + dr, col + dc
                if (0 <= adj_r < const.BOARD_SIZE and 0 <= adj_c < const.BOARD_SIZE and # Use const.BOARD_SIZE
                        (adj_r, adj_c) in board.units and
                        board.units[(adj_r, adj_c)].player_id == self.player_id):
                    # Can retreat to an adjacent ALLIED unit's position
                    # The King swaps with this unit (or just moves there if simpler)
                    # For targeting, the allied unit's tile is the target.
                    targets.append((adj_r, adj_c)) 
        return targets

    def _get_royal_execution_targets(self, board):
        targets = []
        for r_pos in self.get_valid_attacks(board): # Must be a valid attack target first
            unit = board.units.get(r_pos)
            if unit and unit.health < unit.max_health * 0.3: # Below 30% HP
                targets.append(r_pos)
        return targets

    def use_ability(self, ability_idx, target_pos, game=None):
        # Ensure self.board is set if game object is provided
        if game:
            self.board = game.board
            self.game_ref = game # Store game reference
        elif not self.board:
            print(f"ERROR in {self.name}.use_ability: game object not passed and self.board not set.")
            if ability_idx > 1: # Special abilities likely need board
                print(f"Cannot use special ability {self.abilities[ability_idx].name} without board context.")
                return False

        # Standard move or attack - call super() to use the updated base class logic
        if ability_idx == 0: # Move
            return super().use_ability(ability_idx, target_pos, game)
        
        if ability_idx == 1: # Basic Attack
            return super().use_ability(ability_idx, target_pos, game)
        
        # For other King-specific abilities:
        if not self.can_use_ability(ability_idx): # Checks AP, cooldown, stunned
            return False

        ability = self.abilities[ability_idx]
        ap_cost = ability.ap_cost + (1 if self.has_status('Chilled') else 0)
        self.current_ap -= ap_cost
        ability.cooldown_remaining = ability.cooldown
        print(f"{self.name} uses {ability.name} on {target_pos if target_pos != self.position or ability.name == 'Tactical Retreat' else 'self/adjacent'}")

        # Ensure board is available for special abilities that need it
        if not self.board and ability.name not in ["Divine Shield"]:
            print(f"Error: {self.name} cannot use {ability.name} as self.board is not set.")
            self.current_ap += ap_cost # Refund AP
            ability.cooldown_remaining = 0 # Reset cooldown
            return False

        if ability.name == "Royal Decree":
            return self._use_royal_decree(game)
        elif ability.name == "Divine Shield":
            return self._use_divine_shield(game)
        elif ability.name == "Tactical Retreat":
            return self._use_tactical_retreat(target_pos)
        elif ability.name == "Inspire":
            return self._use_inspire()
        elif ability.name == "Royal Execution":
            return self._use_royal_execution(target_pos)
        return False

    def _use_royal_decree(self, game):
        stunned_count = 0
        row, col = self.position
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0: continue
                r, c = row + dr, col + dc
                if (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and (r,c) in self.board.units and
                        self.board.units[(r,c)].player_id != self.player_id):
                    enemy_unit = self.board.units[(r,c)]
                    enemy_unit.stunned = True
                    enemy_unit.stunned_until = game.turn_count + 1
                    print(f"Royal Decree stuns {enemy_unit.name} at {(r,c)} until turn {enemy_unit.stunned_until}")
                    stunned_count += 1
        return stunned_count > 0

    def _use_divine_shield(self, game):
        self.divine_shield_active_until = game.turn_count + 1
        self.divine_protection = True
        print(f"{self.name} activates Divine Shield. Immune to damage until start of its next turn (game turn {self.divine_shield_active_until+1}).")
        return True

    def _use_tactical_retreat(self, target_ally_pos):
        ally_unit = self.board.units.get(target_ally_pos)
        if ally_unit and ally_unit.player_id == self.player_id and ally_unit != self:
            current_king_pos = self.position
            
            # Swap positions
            print(f"{self.name} retreats, swapping with {ally_unit.name} at {target_ally_pos}")
            self.board.units[current_king_pos] = ally_unit
            self.board.units[target_ally_pos] = self
            
            ally_unit.position = current_king_pos
            self.position = target_ally_pos
            return True
        print(f"Tactical Retreat to {target_ally_pos} failed. No valid ally or target is self.")
        return False

    def _use_inspire(self):
        inspired_count = 0
        row, col = self.position
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0: continue
                r, c = row + dr, col + dc
                if (0 <= r < const.BOARD_SIZE and 0 <= c < const.BOARD_SIZE and (r,c) in self.board.units and
                        self.board.units[(r,c)].player_id == self.player_id and self.board.units[(r,c)] != self):
                    ally_unit = self.board.units[(r,c)]
                    ally_unit.current_ap = min(ally_unit.max_ap, ally_unit.current_ap + 1)
                    print(f"Inspire grants +1 AP to {ally_unit.name} at {(r,c)}. Now has {ally_unit.current_ap} AP.")
                    inspired_count += 1
        return inspired_count > 0

    def _use_royal_execution(self, target_pos):
        target_unit = self.board.units.get(target_pos)
        if target_unit and target_unit.player_id != self.player_id and target_unit.health < target_unit.max_health * 0.3:
            damage = 3 # Fixed damage for execution
            print(f"Royal Execution on {target_unit.name} for {damage} damage!")
            target_unit.take_damage(damage, self)
            return True
        print(f"Royal Execution failed. Target not valid or not below 30% HP.")
        return False

    def take_damage(self, amount, attacker=None, game=None):
        if game and self.divine_shield_active_until > game.turn_count:
            print(f"{self.name}'s Divine Shield negates all damage!")
            return self.health # No damage taken
        return super().take_damage(amount, attacker, game=game)

    def reset_turn(self, game=None):
        super().reset_turn(game=game)
        if game and self.divine_shield_active_until <= game.turn_count:
            if self.divine_protection: # Check if it was active from Divine Shield
                self.divine_protection = False # Turn off the generic flag too
                print(f"{self.name}'s Divine Shield has worn off.")
            self.divine_shield_active_until = 0

    def __str__(self):
        base = super().__str__()
        if hasattr(self, 'game_ref') and self.game_ref and self.divine_shield_active_until > self.game_ref.turn_count:
            base += " (Divine Shield ACTIVE)"
        return base 
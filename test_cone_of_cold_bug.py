#!/usr/bin/env python3
"""
Test to demonstrate Cone of Cold bug where hover highlighting 
and actual execution show different patterns
"""

import pygame
from game_state import Game
from units.mage import Mage
from units.warrior import Warrior

def test_cone_of_cold_bug():
    """Test Cone of Cold near board edges to find the bug"""
    pygame.init()
    
    print("🐛 TESTING CONE OF COLD BUG 🐛")
    print("=" * 35)
    
    # Test near board edge where T-pattern might go off-board
    print("📋 TEST: Cone of Cold Near Board Edge")
    print("-" * 38)
    
    game = Game()
    mage = Mage(1)
    
    # Create targets in T-pattern positions for North direction
    # Position mage near top edge so T-pattern goes off board
    target1 = Warrior(2)  # 1 tile north
    target2 = Warrior(2)  # 2 tiles north (center of T) - might be off board
    target3 = Warrior(2)  # 2 tiles north, 1 west (left of T) - might be off board
    target4 = Warrior(2)  # 2 tiles north, 1 east (right of T) - might be off board
    
    # Position mage near edge
    mage.position = (2, 4)  # Close to top edge
    target1.position = (1, 4)  # North (1 tile) - on board
    target2.position = (0, 4)  # North (2 tiles, center) - on board edge
    target3.position = (0, 3)  # North (2 tiles, left) - on board edge
    target4.position = (0, 5)  # North (2 tiles, right) - on board edge
    
    # Set up board
    mage.board = game.board
    game.board.units = {
        (2, 4): mage,
        (1, 4): target1,
        (0, 4): target2,
        (0, 3): target3,
        (0, 5): target4
    }
    
    print(f"Setup (Near edge T-pattern):")
    print(f"  Mage at {mage.position}")
    print(f"  Target 1 at {target1.position} (1 tile north)")
    print(f"  Target 2 at {target2.position} (2 tiles north, center)")
    print(f"  Target 3 at {target3.position} (2 tiles north, left)")
    print(f"  Target 4 at {target4.position} (2 tiles north, right)")
    
    # Find Cone of Cold ability
    cone_ability_idx = None
    for i, ability in enumerate(mage.abilities):
        if ability.name == "Cone of Cold":
            cone_ability_idx = i
            break
    
    # Record original HP
    original_hp = {
        "target1": target1.health,
        "target2": target2.health,
        "target3": target3.health,
        "target4": target4.health
    }
    
    # Use Cone of Cold targeting North
    north_target = (1, 4)  # 1 tile north
    print(f"\n❄️ Using Cone of Cold targeting North...")
    result = mage.use_ability(cone_ability_idx, north_target, game)
    
    print(f"\nResults:")
    print(f"  Ability success: {result}")
    print(f"  Target 1 HP: {original_hp['target1']} → {target1.health}")
    print(f"  Target 2 HP: {original_hp['target2']} → {target2.health}")
    print(f"  Target 3 HP: {original_hp['target3']} → {target3.health}")
    print(f"  Target 4 HP: {original_hp['target4']} → {target4.health}")
    
    # Check if all 4 targets were hit
    targets_hit = 0
    if target1.health < original_hp["target1"]:
        targets_hit += 1
        print(f"  ✅ Target 1 hit")
    else:
        print(f"  ❌ Target 1 missed")
        
    if target2.health < original_hp["target2"]:
        targets_hit += 1
        print(f"  ✅ Target 2 hit")
    else:
        print(f"  ❌ Target 2 missed")
        
    if target3.health < original_hp["target3"]:
        targets_hit += 1
        print(f"  ✅ Target 3 hit")
    else:
        print(f"  ❌ Target 3 missed")
        
    if target4.health < original_hp["target4"]:
        targets_hit += 1
        print(f"  ✅ Target 4 hit")
    else:
        print(f"  ❌ Target 4 missed")
    
    if targets_hit == 4:
        print(f"\n✅ Perfect T-pattern: All 4 targets hit!")
    else:
        print(f"\n❌ T-pattern incomplete: Only {targets_hit}/4 targets hit")
        print(f"   This suggests the hover highlighting and execution differ!")
    
    # Test 2: Another edge case
    print(f"\n📋 TEST: Cone of Cold Going Off Board")
    print("-" * 36)
    
    game2 = Game()
    mage2 = Mage(1)
    target_edge = Warrior(2)
    
    # Position mage so T-pattern goes completely off board
    mage2.position = (1, 4)  # Very close to top edge
    target_edge.position = (0, 4)  # 1 tile north (on edge)
    
    # Set up board
    mage2.board = game2.board
    game2.board.units = {
        (1, 4): mage2,
        (0, 4): target_edge
    }
    
    print(f"Setup (T-pattern goes off board):")
    print(f"  Mage at {mage2.position}")
    print(f"  Target at {target_edge.position} (1 tile north)")
    print(f"  T-pattern center would be at (-1, 4) - OFF BOARD")
    
    # Record original HP
    original_edge_hp = target_edge.health
    
    # Use Cone of Cold targeting North
    north_target2 = (0, 4)  # 1 tile north
    print(f"\n❄️ Using Cone of Cold targeting North (off-board T)...")
    result2 = mage2.use_ability(cone_ability_idx, north_target2, game2)
    
    print(f"\nResults:")
    print(f"  Ability success: {result2}")
    print(f"  Target HP: {original_edge_hp} → {target_edge.health}")
    
    if target_edge.health < original_edge_hp:
        print(f"  ✅ Target hit (first tile of T-pattern)")
    else:
        print(f"  ❌ Target missed - BUG DETECTED!")
    
    print(f"\n" + "=" * 35)
    print("🐛 BUG ANALYSIS")
    print("-" * 15)
    print("The issue is likely that hover highlighting")
    print("and execution use different logic for")
    print("handling off-board tiles in T-pattern.")
    print("\nSolution: Make both systems identical!")

if __name__ == "__main__":
    test_cone_of_cold_bug()
